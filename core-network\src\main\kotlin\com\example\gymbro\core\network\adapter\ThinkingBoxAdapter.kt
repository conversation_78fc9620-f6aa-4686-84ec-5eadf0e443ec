package com.example.gymbro.core.network.adapter

import com.example.gymbro.core.network.detector.ContentType
import com.example.gymbro.core.network.output.DirectOutputChannel
import com.example.gymbro.core.network.output.OutputToken
import com.example.gymbro.core.network.receiver.UnifiedTokenReceiver
import com.example.gymbro.core.network.receiver.HttpSseTokenSource
// TokenRouter已删除，新架构使用DirectOutputChannel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 ThinkingBox适配器 - 新旧架构桥接组件
 *
 * 设计目标：
 * - 桥接新的UnifiedTokenReceiver与现有ThinkingBox架构
 * - 保持向后兼容性，不破坏现有功能
 * - 逐步迁移到新架构，支持A/B测试
 * - 提供性能对比和监控
 */
@Singleton
class ThinkingBoxAdapter @Inject constructor(
    private val unifiedTokenReceiver: UnifiedTokenReceiver,
    private val directOutputChannel: DirectOutputChannel
) {
    companion object {
        private const val TAG = "ThinkingBoxAdapter"
    }

    /**
     * 适配新架构到现有ThinkingBox流程
     *
     * @param tokenFlow 原始token流
     * @param messageId 消息ID
     * @param scope 协程作用域
     */
    fun adaptToThinkingBox(
        tokenFlow: Flow<String>,
        messageId: String,
        scope: CoroutineScope
    ) {
        scope.launch {
            try {
                Timber.tag(TAG).i("🔄 开始适配新架构到ThinkingBox: messageId=$messageId")

                // 创建token源
                val tokenSource = HttpSseTokenSource(tokenFlow)

                // 使用新的UnifiedTokenReceiver处理
                unifiedTokenReceiver.receiveTokenStream(tokenSource, messageId)
                    .collect { processedToken ->
                        // 直接发送到DirectOutputChannel（新架构唯一输出）
                        directOutputChannel.sendToken(
                            token = processedToken,
                            conversationId = messageId,
                            contentType = ContentType.XML_THINKING, // 默认类型
                            metadata = mapOf("adapter" to "ThinkingBoxAdapter")
                        )
                    }

                Timber.tag(TAG).i("✅ ThinkingBox适配完成: messageId=$messageId")

            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "❌ ThinkingBox适配失败: messageId=$messageId")
                throw e
            }
        }
    }

    /**
     * 直接使用新架构处理token流
     *
     * @param tokenFlow 原始token流
     * @param messageId 消息ID
     * @return 处理后的token流
     */
    suspend fun processWithNewArchitecture(
        tokenFlow: Flow<String>,
        messageId: String
    ): Flow<OutputToken> {
        Timber.tag(TAG).i("🚀 使用新架构直接处理: messageId=$messageId")

        // 创建token源
        val tokenSource = HttpSseTokenSource(tokenFlow)

        // 启动处理流程
        val scope = kotlinx.coroutines.GlobalScope
        scope.launch {
            unifiedTokenReceiver.receiveTokenStream(tokenSource, messageId)
                .collect { processedToken ->
                    directOutputChannel.sendToken(
                        token = processedToken,
                        conversationId = messageId,
                        contentType = ContentType.XML_THINKING,
                        metadata = mapOf("direct" to true)
                    )
                }
        }

        // 返回输出流
        return directOutputChannel.subscribeToConversation(messageId)
    }

    /**
     * 获取适配器状态信息
     */
    fun getAdapterStatus(): AdapterStatus {
        val receiverStatus = unifiedTokenReceiver.getReceiverStatus()
        val channelStatus = directOutputChannel.getChannelStatus()

        return AdapterStatus(
            totalTokensReceived = receiverStatus.totalTokensReceived,
            totalConversations = receiverStatus.totalConversations,
            totalTokensOutput = channelStatus.totalTokensOutput,
            currentBufferSize = receiverStatus.currentBufferSize,
            detectedType = receiverStatus.detectedType
        )
    }
}

/**
 * 📊 适配器状态信息
 */
data class AdapterStatus(
    val totalTokensReceived: Long,
    val totalConversations: Long,
    val totalTokensOutput: Long,
    val currentBufferSize: Int,
    val detectedType: ContentType?
)
