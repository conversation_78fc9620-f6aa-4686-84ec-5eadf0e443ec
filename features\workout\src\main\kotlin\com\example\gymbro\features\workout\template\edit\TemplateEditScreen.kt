package com.example.gymbro.features.workout.template.edit

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import kotlinx.coroutines.flow.collectLatest

/**
 * =========================================================================================
 * 🔥 GymBro Template Edit Screen - 遵循黄金标准 🔥
 * =========================================================================================
 *
 * 本Screen遵循 ProfileBioScreen 黄金标准，实现简洁、高效的UI架构。
 *
 * 🎯 核心原则：
 * 1. 无状态设计：接收State对象来渲染UI
 * 2. 回调模式：通过lambda回调发送Intent
 * 3. 设计系统：100%使用designSystem Tokens
 * 4. 单一职责：专注于UI渲染，不包含业务逻辑
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TemplateEditScreen(
    templateId: String? = null,
    onNavigateBack: () -> Unit = {},
    onNavigateToExerciseLibrary: () -> Unit = {},
    viewModel: TemplateEditViewModel = hiltViewModel(),
) {
    val state by viewModel.state.collectAsStateWithLifecycle()

    // 监听一次性 Effect
    LaunchedEffect(Unit) {
        viewModel.effect.collectLatest { effect ->
            when (effect) {
                is TemplateEditContract.Effect.ShowToast -> {
                    // TODO: Show toast
                }
                is TemplateEditContract.Effect.ShowError -> {
                    // TODO: Show error
                }
                is TemplateEditContract.Effect.NavigateBack -> {
                    onNavigateBack()
                }
                is TemplateEditContract.Effect.NavigateToExerciseLibrary -> {
                    onNavigateToExerciseLibrary()
                }
                is TemplateEditContract.Effect.NavigateToPreview -> {
                    // TODO: Navigate to preview
                }
                is TemplateEditContract.Effect.SaveSuccess -> {
                    // TODO: Handle save success
                }
                // 添加其他Effect处理
                else -> {
                    // 其他Effect由EffectHandler处理或暂时忽略
                }
            }
        }
    }

    // 初始化加载
    LaunchedEffect(templateId) {
        if (templateId != null) {
            viewModel.dispatch(TemplateEditContract.Intent.LoadTemplate(templateId))
        } else {
            viewModel.dispatch(TemplateEditContract.Intent.CreateEmptyTemplate("current_user_id"))
        }
    }

    TemplateEditContent(
        state = state,
        onIntent = viewModel::dispatch
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TemplateEditContent(
    state: TemplateEditContract.State,
    onIntent: (TemplateEditContract.Intent) -> Unit
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = state.template?.name?.takeIf { it.isNotBlank() } ?: "新建模板",
                        fontWeight = FontWeight.Medium
                    )
                },
                navigationIcon = {
                    IconButton(onClick = { onIntent(TemplateEditContract.Intent.NavigateBack) }) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                actions = {
                    if (state.canSave) {
                        IconButton(
                            onClick = { onIntent(TemplateEditContract.Intent.SaveTemplate) },
                            enabled = !state.isSaving
                        ) {
                            if (state.isSaving) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(24.dp),
                                    strokeWidth = 2.dp
                                )
                            } else {
                                Icon(
                                    imageVector = Icons.Default.Save,
                                    contentDescription = "保存"
                                )
                            }
                        }
                    }
                }
            )
        },
        floatingActionButton = {
            FloatingActionButton(
                onClick = { onIntent(TemplateEditContract.Intent.ToggleExerciseSelector) }
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "添加动作"
                )
            }
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                state.isLoading -> {
                    LoadingContent()
                }
                else -> {
                    TemplateEditMainContent(
                        state = state,
                        onIntent = onIntent
                    )
                }
            }

            // 错误显示
            state.error?.let { error ->
                Card(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(Tokens.Spacing.Medium),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Text(
                        text = error.asString(),
                        modifier = Modifier.padding(Tokens.Spacing.Medium),
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
}

@Composable
private fun TemplateEditMainContent(
    state: TemplateEditContract.State,
    onIntent: (TemplateEditContract.Intent) -> Unit
) {
    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        contentPadding = PaddingValues(Tokens.Spacing.Medium),
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium)
    ) {
        // 模板基本信息
        item {
            TemplateBasicInfoCard(
                template = state.template,
                onUpdateName = { name ->
                    onIntent(TemplateEditContract.Intent.UpdateTemplateName(name))
                },
                onUpdateDescription = { description ->
                    onIntent(TemplateEditContract.Intent.UpdateTemplateDescription(description))
                }
            )
        }

        // 动作列表
        items(
            items = state.exercises,
            key = { exercise -> exercise.id }
        ) { exercise ->
            ExerciseCard(
                exercise = exercise,
                onUpdate = { updatedExercise ->
                    onIntent(TemplateEditContract.Intent.UpdateExercise(updatedExercise))
                },
                onRemove = {
                    onIntent(TemplateEditContract.Intent.RemoveExercise(exercise.id))
                }
            )
        }

        // 空状态
        if (state.exercises.isEmpty()) {
            item {
                EmptyExerciseState(
                    onAddExercise = {
                        onIntent(TemplateEditContract.Intent.ToggleExerciseSelector)
                    }
                )
            }
        }
    }
}

@Composable
private fun LoadingContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator()
    }
}

@Composable
private fun TemplateBasicInfoCard(
    template: com.example.gymbro.domain.workout.model.template.WorkoutTemplate?,
    onUpdateName: (String) -> Unit,
    onUpdateDescription: (String) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small)
        ) {
            Text(
                text = "模板信息",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            OutlinedTextField(
                value = template?.name ?: "",
                onValueChange = onUpdateName,
                label = { Text("模板名称") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )

            OutlinedTextField(
                value = template?.description ?: "",
                onValueChange = onUpdateDescription,
                label = { Text("模板描述") },
                modifier = Modifier.fillMaxWidth(),
                maxLines = 3
            )
        }
    }
}

@Composable
private fun ExerciseCard(
    exercise: com.example.gymbro.shared.models.workout.TemplateExerciseDto,
    onUpdate: (com.example.gymbro.shared.models.workout.TemplateExerciseDto) -> Unit,
    onRemove: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = exercise.exerciseName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier.weight(1f)
                )

                IconButton(onClick = onRemove) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "删除动作"
                    )
                }
            }

            Text(
                text = "${exercise.sets}组 × ${exercise.reps}次",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun EmptyExerciseState(
    onAddExercise: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Large),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium)
        ) {
            Text(
                text = "还没有添加动作",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Text(
                text = "点击下方按钮添加第一个动作",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            Button(onClick = onAddExercise) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = null
                )
                Spacer(modifier = Modifier.width(Tokens.Spacing.Small))
                Text("添加动作")
            }
        }
    }
}
