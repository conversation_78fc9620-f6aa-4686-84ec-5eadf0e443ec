14:18:22.676 COACH-MESSAGE-FLOW       I  🚀 [UI发送] 用户点击发送按钮，消息内容: 我想开始力量训练，我应该从哪里开始？
14:18:22.677                          I  🚀 [UI发送] 发送状态检查: canSendMessage=true, isLoading=false
14:18:22.701 MESSAGE-SAVE             I  🎯 [AiCoachEffectHandler] 路由SaveUserMessage到AiCoachSessionHandler: messageId=684a1632-dec2-47a3-9e89-2d41af376e89, sessionId=e5f65e60-0de8-40fc-9736-b72d76d3cfda
14:18:22.703 COACH-MESSAGE-FLOW       I  🚀 [EFFECT处理] StartAiStream Effect收到: sessionId=e5f65e60-0de8-40fc-9736-b72d76d3cfda, userMessageId=684a1632-dec2-47a3-9e89-2d41af376e89, aiResponseId=ae9f981b-5a6b-4b15-baef-af7ca1dadf92
14:18:22.715 BgeMemoryMonitor         W  ⚠️ BGE可用内存偏低: 18MB < 120MB (警戒线)
14:18:22.721 tflite                   I  Replacing 200 out of 391 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 76 partitions for subgraph 0.
14:18:22.776 TB-COACH                 I  🔍 AwaitingFirstToken状态: 实际AI消息ID=ae9f981b-5a6b-4b15-baef-af7ca1dadf92
14:18:22.817 TB-VIEWMODEL             D  🚀 ThinkingBoxViewModel 初始化
14:18:22.828 .example.gymbro          I  Compiler allocated 7381KB to compile void androidx.compose.material3.internal.TextFieldImplKt.CommonDecorationBox(androidx.compose.material3.internal.TextFieldType, java.lang.String, kotlin.jvm.functions.Function2, androidx.compose.ui.text.input.VisualTransformation, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, boolean, boolean, boolean, androidx.compose.foundation.interaction.InteractionSource, androidx.compose.foundation.layout.PaddingValues, androidx.compose.material3.TextFieldColors, kotlin.jvm.functions.Function2, androidx.compose.runtime.Composer, int, int, int)
14:18:22.925 TB-COACH                 I  🚀 ThinkingBox激活: messageId=ae9f981b-5a6b-4b15-baef-af7ca1dadf92, state=AwaitingFirstToken
14:18:22.925 TB-INIT                  D  🚀 初始化ThinkingBox: ae9f981b-5a6b-4b15-baef-af7ca1dadf92, hasTokenFlow=false
14:18:22.925 TB-VIEWMODEL             I  🚀 初始化ThinkingBox: messageId=ae9f981b-5a6b-4b15-baef-af7ca1dadf92
14:18:22.925                          I  🎯 [Token流启动] messageId=ae9f981b-5a6b-4b15-baef-af7ca1dadf92
14:18:22.926                          I  ✅ [开始处理消息] messageId=ae9f981b-5a6b-4b15-baef-af7ca1dadf92
14:18:22.927 TB-INIT                  D  🔗 连接HistoryActor到Effect流: ae9f981b-5a6b-4b15-baef-af7ca1dadf92
14:18:22.934 HISTORY-ACTOR            I  🚀 [初始化] 开始监听ThinkingBox Effect流
14:18:22.988 TB-COACH                 I  🔍 AwaitingFirstToken状态: 实际AI消息ID=ae9f981b-5a6b-4b15-baef-af7ca1dadf92
14:18:23.210 tflite                   I  Replacing 200 out of 391 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 76 partitions for subgraph 0.
14:18:23.393 NEW-ARCH                 I  🧹 启动新架构token流路径：messageId=ae9f981b-5a6b-4b15-baef-af7ca1dadf92
14:18:23.402                          I  🚀 启动新架构AI请求: messageId=ae9f981b-5a6b-4b15-baef-af7ca1dadf92, taskType=CHAT
14:18:23.405                          I  ✅ AI请求已启动，token流将通过新架构自动路由到ThinkingBox: messageId=ae9f981b-5a6b-4b15-baef-af7ca1dadf92
14:18:23.405                          I  🚀 新架构token流路径启动完成
14:18:25.041 RetryInterceptor         W  🔄 HTTP 503 错误，第1次重试: https://tbai.xin/v1/chat/completions
14:18:27.330                          W  🔄 HTTP 503 错误，第2次重试: https://tbai.xin/v1/chat/completions
14:18:32.168 RestClientImpl$post      E  🚨 POST请求失败: HTTP 503:
14:18:32.169 SafeApiCallKt            W  🚨 网络IO异常
                                         java.io.IOException: HTTP 503:
                                         	at com.example.gymbro.core.network.rest.RestClientImpl$post$2.invokeSuspend(RestClientImpl.kt:195)
                                         	at com.example.gymbro.core.network.rest.RestClientImpl$post$2.invoke(Unknown Source:8)
                                         	at com.example.gymbro.core.network.rest.RestClientImpl$post$2.invoke(Unknown Source:2)
                                         	at com.example.gymbro.core.network.rest.SafeApiCallKt.safeApiCallString(SafeApiCall.kt:106)
                                         	at com.example.gymbro.core.network.rest.RestClientImpl.post(RestClientImpl.kt:179)
                                         	at com.example.gymbro.data.coach.service.AiResponseReceiver.sendStreamingAiRequest(AiResponseReceiver.kt:352)
                                         	at com.example.gymbro.data.coach.service.AiResponseReceiver.access$sendStreamingAiRequest(AiResponseReceiver.kt:34)
                                         	at com.example.gymbro.data.coach.service.AiResponseReceiver$streamChatWithMessageId$2$1$1.invokeSuspend(AiResponseReceiver.kt:291)
                                         	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                         	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
                                         	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:113)
                                         	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
                                         	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:586)
                                         	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:820)
                                         	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:717)
                                         	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)
14:18:32.171 NEW-ARCH                 E  ❌ 真实AI请求发送失败: messageId=ae9f981b-5a6b-4b15-baef-af7ca1dadf92
                                         java.lang.Exception: AI API请求失败: Network(throwable=java.io.IOException: HTTP 503: )
                                         	at com.example.gymbro.data.coach.service.AiResponseReceiver.sendStreamingAiRequest(AiResponseReceiver.kt:376)
                                         	at com.example.gymbro.data.coach.service.AiResponseReceiver.access$sendStreamingAiRequest(AiResponseReceiver.kt:34)
                                         	at com.example.gymbro.data.coach.service.AiResponseReceiver$streamChatWithMessageId$2$1$1.invokeSuspend(AiResponseReceiver.kt:291)
                                         	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                         	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
                                         	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:113)
                                         	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
                                         	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:586)
                                         	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:820)
                                         	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:717)
                                         	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)
14:18:32.173                          E  ❌ AI请求启动失败: messageId=ae9f981b-5a6b-4b15-baef-af7ca1dadf92
                                         java.lang.Exception: AI API请求失败: Network(throwable=java.io.IOException: HTTP 503: )
                                         	at com.example.gymbro.data.coach.service.AiResponseReceiver.sendStreamingAiRequest(AiResponseReceiver.kt:376)
                                         	at com.example.gymbro.data.coach.service.AiResponseReceiver.access$sendStreamingAiRequest(AiResponseReceiver.kt:34)
                                         	at com.example.gymbro.data.coach.service.AiResponseReceiver$streamChatWithMessageId$2$1$1.invokeSuspend(AiResponseReceiver.kt:291)
                                         	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                         	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:100)
                                         	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:113)
                                         	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:89)
                                         	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:586)
                                         	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:820)
                                         	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:717)
                                         	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)
14:18:32.182 TB-VIEWMODEL             D  📥 [新架构Token] messageId=ae9f981b-5a6b-4b15-baef-af7ca1dadf92, token='AI请求发送失败: AI API请求失败: Network(throwable=java.io.IO...', type=PLAIN_TEXT
14:18:32.184 TB-PARSER                I  🚀 [解析启动] 开始解析 token 流: ae9f981b-5a6b-4b15-baef-af7ca1dadf92
14:18:32.188 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
14:18:32.189                          E  🔍 [Token输出] [0] Text(content=AI请求发送失败: AI API请求失败: Network(throwable=java.io.IOException: HTTP 503: ))
14:18:32.190 TB-MAPPER                I  📝 [finalmermaid规范] PRE_THINK状态，自动创建perthink段
14:18:32.192 TB-REDUCER               D  🔄 处理事件: SegmentStarted
14:18:32.192                          D  🎯 创建段: perthink (PERTHINK)
14:18:32.194                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:18:32.195                          D  🔄 处理事件: SegmentText
14:18:32.195                          D  📝 追加文本到段[perthink]: AI请求发送失败: AI API请求失败: Network(...
14:18:32.195                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:18:32.196 TB-VIEWMODEL             D  📥 [新架构Token] messageId=ae9f981b-5a6b-4b15-baef-af7ca1dadf92, token='AI请求失败: AI API请求失败: Network(throwable=java.io.IOEx...', type=PLAIN_TEXT
14:18:32.197 TB-PARSER                I  🚀 [解析启动] 开始解析 token 流: ae9f981b-5a6b-4b15-baef-af7ca1dadf92
14:18:32.197 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
14:18:32.197                          E  🔍 [Token输出] [0] Text(content=AI请求失败: AI API请求失败: Network(throwable=java.io.IOException: HTTP 503: ))
14:18:32.197 TB-MAPPER                D  📝 段[perthink]文本: AI请求失败: AI API请求失败: Network(throwable=java.io.IOEx...
14:18:32.197 TB-REDUCER               D  🔄 处理事件: SegmentText
14:18:32.198                          D  📝 追加文本到段[perthink]: AI请求失败: AI API请求失败: Network(th...
14:18:32.198                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
14:18:46.084 .example.gymbro          I  Background concurrent mark compact GC freed 19MB AllocSpace bytes, 19(484KB) LOS objects, 20% free, 94MB/118MB, paused 2.422ms,3.344ms total 335.946ms
14:19:20.505                          I  Background concurrent mark compact GC freed 23MB AllocSpace bytes, 0(0B) LOS objects, 20% free, 94MB/118MB, paused 1.529ms,3.811ms total 334.304ms
