package com.example.gymbro.core.network.processor

import com.example.gymbro.core.network.detector.ContentType
import kotlinx.serialization.json.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 流式处理器实现 - 新架构核心组件
 *
 * 设计目标：
 * - 即时处理，零缓冲延迟
 * - 根据内容类型选择最优处理策略
 * - 直接输出给ThinkingBox，无中间转换
 * - 处理时间<2ms per token
 */
@Singleton
class StreamingProcessorImpl @Inject constructor(
    private val contentExtractor: ContentExtractor,
    private val outputSanitizer: OutputSanitizer
) : StreamingProcessor {

    companion object {
        private const val TAG = "StreamingProcessor"
        private const val MAX_PROCESSING_TIME_MS = 5L // 最大处理时间警告阈值
    }

    override fun processImmediate(
        token: String,
        contentType: ContentType,
        conversationId: String
    ): String {
        val startTime = System.currentTimeMillis()

        val result = try {
            when (contentType) {
                ContentType.JSON_SSE -> processJsonSse(token, conversationId)
                ContentType.JSON_STREAM -> processJsonStream(token, conversationId)
                ContentType.XML_THINKING -> processXmlThinking(token, conversationId)
                ContentType.WEBSOCKET_FRAME -> processWebSocketFrame(token, conversationId)
                ContentType.PLAIN_TEXT -> processPlainText(token, conversationId)
            }
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "处理失败: type=$contentType, conversationId=$conversationId")
            "" // 返回空字符串，跳过这个token
        }

        val processingTime = System.currentTimeMillis() - startTime
        if (processingTime > MAX_PROCESSING_TIME_MS) {
            Timber.tag(TAG).w("⚠️ 处理耗时过长: ${processingTime}ms, type=$contentType")
        }

        return result
    }

    /**
     * 处理JSON SSE格式（OpenAI/Claude风格）
     *
     * 示例输入：
     * data: {"choices":[{"delta":{"content":"Hello"}}]}
     * data: {"choices":[{"delta":{"content":" World"}}]}
     */
    private fun processJsonSse(token: String, conversationId: String): String {
        return try {
            // 即时提取JSON内容，无缓冲
            val content = contentExtractor.extractJsonSseContent(token)

            if (content.isNotEmpty()) {
                // 直接输出给ThinkingBox，移除XML转义（让ThinkingBox自己处理）
                outputSanitizer.sanitizeForDirectOutput(content)
            } else {
                ""
            }
        } catch (e: Exception) {
            Timber.tag(TAG).v(e, "JSON SSE处理跳过: $token")
            ""
        }
    }

    /**
     * 处理纯JSON流格式
     *
     * 示例输入：
     * {"text": "Hello", "type": "content"}
     * {"text": " World", "type": "content"}
     */
    private fun processJsonStream(token: String, conversationId: String): String {
        return try {
            val content = contentExtractor.extractJsonStreamContent(token)
            outputSanitizer.sanitizeForDirectOutput(content)
        } catch (e: Exception) {
            Timber.tag(TAG).v(e, "JSON流处理跳过: $token")
            ""
        }
    }

    /**
     * 处理ThinkingBox XML格式
     *
     * 🔥 关键优化：直接输出给ThinkingBox，移除中间XML处理
     * 让ThinkingBox自己解析XML，减少延迟
     */
    private fun processXmlThinking(token: String, conversationId: String): String {
        // 直接输出，ThinkingBox自己处理XML解析
        // 移除XmlCharacterReassembler，减少50ms延迟
        return outputSanitizer.sanitizeForDirectOutput(token)
    }

    /**
     * 处理WebSocket帧格式
     */
    private fun processWebSocketFrame(token: String, conversationId: String): String {
        return try {
            val content = contentExtractor.extractWebSocketContent(token)
            outputSanitizer.sanitizeForDirectOutput(content)
        } catch (e: Exception) {
            Timber.tag(TAG).v(e, "WebSocket帧处理跳过: $token")
            ""
        }
    }

    /**
     * 处理纯文本流
     */
    private fun processPlainText(token: String, conversationId: String): String {
        return outputSanitizer.sanitizeForDirectOutput(token)
    }
}

/**
 * 📄 内容提取器接口
 */
interface ContentExtractor {
    /**
     * 即时提取JSON SSE内容
     */
    fun extractJsonSseContent(token: String): String

    /**
     * 即时提取JSON流内容
     */
    fun extractJsonStreamContent(token: String): String

    /**
     * 提取WebSocket内容
     */
    fun extractWebSocketContent(token: String): String
}

/**
 * 🛡️ 输出净化器接口
 */
interface OutputSanitizer {
    /**
     * 为直接输出净化内容
     */
    fun sanitizeForDirectOutput(content: String): String
}

/**
 * 📄 内容提取器实现 - 从protocol目录移动过来
 *
 * 优化：移除缓冲机制，改为即时提取
 */
@Singleton
class ContentExtractorImpl @Inject constructor(
    private val json: kotlinx.serialization.json.Json
) : ContentExtractor {

    companion object {
        private const val TAG = "ContentExtractor"
    }

    /**
     * 即时提取JSON SSE内容
     *
     * 🔥 优化：移除200ms静默定时器，改为即时提取
     */
    override fun extractJsonSseContent(token: String): String {
        if (!token.startsWith("data: ")) return ""

        val jsonPart = token.removePrefix("data: ").trim()
        if (jsonPart == "[DONE]") return ""

        return try {
            val jsonElement = json.parseToJsonElement(jsonPart)
            val jsonObj = jsonElement.jsonObject

            // OpenAI格式
            val choices = jsonObj["choices"]?.jsonArray
            val firstChoice = choices?.firstOrNull()?.jsonObject
            val delta = firstChoice?.get("delta")?.jsonObject
            val content = delta?.get("content")?.jsonPrimitive?.content

            content
                // Claude格式
                ?: jsonObj["delta"]?.jsonObject?.get("text")?.jsonPrimitive?.content
                // 通用格式
                ?: jsonObj["content"]?.jsonPrimitive?.content
                ?: ""

        } catch (e: Exception) {
            Timber.tag(TAG).v(e, "JSON SSE解析跳过: $jsonPart")
            ""
        }
    }

    /**
     * 即时提取JSON流内容
     */
    override fun extractJsonStreamContent(token: String): String {
        return try {
            val jsonElement = json.parseToJsonElement(token)
            val jsonObj = jsonElement.jsonObject

            jsonObj["text"]?.jsonPrimitive?.content
                ?: jsonObj["message"]?.jsonPrimitive?.content
                ?: jsonObj["content"]?.jsonPrimitive?.content
                ?: ""

        } catch (e: Exception) {
            Timber.tag(TAG).v(e, "JSON流解析跳过: $token")
            ""
        }
    }

    /**
     * 提取WebSocket内容
     */
    override fun extractWebSocketContent(token: String): String {
        // 简化的WebSocket帧解析
        return if (token.startsWith("WS:")) {
            token.removePrefix("WS:")
        } else {
            token
        }
    }
}

/**
 * 🛡️ 输出净化器实现 - 从security目录重命名
 *
 * 优化：简化为直接输出净化，移除XML转义（让ThinkingBox处理）
 */
@Singleton
class OutputSanitizerImpl @Inject constructor(
    private val piiSanitizer: com.example.gymbro.core.network.security.PiiSanitizer
) : OutputSanitizer {

    /**
     * 为直接输出净化内容
     *
     * 🔥 关键优化：移除XML转义，让ThinkingBox自己处理
     * 减少20ms延迟
     */
    override fun sanitizeForDirectOutput(content: String): String {
        if (content.isEmpty()) return ""

        // 仅做PII过滤，移除XML转义
        return piiSanitizer.sanitizeContent(content)
    }
}
