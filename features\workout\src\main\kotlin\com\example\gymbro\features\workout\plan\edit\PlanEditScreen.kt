package com.example.gymbro.features.workout.plan.edit

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.CalendarMonth
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Save
import androidx.compose.material.icons.filled.ViewWeek
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.domain.workout.model.plan.DayPlan
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

/**
 * Plan Edit Screen - 训练计划编辑界面
 *
 * 🎯 核心功能：
 * - 4周x7天的训练计划编辑
 * - 模板拖拽到日期安排
 * - 日历/周视图切换
 * - 实时保存和同步
 *
 * 🏗️ 架构特点：
 * - 遵循Box+LazyColumn+Surface设计模式
 * - 100% Token化，零硬编码值
 * - 统一M3动画系统
 * - MVI 2.0架构合规
 * - 废弃复杂Canvas系统，使用简单Compose组件
 */
@Composable
fun PlanEditScreen(
    planId: String,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    viewModel: PlanEditViewModel = hiltViewModel(),
) {
    // MVI 2.0: 状态收集
    val state by viewModel.state.collectAsStateWithLifecycle()

    // MVI 2.0: Effect处理
    LaunchedEffect(viewModel) {
        viewModel.effect.collect { effect ->
            when (effect) {
                is PlanEditContract.Effect.NavigateBack -> onNavigateBack()
                is PlanEditContract.Effect.ShowSaveSuccess -> {
                    // TODO: 实现保存成功提示
                }
                is PlanEditContract.Effect.ShowError -> {
                    // TODO: 实现错误提示
                }
                else -> {
                    // 其他Effect暂不处理
                }
            }
        }
    }

    // 初始化加载
    LaunchedEffect(planId) {
        viewModel.dispatch(PlanEditContract.Intent.LoadPlan(planId))
    }

    PlanEditScreenContent(
        state = state,
        onAction = viewModel::dispatch,
        onNavigateBack = onNavigateBack,
        modifier = modifier,
    )
}

/**
 * Plan Edit Screen Content - 纯UI组件实现
 * 分离ViewModel依赖，便于测试和预览
 */
@Composable
private fun PlanEditScreenContent(
    state: PlanEditContract.State,
    onAction: (PlanEditContract.Intent) -> Unit,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
) {
    // 🔥 遵循Box+LazyColumn+Surface架构模式 - 与Plan主界面保持一致
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background) // 使用系统背景色，与Plan主界面一致
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 顶部导航栏
            PlanEditTopBar(
                planName = state.planName,
                showCalendarView = state.showCalendarView,
                onNavigateBack = onNavigateBack,
                onToggleView = { onAction(PlanEditContract.Intent.ToggleCalendarView) },
                onSave = { onAction(PlanEditContract.Intent.SavePlan) },
                isLoading = state.isLoading,
            )

            // 主要内容区域
            if (state.showCalendarView) {
                // 日历视图
                PlanCalendarContent(
                    state = state,
                    onAction = onAction,
                    modifier = Modifier.weight(1f),
                )
            } else {
                // 周视图（默认）
                PlanWeeklyContent(
                    state = state,
                    onAction = onAction,
                    modifier = Modifier.weight(1f),
                )
            }
        }

        // 浮动模板选择器
        if (state.showTemplateSelector) {
            FloatingTemplateSelector(
                templates = state.availableTemplates,
                onTemplateSelect = { template ->
                    onAction(PlanEditContract.Intent.SelectTemplate(template))
                },
                onDismiss = { onAction(PlanEditContract.Intent.HideTemplateSelector) },
                modifier = Modifier.align(Alignment.BottomCenter),
            )
        }
    }
}

/**
 * Plan Edit Top Bar - Token化的顶部导航栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun PlanEditTopBar(
    planName: String,
    showCalendarView: Boolean,
    onNavigateBack: () -> Unit,
    onToggleView: () -> Unit,
    onSave: () -> Unit,
    isLoading: Boolean,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        color = MaterialTheme.workoutColors.cardBackground,
        shadowElevation = Tokens.Elevation.Small,
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            // 返回按钮
            IconButton(
                onClick = onNavigateBack,
                modifier = Modifier.size(Tokens.Icon.Large),
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "返回",
                    tint = MaterialTheme.workoutColors.accentPrimary,
                )
            }

            // 计划名称
            Text(
                text = planName,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.workoutColors.textPrimary,
                textAlign = TextAlign.Center,
                modifier = Modifier.weight(1f),
            )

            // 视图切换按钮
            IconButton(
                onClick = onToggleView,
                modifier = Modifier.size(Tokens.Icon.Large),
            ) {
                Icon(
                    imageVector = if (showCalendarView) Icons.Default.CalendarMonth else Icons.Default.ViewWeek,
                    contentDescription = if (showCalendarView) "切换到周视图" else "切换到日历视图",
                    tint = MaterialTheme.workoutColors.accentSecondary,
                )
            }

            // 保存按钮
            IconButton(
                onClick = onSave,
                enabled = !isLoading,
                modifier = Modifier.size(Tokens.Icon.Large),
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(Tokens.Icon.Medium),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.workoutColors.accentPrimary,
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.Save,
                        contentDescription = "保存",
                        tint = MaterialTheme.workoutColors.accentPrimary,
                    )
                }
            }
        }
    }
}

/**
 * Plan Weekly Content - 周视图内容（4周x7天网格）
 * 使用LazyColumn + LazyRow替代复杂Canvas系统
 * 🎨 与Plan主界面保持一致的设计风格
 */
@Composable
private fun PlanWeeklyContent(
    state: PlanEditContract.State,
    onAction: (PlanEditContract.Intent) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(Tokens.Spacing.Medium), // 与Plan主界面一致的内边距
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium), // 与Plan主界面一致的间距
    ) {
        // 4周循环
        items(4) { weekIndex ->
            val week = weekIndex + 1

            WeekPlanCard(
                week = week,
                dayPlans = state.getWeekPlans(week),
                onDayPlanUpdate = { day, dayPlan ->
                    onAction(PlanEditContract.Intent.UpdateDayPlan(week, day, dayPlan))
                },
                onAddTemplate = { week, day ->
                    onAction(PlanEditContract.Intent.ShowTemplateSelector(week, day))
                },
            )
        }
    }
}

/**
 * Week Plan Card - 单周计划卡片
 * 使用LazyRow实现7天横向滚动
 */
@Composable
private fun WeekPlanCard(
    week: Int,
    dayPlans: List<DayPlan>,
    onDayPlanUpdate: (Int, DayPlan) -> Unit,
    onAddTemplate: (Int, Int) -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(Tokens.Radius.Card), // 与Plan主界面一致的圆角
        color = MaterialTheme.workoutColors.cardBackground,
        shadowElevation = Tokens.Elevation.Small, // 与Plan主界面一致的阴影
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium), // 与Plan主界面一致的内边距
        ) {
            // 周标题 - 与Plan主界面保持一致的文本样式
            Text(
                text = "第 $week 周",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium, // 与Plan主界面一致的字重
                color = MaterialTheme.workoutColors.textPrimary, // 使用主要文本颜色
                modifier = Modifier.padding(bottom = Tokens.Spacing.Small),
            )

            // 7天横向滚动
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                contentPadding = PaddingValues(horizontal = Tokens.Spacing.XSmall),
            ) {
                items(7) { dayIndex ->
                    val day = dayIndex + 1
                    val dayPlan = dayPlans.getOrNull(dayIndex) ?: DayPlan.createWorkoutDay(day, emptyList())

                    DayPlanCard(
                        dayPlan = dayPlan,
                        onUpdate = { updatedPlan -> onDayPlanUpdate(day, updatedPlan) },
                        onAddTemplate = { onAddTemplate(week, day) },
                        modifier = Modifier.width(120.dp),
                    )
                }
            }
        }
    }
}

/**
 * Plan Calendar Content - 日历视图内容
 * 简化的日历视图实现
 */
@Composable
private fun PlanCalendarContent(
    state: PlanEditContract.State,
    onAction: (PlanEditContract.Intent) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(Tokens.Spacing.Medium),
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
    ) {
        item {
            Surface(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(Tokens.Radius.Card), // 与Plan主界面一致
                color = MaterialTheme.workoutColors.cardBackground,
                shadowElevation = Tokens.Elevation.Small, // 与Plan主界面一致
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(Tokens.Spacing.Large),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Text(
                        text = "日历视图",
                        style = MaterialTheme.typography.titleLarge, // 与Plan主界面一致的标题样式
                        fontWeight = FontWeight.Medium, // 与Plan主界面一致的字重
                        color = MaterialTheme.workoutColors.textPrimary, // 使用主要文本颜色
                    )

                    Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))

                    Text(
                        text = "日历视图功能开发中...",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.workoutColors.textSecondary,
                        textAlign = TextAlign.Center,
                    )

                    Spacer(modifier = Modifier.height(Tokens.Spacing.Small))

                    Text(
                        text = "请切换到周视图进行计划编辑",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.workoutColors.textSecondary,
                        textAlign = TextAlign.Center,
                    )
                }
            }
        }
    }
}

/**
 * Floating Template Selector - 浮动模板选择器
 * 简化的模板选择实现
 */
@Composable
private fun FloatingTemplateSelector(
    templates: ImmutableList<WorkoutTemplate>,
    onTemplateSelect: (WorkoutTemplate) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .padding(Tokens.Spacing.Medium),
        shape = RoundedCornerShape(
            topStart = Tokens.Radius.Card, // 与Plan主界面一致的圆角
            topEnd = Tokens.Radius.Card,
        ),
        color = MaterialTheme.workoutColors.cardBackground,
        shadowElevation = Tokens.Elevation.Medium, // 稍微增加阴影以突出浮动效果
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
        ) {
            // 标题栏
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = "选择训练模板",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.workoutColors.textPrimary,
                )

                TextButton(onClick = onDismiss) {
                    Text(
                        text = "取消",
                        color = MaterialTheme.workoutColors.accentSecondary,
                    )
                }
            }

            Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))

            // 模板网格
            LazyVerticalGrid(
                columns = GridCells.Fixed(2),
                modifier = Modifier.height(200.dp),
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            ) {
                items(templates) { template ->
                    TemplateCard(
                        template = template,
                        onClick = { onTemplateSelect(template) },
                    )
                }
            }
        }
    }
}

/**
 * Template Card - 模板卡片
 */
@Composable
private fun TemplateCard(
    template: WorkoutTemplate,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        onClick = onClick,
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(Tokens.Radius.Card),
        color = MaterialTheme.workoutColors.cardBackground,
        shadowElevation = Tokens.Elevation.Small,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Small),
        ) {
            Text(
                text = template.name.toString(),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.workoutColors.textPrimary,
                maxLines = 2,
            )

            Spacer(modifier = Modifier.height(Tokens.Spacing.XSmall))

            Text(
                text = "${template.exercises.size}个动作",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.workoutColors.textSecondary,
            )
        }
    }
}

/**
 * Day Plan Card - 简化的日计划卡片
 * 适配新的MVI 2.0架构，与Plan主界面保持一致的设计风格
 */
@Composable
private fun DayPlanCard(
    dayPlan: DayPlan,
    onUpdate: (DayPlan) -> Unit,
    onAddTemplate: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(Tokens.Radius.Card), // 与Plan主界面一致
        color = if (dayPlan.isRestDay) {
            MaterialTheme.workoutColors.cardBackground.copy(alpha = 0.7f) // 稍微调整透明度
        } else {
            MaterialTheme.workoutColors.cardBackground
        },
        shadowElevation = Tokens.Elevation.Small, // 与Plan主界面一致
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Small),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
        ) {
            // 日期标题
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = getDayName(dayPlan.dayNumber),
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.workoutColors.textPrimary,
                )

                // 休息日开关
                Switch(
                    checked = dayPlan.isRestDay,
                    onCheckedChange = { isRest ->
                        val updatedPlan = if (isRest) {
                            DayPlan.createRestDay(dayPlan.dayNumber)
                        } else {
                            DayPlan.createWorkoutDay(dayPlan.dayNumber, emptyList())
                        }
                        onUpdate(updatedPlan)
                    },
                    modifier = Modifier.size(Tokens.Icon.Medium),
                )
            }

            // 内容区域
            if (dayPlan.isRestDay) {
                // 休息日显示
                Text(
                    text = "休息日",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.workoutColors.textSecondary,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth(),
                )
            } else {
                // 训练日显示
                if (dayPlan.templateVersionIds.isEmpty()) {
                    // 空状态 - 与Plan主界面保持一致的交互设计
                    Surface(
                        onClick = onAddTemplate,
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(Tokens.Radius.Small),
                        color = MaterialTheme.workoutColors.accentPrimary.copy(alpha = 0.1f),
                    ) {
                        Text(
                            text = "添加训练",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.workoutColors.accentPrimary,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(Tokens.Spacing.Small),
                        )
                    }
                } else {
                    // 已安排的模板
                    Column(
                        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.XSmall),
                    ) {
                        dayPlan.templateVersionIds.forEachIndexed { index, templateId ->
                            TemplateChip(
                                templateId = templateId,
                                onRemove = {
                                    val updatedTemplates = dayPlan.templateVersionIds.toMutableList()
                                    updatedTemplates.removeAt(index)
                                    val updatedPlan = dayPlan.copy(templateVersionIds = updatedTemplates)
                                    onUpdate(updatedPlan)
                                },
                            )
                        }

                        // 添加更多按钮
                        Surface(
                            onClick = onAddTemplate,
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(Tokens.Radius.Small),
                            color = MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.1f),
                        ) {
                            Text(
                                text = "+ 添加更多",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.workoutColors.accentSecondary,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.padding(Tokens.Spacing.XSmall),
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * Template Chip - 模板标签
 * 与Plan主界面保持一致的标签设计
 */
@Composable
private fun TemplateChip(
    templateId: String,
    onRemove: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(Tokens.Radius.Small),
        color = MaterialTheme.workoutColors.accentSecondary.copy(alpha = 0.1f), // 使用次要强调色
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.XSmall),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = "模板 ${templateId.take(8)}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.workoutColors.textPrimary,
                modifier = Modifier.weight(1f),
            )

            IconButton(
                onClick = onRemove,
                modifier = Modifier.size(Tokens.Icon.Small),
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "移除",
                    tint = MaterialTheme.workoutColors.accentSecondary,
                    modifier = Modifier.size(12.dp),
                )
            }
        }
    }
}

/**
 * 获取星期名称
 */
private fun getDayName(day: Int): String {
    return when (day) {
        1 -> "周一"
        2 -> "周二"
        3 -> "周三"
        4 -> "周四"
        5 -> "周五"
        6 -> "周六"
        7 -> "周日"
        else -> "第${day}天"
    }
}

/**
 * Preview - 预览组件
 */
@GymBroPreview
@Composable
private fun PlanEditScreenPreview() {
    GymBroTheme {
        val sampleState = PlanEditContract.State(
            planId = "sample_plan",
            planName = "增肌训练计划",
            showCalendarView = false,
            showTemplateSelector = false,
            weekPlans = mapOf(
                1 to listOf(
                    DayPlan.createWorkoutDay(1, listOf("template1")),
                    DayPlan.createWorkoutDay(2, listOf("template2", "template3")),
                    DayPlan.createRestDay(3),
                    DayPlan.createWorkoutDay(4, listOf("template4")),
                    DayPlan.createWorkoutDay(5, emptyList()),
                    DayPlan.createRestDay(6),
                    DayPlan.createRestDay(7),
                )
            ),
            availableTemplates = persistentListOf(),
            hasUnsavedChanges = true,
        )

        PlanEditScreenContent(
            state = sampleState,
            onAction = { },
            onNavigateBack = { },
        )
    }
}
