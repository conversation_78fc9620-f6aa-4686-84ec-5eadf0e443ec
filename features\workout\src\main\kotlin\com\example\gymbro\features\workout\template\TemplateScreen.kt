package com.example.gymbro.features.workout.template

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.components.extensions.asString
import kotlinx.coroutines.flow.collectLatest

/**
 * =========================================================================================
 * 🔥 GymBro Template Screen - 遵循黄金标准 🔥
 * =========================================================================================
 *
 * 本Screen遵循 ProfileBioScreen 黄金标准，实现简洁、高效的UI架构。
 *
 * 🎯 核心原则：
 * 1. 无状态设计：接收State对象来渲染UI
 * 2. 回调模式：通过lambda回调发送Intent
 * 3. 设计系统：100%使用designSystem Tokens
 * 4. 单一职责：专注于UI渲染，不包含业务逻辑
 * 5. 组件化：将复杂UI拆分为独立的Composable组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TemplateScreen(
    onNavigateToCreateTemplate: () -> Unit = {},
    onNavigateToEditTemplate: (String) -> Unit = {},
    onNavigateBack: () -> Unit = {},
    viewModel: TemplateViewModel = hiltViewModel(),
) {
    val state by viewModel.state.collectAsStateWithLifecycle()

    // 监听一次性 Effect
    LaunchedEffect(Unit) {
        viewModel.effect.collectLatest { effect ->
            when (effect) {
                is TemplateContract.Effect.ShowToast -> {
                    // TODO: Show toast
                }
                is TemplateContract.Effect.ShowError -> {
                    // TODO: Show error
                }
                is TemplateContract.Effect.NavigateToCreateTemplate -> {
                    onNavigateToCreateTemplate()
                }
                is TemplateContract.Effect.NavigateToEditTemplate -> {
                    onNavigateToEditTemplate(effect.templateId)
                }
                is TemplateContract.Effect.DeleteTemplate -> {
                    // 删除操作由EffectHandler处理，这里不需要额外处理
                }
                is TemplateContract.Effect.LoadTemplatesData -> {
                    // 数据加载由EffectHandler处理
                }
                is TemplateContract.Effect.RefreshTemplatesData -> {
                    // 数据刷新由EffectHandler处理
                }
                is TemplateContract.Effect.LoadDraftsData -> {
                    // 草稿加载由EffectHandler处理
                }
                is TemplateContract.Effect.RefreshDraftsData -> {
                    // 草稿刷新由EffectHandler处理
                }
            }
        }
    }

    TemplateContent(
        state = state,
        onIntent = viewModel::dispatch
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TemplateContent(
    state: TemplateContract.State,
    onIntent: (TemplateContract.Intent) -> Unit
) {
    Scaffold(
        topBar = {
            TemplateTopBar(
                currentTab = state.currentTab,
                showSearchField = state.showSearchField,
                onToggleSearch = { onIntent(TemplateContract.Intent.ToggleSearch) },
                onNavigateToCreate = { onIntent(TemplateContract.Intent.NavigateToCreateTemplate) },
                onNavigateBack = { /* TODO: Handle back navigation */ }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 搜索栏
            if (state.showSearchField) {
                TemplateSearchBar(
                    query = state.searchQuery,
                    onQueryChange = { query ->
                        onIntent(TemplateContract.Intent.UpdateSearchQuery(query))
                    },
                    onClearSearch = {
                        onIntent(TemplateContract.Intent.ClearSearch)
                    }
                )
            }

            // Tab切换
            TemplateTabRow(
                currentTab = state.currentTab,
                onTabSelected = { tab ->
                    onIntent(TemplateContract.Intent.SwitchTab(tab))
                }
            )

            // 内容区域
            TemplateTabContent(
                state = state,
                onIntent = onIntent
            )
        }
    }
}

// === 组件实现 ===

@Composable
private fun TemplateTopBar(
    currentTab: TemplateContract.TemplateTab,
    showSearchField: Boolean,
    onToggleSearch: () -> Unit,
    onNavigateToCreate: () -> Unit,
    onNavigateBack: () -> Unit
) {
    TopAppBar(
        title = { Text("训练模板") },
        navigationIcon = {
            IconButton(onClick = onNavigateBack) {
                Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "返回")
            }
        },
        actions = {
            IconButton(onClick = onToggleSearch) {
                Icon(
                    imageVector = if (showSearchField) Icons.Default.Close else Icons.Default.Search,
                    contentDescription = if (showSearchField) "关闭搜索" else "搜索模板"
                )
            }
            IconButton(onClick = onNavigateToCreate) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = when (currentTab) {
                        TemplateContract.TemplateTab.TEMPLATES -> "创建新模板"
                        TemplateContract.TemplateTab.DRAFTS -> "创建新草稿"
                    }
                )
            }
        }
    )
}

@Composable
private fun TemplateSearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    onClearSearch: () -> Unit
) {
    OutlinedTextField(
        value = query,
        onValueChange = onQueryChange,
        label = { Text("搜索模板") },
        modifier = Modifier
            .fillMaxWidth()
            .padding(Tokens.Spacing.Medium),
        trailingIcon = {
            if (query.isNotEmpty()) {
                IconButton(onClick = onClearSearch) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "清除搜索"
                    )
                }
            }
        },
        singleLine = true
    )
}

@Composable
private fun TemplateTabRow(
    currentTab: TemplateContract.TemplateTab,
    onTabSelected: (TemplateContract.TemplateTab) -> Unit
) {
    TabRow(
        selectedTabIndex = currentTab.ordinal,
        modifier = Modifier.fillMaxWidth()
    ) {
        TemplateContract.TemplateTab.values().forEach { tab ->
            Tab(
                selected = currentTab == tab,
                onClick = { onTabSelected(tab) },
                text = { Text(tab.name) }
            )
        }
    }
}

@Composable
private fun TemplateTabContent(
    state: TemplateContract.State,
    onIntent: (TemplateContract.Intent) -> Unit
) {
    when (state.currentTab) {
        TemplateContract.TemplateTab.TEMPLATES -> {
            TemplateList(
                templates = state.filteredTemplates,
                isLoading = state.isLoading,
                error = state.error,
                onEditTemplate = { templateId ->
                    onIntent(TemplateContract.Intent.NavigateToEditTemplate(templateId))
                },
                onDeleteTemplate = { templateId ->
                    onIntent(TemplateContract.Intent.DeleteTemplate(templateId))
                }
            )
        }
        TemplateContract.TemplateTab.DRAFTS -> {
            EmptyDraftsContent()
        }
    }
}

@Composable
private fun TemplateList(
    templates: List<com.example.gymbro.domain.workout.model.template.WorkoutTemplate>,
    isLoading: Boolean,
    error: com.example.gymbro.core.ui.text.UiText?,
    onEditTemplate: (String) -> Unit,
    onDeleteTemplate: (String) -> Unit
) {
    when {
        isLoading -> {
            LoadingContent()
        }
        error != null -> {
            ErrorContent(
                error = error,
                onRetry = { /* TODO: Retry logic */ }
            )
        }
        templates.isEmpty() -> {
            EmptyTemplatesContent()
        }
        else -> {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(Tokens.Spacing.Medium),
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small)
            ) {
                items(
                    items = templates,
                    key = { template -> template.id }
                ) { template ->
                    TemplateCard(
                        template = template,
                        onEdit = { onEditTemplate(template.id) },
                        onDelete = { onDeleteTemplate(template.id) }
                    )
                }
            }
        }
    }
}

@Composable
private fun TemplateCard(
    template: com.example.gymbro.domain.workout.model.template.WorkoutTemplate,
    onEdit: () -> Unit,
    onDelete: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = onEdit
    ) {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small)
        ) {
            Text(
                text = template.name,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )

            if (!template.description.isNullOrBlank()) {
                Text(
                    text = template.description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Text(
                text = "${template.exercises.size} 个动作",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun LoadingContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator()
    }
}

@Composable
private fun ErrorContent(
    error: com.example.gymbro.core.ui.text.UiText,
    onRetry: () -> Unit
) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium)
        ) {
            Text(
                text = error.asString(),
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.error
            )
            Button(onClick = onRetry) {
                Text("重试")
            }
        }
    }
}

@Composable
private fun EmptyTemplatesContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium)
        ) {
            Text(
                text = "还没有模板",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "点击右上角按钮创建第一个模板",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun EmptyDraftsContent() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "草稿功能开发中",
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}
