package com.example.gymbro.core.network.logging

import com.example.gymbro.core.logging.LoggingConfig
import timber.log.Timber

/**
 * 网络模块专用的 Timber 日志树
 *
 * 遵循GymBro项目的模块化日志架构，专门处理网络相关日志：
 * - 统一的CNET-*标签管理
 * - 与LoggingConfig集成的模块级控制
 * - 网络敏感信息过滤
 * - 性能监控日志
 *
 * 🔥 【架构集成】与TimberManager和LoggingConfig完全集成
 */
class NetworkLogTree(
    private val loggingConfig: LoggingConfig,
) : Timber.Tree() {

    companion object {
        // 🔥 【标签定义】统一的网络模块日志标签
        object Tags {
            const val ERROR = "CNET-ERROR" // 🔥 【关键】网络错误日志
            const val STREAM = "CNET-STREAM" // 流式传输
            const val CHECK = "CNET-CHECK" // 连接检查
            const val SSE = "CNET-SSE" // SSE 连接
            const val MONITOR = "CNET-MONITOR" // 网络状态监控
            const val RETRY = "CNET-RETRY" // 重试策略
            const val LIFECYCLE = "CNET-LIFECYCLE" // 生命周期
            const val SECURITY = "CNET-SECURITY" // 安全相关
            const val PERFORMANCE = "CNET-PERF" // 性能监控
            
            // 🔥 【RAW TOKEN日志】新增token采集标签
            const val TOKEN_RECEIVED = "CNET-TOKEN-RECV" // 接收的RAW TOKEN
            const val TOKEN_OUTPUT = "CNET-TOKEN-OUT" // 输出的RAW TOKEN
            const val TOKEN_STATS = "CNET-TOKEN-STATS" // Token统计信息
            const val TOKEN_BATCH = "CNET-TOKEN-BATCH" // Token批量操作
            const val TOKEN_ERROR = "CNET-TOKEN-ERROR" // Token处理错误
        }

        // 敏感信息关键词
        private val SENSITIVE_KEYWORDS = setOf(
            "api_key", "apikey", "token", "password", "secret",
            "authorization", "bearer", "key", "credential",
        )

        /**
         * 🔥 【便捷方法】快速测试CNET标签功能
         *
         * 可以在应用启动时调用此方法验证网络日志是否正常工作
         */
        fun testNetworkLogging() {
            Timber.tag("CNET-TEST").i("🧪 [测试] 网络日志系统测试 - 如果看到这条消息，说明CNET标签正常工作")
        }
    }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // 🔥 【架构集成】只处理网络模块的标签
        if (!isNetworkTag(tag)) {
            return
        }

        // 🔥 【架构集成】使用LoggingConfig进行模块级控制
        if (!loggingConfig.shouldLog(LoggingConfig.MODULE_CORE_NETWORK, tag, priority)) {
            return
        }

        // 过滤敏感信息
        val filteredMessage = filterSensitiveInfo(message)

        // 格式化日志消息
        val formattedMessage = formatNetworkLogMessage(tag ?: "CNET-GENERAL", filteredMessage)

        // 🔥 【架构一致性】使用标准的Timber.DebugTree输出格式
        super.log(priority, tag, formattedMessage, t)

        // 性能监控日志特殊处理
        if (tag == Tags.PERFORMANCE) {
            handlePerformanceLog(formattedMessage)
        }
        
        // 🔥 【RAW TOKEN日志】Token日志特殊处理
        if (isTokenLogTag(tag)) {
            handleTokenLog(tag, formattedMessage)
        }
    }

    override fun isLoggable(tag: String?, priority: Int): Boolean {
        return isNetworkTag(tag) && loggingConfig.shouldLog(LoggingConfig.MODULE_CORE_NETWORK, tag, priority)
    }

    /**
     * 🔥 【架构集成】检查是否为网络模块标签
     */
    private fun isNetworkTag(tag: String?): Boolean {
        return tag?.startsWith("CNET-") == true
    }

    /**
     * 过滤敏感信息
     */
    private fun filterSensitiveInfo(message: String): String {
        var filtered = message

        SENSITIVE_KEYWORDS.forEach { keyword ->
            // 使用正则表达式匹配敏感信息模式
            val pattern = "(?i)$keyword[\"'\\s]*[:=][\"'\\s]*([^\\s,}\\]\"']+)".toRegex()
            filtered = pattern.replace(filtered) { matchResult ->
                val fullMatch = matchResult.value
                val sensitiveValue = matchResult.groupValues[1]
                fullMatch.replace(sensitiveValue, "***FILTERED***")
            }
        }

        return filtered
    }

    /**
     * 格式化网络日志消息
     * 🔥 【架构简化】遵循项目统一的日志格式
     */
    private fun formatNetworkLogMessage(tag: String, message: String): String {
        // 🔥 【网络专用】添加网络模块标识
        return "🌐 $message"
    }

    /**
     * 处理性能监控日志
     * 🔥 【架构简化】基础性能日志处理
     */
    private fun handlePerformanceLog(message: String) {
        // 🔥 【性能监控】可以在这里添加性能数据收集逻辑
        // 例如：发送到分析服务、本地存储等
        // 目前仅记录到日志，后续可扩展
    }

    /**
     * 🔥 【RAW TOKEN日志】检查是否为Token日志标签
     */
    private fun isTokenLogTag(tag: String?): Boolean {
        return tag?.startsWith("CNET-TOKEN-") == true
    }
    
    /**
     * 🔥 【RAW TOKEN日志】处理Token日志特殊逻辑
     */
    private fun handleTokenLog(tag: String?, message: String) {
        // Token日志特殊处理：添加Token专用标识
        when (tag) {
            Tags.TOKEN_RECEIVED -> {
                // 接收的RAW TOKEN日志
            }
            Tags.TOKEN_OUTPUT -> {
                // 输出的RAW TOKEN日志
            }
            Tags.TOKEN_STATS -> {
                // Token统计信息特殊处理
            }
            Tags.TOKEN_BATCH -> {
                // 批量Token操作日志
            }
            Tags.TOKEN_ERROR -> {
                // Token处理错误，可能需要额外监控
            }
        }
    }

    /**
     * 🔥 【测试工具】验证CNET标签是否正常工作
     *
     * 这个方法可以在应用启动时调用，验证网络日志系统是否正确配置
     */
    fun testCnetTagsWorking() {
        // 测试各种CNET标签
        Timber.tag(Tags.ERROR).e("🧪 [测试] CNET-ERROR标签测试")
        Timber.tag(Tags.STREAM).d("🧪 [测试] CNET-STREAM标签测试")
        Timber.tag(Tags.CHECK).i("🧪 [测试] CNET-CHECK标签测试")
        Timber.tag(Tags.SSE).d("🧪 [测试] CNET-SSE标签测试")
        Timber.tag(Tags.MONITOR).i("🧪 [测试] CNET-MONITOR标签测试")
        Timber.tag(Tags.RETRY).w("🧪 [测试] CNET-RETRY标签测试")
        Timber.tag(Tags.LIFECYCLE).d("🧪 [测试] CNET-LIFECYCLE标签测试")
        Timber.tag(Tags.SECURITY).w("🧪 [测试] CNET-SECURITY标签测试")
        Timber.tag(Tags.PERFORMANCE).i("🧪 [测试] CNET-PERF标签测试")
        
        // 🔥 【新增】测试RAW TOKEN日志标签
        Timber.tag(Tags.TOKEN_RECEIVED).i("🧪 [测试] CNET-TOKEN-RECV标签测试")
        Timber.tag(Tags.TOKEN_OUTPUT).i("🧪 [测试] CNET-TOKEN-OUT标签测试")
        Timber.tag(Tags.TOKEN_STATS).i("🧪 [测试] CNET-TOKEN-STATS标签测试")
        Timber.tag(Tags.TOKEN_BATCH).i("🧪 [测试] CNET-TOKEN-BATCH标签测试")
        Timber.tag(Tags.TOKEN_ERROR).w("🧪 [测试] CNET-TOKEN-ERROR标签测试")

        // 测试敏感信息过滤
        Timber.tag(Tags.SECURITY).w("🧪 [测试] 敏感信息过滤: api_key=secret123, token=bearer456")
    }
}
