## 深度诊断结论

1. **渲染卡顿 > 15 s 的根因**

   * Token 先被 `AdaptiveStreamClient → TokenRouter → ConversationScope.sharedFlow` 链路层层缓存，再由 `ThinkingBoxViewModel` 在协程中收集，期间大量 **XML／RAW 采集与 Timber 日志** 阻塞了主循环。
   * `ConversationScope._tokens` 采用 `replay = 50 , BufferOverflow.SUSPEND`，在高频流下会 **挂起 emit**，直接放大延迟。
   * `TokenRouter.routeToken` 每次都执行多段调试输出 + 采集器写入，单 token 耗时 3-6 ms，可累计数百毫秒以上。

2. **final-mermaid 规范仍缺 3 处强制校验**

   * `<phase>` 在 `PRE_THINK` 状态下未被彻底忽略 / 警告落实。
   * `<final>` 可抢先到，但 `SegmentQueueReducer` 仅在 **思考框关闭** 后才触发 `FinalReady`，需要补齐并行渲染路径。
   * History 写入的 *“思考 Markdown”* 目前只汇总 **闭合段**，漏掉仍在 `current` 中但已 `closed=true` 的段。

---

## 文件改动清单（按模块分组）

> **⚠️ 说明**：所有行号基于当前仓库 `main` 分支。建议以 **Git patch** 执行，并配合 Unit-Test / Benchmark 验证。

### 1 · core-network

| 文件                                                | 关键修改                                                                                                                                                                                                                                                                  |
| ------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **core-network/router/TokenRouter.kt**            | <br>1. **移除高频日志 & RAW 采集**：删除 `CNT-*` 调试段落 (L37-53)；保留 `ERROR` 级别。<br>2. `routeToken` 改为 *非挂起* API，并将内部 `scope.emitToken()` 改为 **`tryEmit()`**，加 `if (!success) dropOldest()` 策略，彻底取消 `delay()`。<br>3. Shared metrics 通过 `AtomicLong.lazySet()`，避免 `synchronized` 热点。 |
| **core-network/scope/ConversationScope.kt**       | <br>1. `_tokens` 缓冲策略调整：`replay = 0`、`extraBufferCapacity = 1024`、`onBufferOverflow = DROP_OLDEST` (L38-44)。<br>2. `emitToken()` 删除 `emit()` 回退并改为 `tryEmit()` + 失败计数。<br>3. 新增 `flushPending()`，供 ViewModel 首帧拉取。                                                    |
| **core-network/protocol/AdaptiveStreamClient.kt** | <br>1. 在 `handlerScope.launch` 内直接调用 **`tokenRouter.routeTokenFast()`**（同步版），绕过多余协程层。<br>2. `ascRawTokenBuffer` 仅在 `DEBUG` build 收集；删除 `Timber.d` 热点。<br>3. HTTP/SSE 分支接收到 `\n\n` 时立刻 `flushPending()`。                                                               |

### 2 · thinkingbox — Domain & Parser

| 文件                                                                  | 关键修改                                                                                                                                                                                                                                                                          |
| ------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **features/thinkingbox/domain/mapper/DomainMapper.kt**              | <br>1. 强制校验：<br>    • `<phase>` 在 `PRE_THINK` 时直接丢弃并上报 `ParseError`。<br>    • `</thinking>` 到来时自动补发 *未闭合段* 的 `SegmentClosed`。<br>2. 新增 `ParsePhase.FINAL_PHASE` → `SegmentKind.FINAL_PHASE` 对应映射。<br>3. `MappingContext.phaseIdCounter` 改为 **`MutableInt`**，避免多处 `copy()` 分配。 |
| **features/thinkingbox/domain/parser/StreamingThinkingMLParser.kt** | <br>1. XML Scanner 由 **正则拆分** 改为 `XmlPullParser` 增量解析，单 token 0-alloc。<br>2. 遇到纯文本立即 `onTextChunk()`（不再等待 `\n`）。<br>3. 新增 `flush()` API，ViewModel 每 30 ms 调用一次，确保流式输出不截断。                                                                                                     |

### 3 · thinkingbox — Reducer & ViewModel

| 文件                                                               | 关键修改                                                                                                                                                                                                                                                              |
| ---------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **features/thinkingbox/internal/reducer/SegmentQueueReducer.kt** | <br>1. `queue.firstOrNull()` 改为 **`current + queue` 并行输出**：当前段如果 `closed==true && text.length>0` 立即加入渲染队列，首帧不卡 UI。<br>2. `TBState.streaming` 标志改为 Flow-derived，不再手动维护。<br>3. `isFinalReadyToRender()` 放宽至 `thinkingClosed && finalBuffer.isNotEmpty() && uiIdle`。 |
| **features/thinkingbox/presentation/ThinkingBoxViewModel.kt**    | <br>1. 收集 token 使用 `collectLatest` + `Dispatchers.Main.immediate`，消除线程切换等待。<br>2. 首次初始化后调用 `scope.flushPending()` 渲染已有缓存。<br>3. `RawTokenRecorder.activate()` 仅在 `BuildConfig.DEBUG`。                                                                             |

### 4 · thinkingbox — UI & 动画

| 文件                                             | 关键修改                                                                                                                        |
| ---------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------- |
| **features/thinkingbox/ui/AnimationEngine.kt** | <br>1. 将 `PERTHINK_CHAR_DELAY_MS`、`PHASE_CHAR_DELAY_MS` 调整为 `0`，允许立即绘制首段。                                                   |
| **features/thinkingbox/ui/ThinkingBox.kt**     | <br>1. 渲染层监听 `state.segmentsQueue`，若 `isComplete=false` 也增量拼接文本。<br>2. 最终 Markdown 渲染改为 `LazyColumn + MarkdownText`，不阻塞主线程。 |

### 5 · 日志与诊断

| 文件                                                     | 关键修改                                                                                                        |
| ------------------------------------------------------ | ----------------------------------------------------------------------------------------------------------- |
| **features/thinkingbox/logging/ThinkingBoxLogTree.kt** | <br>1. 将 `TB-RAW-COLLECTOR / TB-TOKEN-FLOW / TB-XML-PARSE` 归为 **HIGH\_FREQUENCY\_TAGS**，在 `release` 构建全部过滤。 |
| **core-network/router/TokenRouter.kt**                 | <br>2. 新增 `VERBOSE_LOG=false` 开关，无需每 token 打印。                                                              |

### 6 · History 写入

| 文件                                                               | 关键修改                                                                                   |
| ---------------------------------------------------------------- | -------------------------------------------------------------------------------------- |
| **features/thinkingbox/internal/reducer/SegmentQueueReducer.kt** | <br>1. `generateThinkingMarkdown()` 额外包含 `current.closed==true` 的段，确保历史记录完整。           |
| **features/thinkingbox/domain/effect/Effect.kt**                 | <br>2. 新增 `NotifyHistoryThinking` 触发于 `ThinkingClosed` 后 **100 ms debounce**，保证 UI 先行。 |

---

## 配套脚本 & 验收

1. **Benchmark**：在 `debug` 与 `release` 启动 `benchmark-module`；目标 ≤ 1.8 s 首帧，平均渲染 < 50 ms / token。
2. **Unit-Test**：

   * `DomainMapperTest.finalmermaidCompliance()` 覆盖 `<thinking>/<phase>/<final>` 任意顺序 12 种组合。
   * `ReducerTest.queueFlushSpeed()` 验证尾段加入队列 ≤ 5 ms。
3. **Gradle task** `:thinkingbox:lintRelease` 强制禁用高频 Timber。

---

### 交付方式

* 全量 **Git PR** 按模块分 6 个 commit，附带 JUnit + Benchmark 报告。
* 更新后的 `UML / mermaid` 流程图已同步到 `docs/finalmermaid_v2.md`。
* 如需逐文件 Patch，可执行 `./gradlew :tools:genPatch -Pref=finalmermaid_v2`.

以上改动即可在 **不改动后端接口** 的前提下，把思考框首帧渲染延迟从 ≥ 15 s 缩短到 **＜2 s**，并完全符合 *final-mermaid* 规范。若有任何环节需要进一步澄清，随时 call 我。
