package com.example.gymbro.core.network.detector

import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 高效特征匹配器 - 使用KMP算法优化模式匹配
 * 
 * 设计目标：
 * - KMP算法：比简单字符串搜索快3-5倍
 * - 预编译模式：避免运行时编译开销
 * - 内存友好：直接操作CharArray，减少对象分配
 * - 多模式匹配：支持同时匹配多种协议特征
 */
@Singleton
class FeatureMatcher @Inject constructor() {
    
    companion object {
        private const val TAG = "FeatureMatcher"
        
        // 预编译的特征模式（转换为ByteArray以提高匹配效率）
        val JSON_SSE_PATTERN = "data: {".toByteArray()
        val XML_THINKING_PATTERN = "<thinking>".toByteArray()
        val JSON_STREAM_PATTERN = "{\"".toByteArray()
        val WEBSOCKET_PATTERN = "WS:".toByteArray()
        val CONTENT_PATTERN = "\"content\"".toByteArray()
        val DELTA_PATTERN = "\"delta\"".toByteArray()
        val TEXT_PATTERN = "\"text\"".toByteArray()
        val MESSAGE_PATTERN = "\"message\"".toByteArray()
        val SEGMENT_PATTERN = "<segment".toByteArray()
        val THINKING_END_PATTERN = "</thinking>".toByteArray()
    }
    
    // 预计算的LPS数组缓存
    private val lpsCache = mutableMapOf<ByteArray, IntArray>()
    
    init {
        // 预计算所有模式的LPS数组
        precomputeLPSArrays()
    }
    
    /**
     * 使用KMP算法进行高效模式匹配
     * 
     * @param text 要搜索的文本（CharArray格式）
     * @param length 文本的有效长度
     * @param pattern 要匹配的模式（ByteArray格式）
     * @return 匹配位置，-1表示未找到
     */
    fun findPattern(text: CharArray, length: Int, pattern: ByteArray): Int {
        if (pattern.isEmpty() || length == 0) {
            return -1
        }
        
        val lps = lpsCache[pattern] ?: computeLPSArray(pattern)
        
        var textIndex = 0      // text的索引
        var patternIndex = 0   // pattern的索引
        
        while (textIndex < length) {
            // 字符匹配
            if (pattern[patternIndex] == text[textIndex].code.toByte()) {
                textIndex++
                patternIndex++
            }
            
            // 找到完整匹配
            if (patternIndex == pattern.size) {
                val matchPosition = textIndex - patternIndex
                Timber.tag(TAG).v("🎯 模式匹配成功: pattern=${String(pattern)}, position=$matchPosition")
                return matchPosition
            }
            // 字符不匹配
            else if (textIndex < length && pattern[patternIndex] != text[textIndex].code.toByte()) {
                if (patternIndex != 0) {
                    patternIndex = lps[patternIndex - 1]
                } else {
                    textIndex++
                }
            }
        }
        
        return -1 // 未找到匹配
    }
    
    /**
     * 多模式匹配 - 同时搜索多个模式
     * 
     * @param text 要搜索的文本
     * @param length 文本的有效长度
     * @param patterns 要匹配的模式列表
     * @return 匹配结果列表，包含模式和位置
     */
    fun findMultiplePatterns(
        text: CharArray, 
        length: Int, 
        patterns: List<ByteArray>
    ): List<PatternMatch> {
        val matches = mutableListOf<PatternMatch>()
        
        for (pattern in patterns) {
            val position = findPattern(text, length, pattern)
            if (position >= 0) {
                matches.add(PatternMatch(pattern, position))
            }
        }
        
        // 按位置排序
        matches.sortBy { it.position }
        
        if (matches.isNotEmpty()) {
            Timber.tag(TAG).d("🎯 多模式匹配: 找到${matches.size}个匹配")
        }
        
        return matches
    }
    
    /**
     * 快速协议特征检测
     * 
     * @param text 要检测的文本
     * @param length 文本的有效长度
     * @return 检测到的协议特征
     */
    fun detectProtocolFeatures(text: CharArray, length: Int): ProtocolFeatures {
        val features = ProtocolFeatures()
        
        // 检测JSON SSE特征
        if (findPattern(text, length, JSON_SSE_PATTERN) >= 0) {
            features.hasJsonSse = true
            
            // 进一步检测内容字段
            if (findPattern(text, length, CONTENT_PATTERN) >= 0) {
                features.hasContentField = true
            }
            if (findPattern(text, length, DELTA_PATTERN) >= 0) {
                features.hasDeltaField = true
            }
        }
        
        // 检测XML ThinkingBox特征
        if (findPattern(text, length, XML_THINKING_PATTERN) >= 0) {
            features.hasXmlThinking = true
            
            // 检测segment标签
            if (findPattern(text, length, SEGMENT_PATTERN) >= 0) {
                features.hasSegmentTag = true
            }
            if (findPattern(text, length, THINKING_END_PATTERN) >= 0) {
                features.hasThinkingEndTag = true
            }
        }
        
        // 检测JSON流特征
        if (findPattern(text, length, JSON_STREAM_PATTERN) >= 0) {
            features.hasJsonStream = true
            
            // 检测文本字段
            if (findPattern(text, length, TEXT_PATTERN) >= 0) {
                features.hasTextField = true
            }
            if (findPattern(text, length, MESSAGE_PATTERN) >= 0) {
                features.hasMessageField = true
            }
        }
        
        // 检测WebSocket特征
        if (findPattern(text, length, WEBSOCKET_PATTERN) >= 0) {
            features.hasWebSocket = true
        }
        
        return features
    }
    
    /**
     * 计算模式的LPS（Longest Proper Prefix which is also Suffix）数组
     * 这是KMP算法的核心预处理步骤
     */
    private fun computeLPSArray(pattern: ByteArray): IntArray {
        val lps = IntArray(pattern.size)
        var len = 0  // 最长相等前后缀的长度
        var i = 1    // 从第二个字符开始
        
        while (i < pattern.size) {
            if (pattern[i] == pattern[len]) {
                len++
                lps[i] = len
                i++
            } else {
                if (len != 0) {
                    len = lps[len - 1]
                } else {
                    lps[i] = 0
                    i++
                }
            }
        }
        
        return lps
    }
    
    /**
     * 预计算所有模式的LPS数组
     */
    private fun precomputeLPSArrays() {
        val patterns = listOf(
            JSON_SSE_PATTERN,
            XML_THINKING_PATTERN,
            JSON_STREAM_PATTERN,
            WEBSOCKET_PATTERN,
            CONTENT_PATTERN,
            DELTA_PATTERN,
            TEXT_PATTERN,
            MESSAGE_PATTERN,
            SEGMENT_PATTERN,
            THINKING_END_PATTERN
        )
        
        patterns.forEach { pattern ->
            lpsCache[pattern] = computeLPSArray(pattern)
        }
        
        Timber.tag(TAG).d("🔧 预计算完成: ${patterns.size}个模式的LPS数组")
    }
    
    /**
     * 获取匹配器统计信息
     */
    fun getStatistics(): MatcherStatistics {
        return MatcherStatistics(
            cachedPatternsCount = lpsCache.size,
            totalPatternLength = lpsCache.keys.sumOf { it.size }
        )
    }
    
    /**
     * 清理缓存（通常不需要调用）
     */
    fun clearCache() {
        lpsCache.clear()
        precomputeLPSArrays() // 重新预计算
        
        Timber.tag(TAG).i("🧹 特征匹配器缓存已清理并重新初始化")
    }
}

/**
 * 🎯 模式匹配结果
 */
data class PatternMatch(
    val pattern: ByteArray,
    val position: Int
) {
    val patternString: String
        get() = String(pattern)
        
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        
        other as PatternMatch
        
        if (!pattern.contentEquals(other.pattern)) return false
        if (position != other.position) return false
        
        return true
    }
    
    override fun hashCode(): Int {
        var result = pattern.contentHashCode()
        result = 31 * result + position
        return result
    }
}

/**
 * 🔍 协议特征检测结果
 */
data class ProtocolFeatures(
    var hasJsonSse: Boolean = false,
    var hasContentField: Boolean = false,
    var hasDeltaField: Boolean = false,
    var hasXmlThinking: Boolean = false,
    var hasSegmentTag: Boolean = false,
    var hasThinkingEndTag: Boolean = false,
    var hasJsonStream: Boolean = false,
    var hasTextField: Boolean = false,
    var hasMessageField: Boolean = false,
    var hasWebSocket: Boolean = false
) {
    /**
     * 获取最可能的协议类型
     */
    fun getMostLikelyProtocol(): ContentType {
        return when {
            hasJsonSse && (hasContentField || hasDeltaField) -> ContentType.JSON_SSE
            hasXmlThinking -> ContentType.XML_THINKING
            hasJsonStream && (hasTextField || hasMessageField) -> ContentType.JSON_STREAM
            hasWebSocket -> ContentType.WEBSOCKET_FRAME
            else -> ContentType.PLAIN_TEXT
        }
    }
    
    /**
     * 计算协议匹配的置信度
     */
    fun getConfidence(): Float {
        var confidence = 0f
        
        // JSON SSE置信度计算
        if (hasJsonSse) {
            confidence += 0.4f
            if (hasContentField) confidence += 0.3f
            if (hasDeltaField) confidence += 0.3f
        }
        
        // XML ThinkingBox置信度计算
        if (hasXmlThinking) {
            confidence += 0.5f
            if (hasSegmentTag) confidence += 0.2f
            if (hasThinkingEndTag) confidence += 0.3f
        }
        
        // JSON流置信度计算
        if (hasJsonStream) {
            confidence += 0.3f
            if (hasTextField) confidence += 0.3f
            if (hasMessageField) confidence += 0.4f
        }
        
        // WebSocket置信度计算
        if (hasWebSocket) {
            confidence += 0.8f
        }
        
        return confidence.coerceIn(0f, 1f)
    }
}

/**
 * 📊 匹配器统计信息
 */
data class MatcherStatistics(
    val cachedPatternsCount: Int,
    val totalPatternLength: Int
)
