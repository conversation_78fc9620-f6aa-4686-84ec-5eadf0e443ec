package com.example.gymbro.features.workout.template.edit

import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.shared.models.workout.TemplateExerciseDto
import timber.log.Timber
import javax.inject.Inject

/**
 * =========================================================================================
 * 🔥 GymBro Template Edit Reducer - 遵循黄金标准 🔥
 * =========================================================================================
 *
 * 本Reducer遵循 ProfileBioReducer 黄金标准，实现纯函数状态转换。
 *
 * 🎯 核心原则：
 * 1. 纯函数：给定相同输入必须产生相同输出
 * 2. 无副作用：仅进行状态转换，不执行任何副作用
 * 3. 简洁性：每个Intent处理逻辑清晰简洁
 * 4. 可测试性：易于单元测试的纯函数设计
 */
class TemplateEditReducer @Inject constructor() :
    Reducer<TemplateEditContract.Intent, TemplateEditContract.State, TemplateEditContract.Effect> {

    override fun reduce(
        intent: TemplateEditContract.Intent,
        currentState: TemplateEditContract.State
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {

        return when (intent) {
            // 模板生命周期
            is TemplateEditContract.Intent.LoadTemplate -> {
                ReduceResult.stateOnly(currentState.copy(isLoading = true, error = null))
            }

            is TemplateEditContract.Intent.CreateEmptyTemplate -> {
                val emptyTemplate = WorkoutTemplate(
                    id = "",
                    name = "",
                    description = "",
                    exercises = emptyList(),
                    userId = intent.userId,
                    isDraft = true,
                    isPublished = false
                )
                ReduceResult.stateOnly(
                    currentState.copy(
                        template = emptyTemplate,
                        exercises = emptyList(),
                        isLoading = false,
                        hasUnsavedChanges = false
                    )
                )
            }

            is TemplateEditContract.Intent.SaveTemplate -> {
                ReduceResult.stateOnly(currentState.copy(isSaving = true, error = null))
            }

            is TemplateEditContract.Intent.SaveAsDraft -> {
                ReduceResult.stateOnly(currentState.copy(isSaving = true, error = null))
            }

            is TemplateEditContract.Intent.PublishTemplate -> {
                ReduceResult.stateOnly(currentState.copy(isSaving = true, error = null))
            }

            is TemplateEditContract.Intent.DeleteTemplate -> {
                ReduceResult.stateOnly(currentState.copy(isLoading = true, error = null))
            }

            // 模板内容编辑
            is TemplateEditContract.Intent.UpdateTemplateName -> {
                val updatedTemplate = currentState.template?.copy(name = intent.name)
                ReduceResult.stateOnly(
                    currentState.copy(
                        template = updatedTemplate,
                        hasUnsavedChanges = true
                    )
                )
            }

            is TemplateEditContract.Intent.UpdateTemplateDescription -> {
                val updatedTemplate = currentState.template?.copy(description = intent.description)
                ReduceResult.stateOnly(
                    currentState.copy(
                        template = updatedTemplate,
                        hasUnsavedChanges = true
                    )
                )
            }

            is TemplateEditContract.Intent.AddExercises -> {
                val newExercises = intent.exercises.map { exercise ->
                    TemplateExerciseDto(
                        id = java.util.UUID.randomUUID().toString(),
                        exerciseId = exercise.id,
                        exerciseName = exercise.name.toString(),
                        targetWeight = 0f,
                        reps = 10,
                        sets = 3,
                        restTimeSeconds = 60,
                        notes = exercise.description?.toString(),
                        imageUrl = exercise.imageUrl,
                        videoUrl = exercise.videoUrl,
                        customSets = emptyList()
                    )
                }
                ReduceResult.stateOnly(
                    currentState.copy(
                        exercises = currentState.exercises + newExercises,
                        hasUnsavedChanges = true,
                        showExerciseSelector = false
                    )
                )
            }

            is TemplateEditContract.Intent.UpdateExercise -> {
                val updatedExercises = currentState.exercises.map { exercise ->
                    if (exercise.id == intent.exercise.id) intent.exercise else exercise
                }
                ReduceResult.stateOnly(
                    currentState.copy(
                        exercises = updatedExercises,
                        hasUnsavedChanges = true
                    )
                )
            }

            is TemplateEditContract.Intent.RemoveExercise -> {
                val updatedExercises = currentState.exercises.filter { it.id != intent.exerciseId }
                ReduceResult.stateOnly(
                    currentState.copy(
                        exercises = updatedExercises,
                        hasUnsavedChanges = true
                    )
                )
            }

            is TemplateEditContract.Intent.ReorderExercises -> {
                val updatedExercises = currentState.exercises.toMutableList()
                if (intent.fromIndex in updatedExercises.indices && intent.toIndex in updatedExercises.indices) {
                    val item = updatedExercises.removeAt(intent.fromIndex)
                    updatedExercises.add(intent.toIndex, item)
                }
                ReduceResult.stateOnly(
                    currentState.copy(
                        exercises = updatedExercises,
                        hasUnsavedChanges = true
                    )
                )
            }

            // UI状态控制
            is TemplateEditContract.Intent.ToggleExerciseSelector -> {
                ReduceResult.stateOnly(
                    currentState.copy(showExerciseSelector = !currentState.showExerciseSelector)
                )
            }

            is TemplateEditContract.Intent.NavigateBack -> {
                ReduceResult.withEffect(
                    newState = currentState,
                    effect = TemplateEditContract.Effect.NavigateBack
                )
            }

            is TemplateEditContract.Intent.ShowPreview -> {
                ReduceResult.withEffect(
                    newState = currentState,
                    effect = TemplateEditContract.Effect.NavigateToPreview
                )
            }

            // 内部结果 Intent
            is TemplateEditContract.Intent.LoadTemplateResult -> {
                when (val result = intent.result) {
                    is com.example.gymbro.core.error.types.ModernResult.Success -> {
                        val template = result.data
                        val exercises = template?.exercises?.map { exercise ->
                            TemplateExerciseDto(
                                id = exercise.id,
                                exerciseId = exercise.exerciseId,
                                exerciseName = exercise.name,
                                targetWeight = exercise.weight ?: 0f,
                                reps = exercise.reps,
                                sets = exercise.sets,
                                restTimeSeconds = exercise.restSeconds,
                                notes = exercise.notes,
                                imageUrl = exercise.imageUrl,
                                videoUrl = exercise.videoUrl,
                                customSets = exercise.customSets.map { set ->
                                    com.example.gymbro.shared.models.workout.TemplateSetDto(
                                        setNumber = set.setNumber,
                                        targetWeight = set.targetWeight,
                                        targetReps = set.targetReps,
                                        restTimeSeconds = set.restTimeSeconds,
                                        targetDuration = set.targetDuration,
                                        rpe = set.rpe
                                    )
                                }
                            )
                        } ?: emptyList()

                        ReduceResult.stateOnly(
                            currentState.copy(
                                template = template,
                                exercises = exercises,
                                isLoading = false,
                                error = null,
                                hasUnsavedChanges = false
                            )
                        )
                    }
                    is com.example.gymbro.core.error.types.ModernResult.Error -> {
                        ReduceResult.stateOnly(
                            currentState.copy(
                                isLoading = false,
                                error = result.error.uiMessage
                            )
                        )
                    }
                    is com.example.gymbro.core.error.types.ModernResult.Loading -> {
                        ReduceResult.stateOnly(currentState.copy(isLoading = true))
                    }
                }
            }

            is TemplateEditContract.Intent.SaveTemplateResult -> {
                when (val result = intent.result) {
                    is com.example.gymbro.core.error.types.ModernResult.Success -> {
                        ReduceResult.withEffect(
                            newState = currentState.copy(
                                isSaving = false,
                                hasUnsavedChanges = false,
                                error = null
                            ),
                            effect = TemplateEditContract.Effect.SaveSuccess(result.data)
                        )
                    }
                    is com.example.gymbro.core.error.types.ModernResult.Error -> {
                        val errorMessage = result.error.uiMessage ?: UiText.DynamicString("保存失败")
                        ReduceResult.withEffect(
                            newState = currentState.copy(
                                isSaving = false,
                                error = errorMessage
                            ),
                            effect = TemplateEditContract.Effect.ShowError(errorMessage)
                        )
                    }
                    is com.example.gymbro.core.error.types.ModernResult.Loading -> {
                        ReduceResult.stateOnly(currentState.copy(isSaving = true))
                    }
                }
            }

            is TemplateEditContract.Intent.DeleteTemplateResult -> {
                when (val result = intent.result) {
                    is com.example.gymbro.core.error.types.ModernResult.Success -> {
                        ReduceResult.withEffect(
                            newState = currentState.copy(isLoading = false, error = null),
                            effect = TemplateEditContract.Effect.NavigateBack
                        )
                    }
                    is com.example.gymbro.core.error.types.ModernResult.Error -> {
                        val errorMessage = result.error.uiMessage ?: UiText.DynamicString("删除失败")
                        ReduceResult.withEffect(
                            newState = currentState.copy(
                                isLoading = false,
                                error = errorMessage
                            ),
                            effect = TemplateEditContract.Effect.ShowError(errorMessage)
                        )
                    }
                    is com.example.gymbro.core.error.types.ModernResult.Loading -> {
                        ReduceResult.stateOnly(currentState.copy(isLoading = true))
                    }
                }
            }

            // === 新增的Intent处理 ===
            is TemplateEditContract.Intent.CreateAndSaveImmediately -> {
                ReduceResult.stateOnly(currentState.copy(isSaving = true, error = null))
            }

            is TemplateEditContract.Intent.SetTemplate -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        template = intent.template,
                        templateName = intent.template.name,
                        templateDescription = intent.template.description ?: ""
                    )
                )
            }

            is TemplateEditContract.Intent.SetCurrentUserId -> {
                ReduceResult.stateOnly(currentState.copy(currentUserId = intent.userId))
            }

            is TemplateEditContract.Intent.SetVersionHistory -> {
                ReduceResult.stateOnly(currentState.copy(versionHistory = intent.versions))
            }

            is TemplateEditContract.Intent.ShowExerciseSelector -> {
                ReduceResult.stateOnly(currentState.copy(showExerciseSelector = true))
            }

            is TemplateEditContract.Intent.ToggleExerciseSelector -> {
                ReduceResult.stateOnly(currentState.copy(showExerciseSelector = !currentState.showExerciseSelector))
            }

            is TemplateEditContract.Intent.NavigateBack -> {
                ReduceResult.withEffect(
                    newState = currentState,
                    effect = TemplateEditContract.Effect.NavigateBack
                )
            }

            is TemplateEditContract.Intent.PrepareToExit -> {
                ReduceResult.stateOnly(currentState)
            }

            is TemplateEditContract.Intent.ShowPreview -> {
                ReduceResult.withEffect(
                    newState = currentState,
                    effect = TemplateEditContract.Effect.NavigateToPreview
                )
            }

            is TemplateEditContract.Intent.ResetNavigationState -> {
                ReduceResult.stateOnly(currentState)
            }

            is TemplateEditContract.Intent.ClearError -> {
                ReduceResult.stateOnly(currentState.copy(error = null))
            }

            is TemplateEditContract.Intent.HandleError -> {
                ReduceResult.stateOnly(currentState.copy(error = intent.error))
            }

            // === 对话框管理 ===
            is TemplateEditContract.Intent.ShowTemplateNameDialog -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        showTemplateNameDialog = true,
                        tempTemplateName = currentState.templateName
                    )
                )
            }

            is TemplateEditContract.Intent.ShowTemplateDescriptionDialog -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        showTemplateDescriptionDialog = true,
                        tempTemplateDescription = currentState.templateDescription
                    )
                )
            }

            is TemplateEditContract.Intent.DismissDialog -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        showTemplateNameDialog = false,
                        showTemplateDescriptionDialog = false,
                        tempTemplateName = "",
                        tempTemplateDescription = ""
                    )
                )
            }

            is TemplateEditContract.Intent.UpdateTempTemplateName -> {
                ReduceResult.stateOnly(currentState.copy(tempTemplateName = intent.name))
            }

            is TemplateEditContract.Intent.UpdateTempTemplateDescription -> {
                ReduceResult.stateOnly(currentState.copy(tempTemplateDescription = intent.description))
            }

            is TemplateEditContract.Intent.ConfirmTemplateName -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        templateName = currentState.tempTemplateName,
                        showTemplateNameDialog = false,
                        tempTemplateName = "",
                        hasUnsavedChanges = true
                    )
                )
            }

            is TemplateEditContract.Intent.ConfirmTemplateDescription -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        templateDescription = currentState.tempTemplateDescription,
                        showTemplateDescriptionDialog = false,
                        tempTemplateDescription = "",
                        hasUnsavedChanges = true
                    )
                )
            }

            // === 版本管理 ===
            is TemplateEditContract.Intent.ShowVersionHistory -> {
                ReduceResult.stateOnly(currentState.copy(showVersionHistory = true))
            }

            is TemplateEditContract.Intent.HideVersionHistory -> {
                ReduceResult.stateOnly(currentState.copy(showVersionHistory = false))
            }

            is TemplateEditContract.Intent.RestoreFromVersion -> {
                ReduceResult.stateOnly(currentState.copy(isRestoringVersion = true))
            }

            is TemplateEditContract.Intent.VersionCreated -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        isCreatingVersion = false,
                        currentVersion = currentState.currentVersion + 1
                    )
                )
            }

            // === 快速操作 ===
            is TemplateEditContract.Intent.ShowQuickActions -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        showQuickActions = true,
                        quickActionTargetId = intent.exerciseId
                    )
                )
            }

            is TemplateEditContract.Intent.HideQuickActions -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        showQuickActions = false,
                        quickActionTargetId = null
                    )
                )
            }

            // === 状态管理 ===

            is TemplateEditContract.Intent.SaveSuccess -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        isSaving = false,
                        hasUnsavedChanges = false
                    )
                )
            }

            is TemplateEditContract.Intent.DraftSaved -> {
                ReduceResult.withEffect(
                    newState = currentState.copy(
                        isSaving = false,
                        hasUnsavedChanges = false
                    ),
                    effect = TemplateEditContract.Effect.ShowDraftSaved
                )
            }

            is TemplateEditContract.Intent.PublishCompleted -> {
                ReduceResult.withEffect(
                    newState = currentState.copy(
                        isSaving = false,
                        hasUnsavedChanges = false,
                        lastPublishedAt = System.currentTimeMillis()
                    ),
                    effect = TemplateEditContract.Effect.ShowTemplatePublished
                )
            }

            // === 添加缺失的AddExercise Intent ===
            is TemplateEditContract.Intent.AddExercise -> {
                val newExercise = TemplateExerciseDto(
                    id = java.util.UUID.randomUUID().toString(),
                    exerciseId = intent.exercise.id,
                    exerciseName = intent.exercise.name.toString(),
                    targetWeight = 0f,
                    reps = 10,
                    sets = 3,
                    restTimeSeconds = 60,
                    notes = intent.exercise.description?.toString(),
                    imageUrl = intent.exercise.imageUrl,
                    videoUrl = intent.exercise.videoUrl,
                    customSets = emptyList()
                )
                ReduceResult.stateOnly(
                    currentState.copy(
                        exercises = currentState.exercises + newExercise,
                        hasUnsavedChanges = true
                    )
                )
            }

            // === 快速操作 ===
            is TemplateEditContract.Intent.QuickDuplicateExercise -> {
                val exerciseToClone = currentState.exercises.find { it.id == intent.exerciseId }
                if (exerciseToClone != null) {
                    val clonedExercise = exerciseToClone.copy(
                        id = java.util.UUID.randomUUID().toString()
                    )
                    ReduceResult.stateOnly(
                        currentState.copy(
                            exercises = currentState.exercises + clonedExercise,
                            hasUnsavedChanges = true
                        )
                    )
                } else {
                    ReduceResult.stateOnly(currentState)
                }
            }

            is TemplateEditContract.Intent.QuickDeleteExercise -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        exercises = currentState.exercises.filter { it.id != intent.exerciseId },
                        hasUnsavedChanges = true
                    )
                )
            }

            // === 拖拽操作 ===
            is TemplateEditContract.Intent.StartDrag -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        isDragInProgress = true,
                        draggedExerciseId = intent.exerciseId,
                        draggedItemIndex = intent.startIndex
                    )
                )
            }

            is TemplateEditContract.Intent.UpdateDragPosition -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        dragTargetIndex = intent.targetIndex,
                        dragOffset = intent.offset
                    )
                )
            }

            is TemplateEditContract.Intent.CompleteDrag -> {
                val reorderedExercises = currentState.exercises.toMutableList()
                if (intent.fromIndex in reorderedExercises.indices && intent.toIndex in reorderedExercises.indices) {
                    val item = reorderedExercises.removeAt(intent.fromIndex)
                    reorderedExercises.add(intent.toIndex, item)
                }
                ReduceResult.stateOnly(
                    currentState.copy(
                        exercises = reorderedExercises,
                        isDragInProgress = false,
                        draggedExerciseId = null,
                        draggedItemIndex = -1,
                        dragTargetIndex = -1,
                        dragOffset = 0f,
                        hasUnsavedChanges = true
                    )
                )
            }

            is TemplateEditContract.Intent.CancelDrag -> {
                ReduceResult.stateOnly(
                    currentState.copy(
                        isDragInProgress = false,
                        draggedExerciseId = null,
                        draggedItemIndex = -1,
                        dragTargetIndex = -1,
                        dragOffset = 0f
                    )
                )
            }


        }
    }
}
