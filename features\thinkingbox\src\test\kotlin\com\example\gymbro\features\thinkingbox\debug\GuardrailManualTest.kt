import com.example.gymbro.features.thinkingbox.domain.guardrail.ThinkingMLGuardrail

fun main() {
    val guardrail = ThinkingMLGuardrail()
    
    // 测试非法标签删除 - 符合大纲要求
    val input = "<phase:PLAN>这是计划阶段</phase:PLAN>"
    val result = guardrail.validateAndFix(input)
    
    println("输入: $input")
    println("输出: ${result.content}")
    println("有修复: ${result.hasFixes()}")
    println("修复列表: ${result.fixes.map { it.description }}")
    
    // 测试期望结果 - 应该删除非法标签，只保留文本
    val expectedContentClean = result.content.contains("这是计划阶段") && !result.content.contains("phase:")
    println("正确删除非法标签，保留文本: $expectedContentClean")
    
    // 测试 think 转换
    val input2 = "<think>思考内容</think>"
    val result2 = guardrail.validateAndFix(input2)
    
    println("\n输入2: $input2")
    println("输出2: ${result2.content}")
    println("正确转换为thinking: ${result2.content.contains("<thinking>")}")
    
    // 测试复杂场景
    val input3 = "<think><phase:ANALYSIS>分析内容</phase:ANALYSIS></think>"
    val result3 = guardrail.validateAndFix(input3)
    
    println("\n输入3: $input3")
    println("输出3: ${result3.content}")
    println("正确清理: ${result3.content.contains("分析内容") && !result3.content.contains("phase:")}")
}