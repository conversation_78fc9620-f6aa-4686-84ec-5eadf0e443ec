package com.example.gymbro.features.thinkingbox.internal.reducer

import com.example.gymbro.core.arch.mvi.BaseReducer
import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.features.thinkingbox.domain.model.Segment
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import timber.log.Timber
import java.util.ArrayDeque
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ThinkingBoxReducer - 标准MVI架构Reducer
 *
 * 🎯 核心职责：
 * - 实现标准MVI Reducer接口
 * - 处理ThinkingBoxContract.Intent并返回ReduceResult
 * - 管理Segment队列状态转换
 * - 支持History写入Effect生成
 *
 * 🔥 架构特点：
 * - 纯函数状态转换，符合MVI规范
 * - 集成SegmentQueueReducer的核心逻辑
 * - 支持标准Intent/State/Effect模式
 * - 完整的错误处理和日志记录
 */
@Singleton
class ThinkingBoxReducer @Inject constructor(
    private val segmentQueueReducer: SegmentQueueReducer
) : BaseReducer<ThinkingBoxContract.Intent, ThinkingBoxContract.State, ThinkingBoxContract.Effect>() {

    // 内部状态管理 - 使用SegmentQueueReducer.TBState
    private var internalState = SegmentQueueReducer.TBState()

    override fun reduceInternal(
        intent: ThinkingBoxContract.Intent,
        currentState: ThinkingBoxContract.State
    ): ReduceResult<ThinkingBoxContract.State, ThinkingBoxContract.Effect> {
        
        Timber.tag("TB-REDUCER").d("🔄 [Intent处理] ${intent::class.simpleName}")

        return when (intent) {
            is ThinkingBoxContract.Intent.Initialize -> handleInitialize(intent, currentState)
            is ThinkingBoxContract.Intent.Reset -> handleReset(currentState)
            is ThinkingBoxContract.Intent.UiSegmentRendered -> handleUiSegmentRendered(intent, currentState)
            is ThinkingBoxContract.Intent.ClearError -> handleClearError(currentState)
        }
    }

    /**
     * 处理初始化Intent
     */
    private fun handleInitialize(
        intent: ThinkingBoxContract.Intent.Initialize,
        currentState: ThinkingBoxContract.State
    ): ReduceResult<ThinkingBoxContract.State, ThinkingBoxContract.Effect> {
        
        Timber.tag("TB-INIT").i("🚀 [初始化] ThinkingBox: messageId=${intent.messageId}")

        // 防止重复初始化
        if (internalState.messageId == intent.messageId) {
            Timber.tag("TB-INIT").d("⏭️ [跳过重复] 初始化: ${intent.messageId}")
            return ReduceResult.noChange(currentState)
        }

        // 重置内部状态
        internalState = SegmentQueueReducer.TBState(messageId = intent.messageId)

        // 更新Contract State
        val newState = convertToContractState(internalState)
        
        // 启动Token流监听Effect
        val effect = ThinkingBoxContract.Effect.StartTokenStreamListening(intent.messageId)
        
        return ReduceResult.withEffect(newState, effect)
    }

    /**
     * 处理重置Intent
     */
    private fun handleReset(
        currentState: ThinkingBoxContract.State
    ): ReduceResult<ThinkingBoxContract.State, ThinkingBoxContract.Effect> {
        
        Timber.i("TB-Reducer: 🔄 重置状态")

        // 重置内部状态
        internalState = SegmentQueueReducer.TBState()

        // 更新Contract State
        val newState = convertToContractState(internalState)
        
        return ReduceResult.stateOnly(newState)
    }

    /**
     * 处理UI段渲染完成Intent
     */
    private fun handleUiSegmentRendered(
        intent: ThinkingBoxContract.Intent.UiSegmentRendered,
        currentState: ThinkingBoxContract.State
    ): ReduceResult<ThinkingBoxContract.State, ThinkingBoxContract.Effect> {
        
        Timber.d("TB-Reducer: 📤 段渲染完成: ${intent.segmentId}")

        // 创建ThinkingEvent并处理
        val event = ThinkingEvent.UiSegmentRendered(intent.segmentId)
        val reduceResult = segmentQueueReducer.reduce(internalState, event)
        
        // 更新内部状态
        internalState = reduceResult.state

        // 转换为Contract State
        val newState = convertToContractState(internalState)
        
        // 转换Effects
        val contractEffects = reduceResult.effects
        
        return ReduceResult.withEffects(newState, contractEffects)
    }

    /**
     * 处理清除错误Intent
     */
    private fun handleClearError(
        currentState: ThinkingBoxContract.State
    ): ReduceResult<ThinkingBoxContract.State, ThinkingBoxContract.Effect> {
        
        val newState = currentState.copy(error = null)
        return ReduceResult.stateOnly(newState)
    }

    /**
     * 处理ThinkingEvent - 供ViewModel调用
     * 
     * 这个方法允许ViewModel直接处理来自DomainMapper的ThinkingEvent
     */
    fun handleThinkingEvent(
        event: ThinkingEvent,
        currentState: ThinkingBoxContract.State
    ): ReduceResult<ThinkingBoxContract.State, ThinkingBoxContract.Effect> {
        
        // 使用SegmentQueueReducer处理事件
        val reduceResult = segmentQueueReducer.reduce(internalState, event)
        
        // 更新内部状态
        internalState = reduceResult.state

        // 转换为Contract State
        val newState = convertToContractState(internalState)
        
        // 转换Effects
        val contractEffects = reduceResult.effects
        
        return ReduceResult.withEffects(newState, contractEffects)
    }

    /**
     * 将内部TBState转换为Contract State
     */
    private fun convertToContractState(tbState: SegmentQueueReducer.TBState): ThinkingBoxContract.State {
        // 🔥 【单一队列】所有段都加入segmentsQueue，供LazyColumn渲染
        val allSegments = buildList {
            // 添加队列中的段
            tbState.queue.forEach { segment ->
                add(convertToSegmentUi(segment))
            }
            // 添加当前段（如果已闭合且非空）
            tbState.current?.let { current ->
                if (current.closed && current.getTextContent().isNotEmpty()) {
                    add(convertToSegmentUi(current))
                }
            }
        }
        
        return ThinkingBoxContract.State(
            messageId = tbState.messageId ?: "",
            segmentsQueue = allSegments, // LazyColumn直接渲染这个队列
            finalReady = tbState.isFinalReadyToRender(),
            finalContent = tbState.getFinalContent(),
            thinkingClosed = tbState.thinkingClosed,
            isLoading = false
        )
    }

    /**
     * 将Domain Segment转换为Contract SegmentUi
     */
    private fun convertToSegmentUi(segment: Segment): ThinkingBoxContract.SegmentUi {
        return ThinkingBoxContract.SegmentUi(
            id = segment.id,
            kind = segment.kind,
            title = segment.title,
            content = segment.getTextContent(),
            isComplete = segment.closed,
            isRendered = segment.rendered, // 使用Domain模型的rendered字段
        )
    }

    /**
     * 获取当前内部状态摘要（调试用）
     */
    fun getInternalStateSummary(): String {
        return internalState.getSummary()
    }
}