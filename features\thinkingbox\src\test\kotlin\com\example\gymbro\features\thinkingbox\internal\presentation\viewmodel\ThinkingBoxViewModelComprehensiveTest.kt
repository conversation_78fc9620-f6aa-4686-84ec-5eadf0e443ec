package com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel

// TokenRouter已删除，新架构不再需要
import com.example.gymbro.features.thinkingbox.domain.mapper.DomainMapper
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.parser.StreamingThinkingMLParser
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.reducer.SegmentQueueReducer
import io.mockk.*
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.test.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * ThinkingBoxViewModel 综合测试
 *
 * 🎯 测试覆盖：
 * - ViewModel生命周期管理
 * - Token流处理和状态更新
 * - Effect分发机制
 * - UI交互处理
 * - 错误处理和恢复
 */
@DisplayName("ThinkingBoxViewModel 综合测试")
class ThinkingBoxViewModelComprehensiveTest {

    private val mockSegmentQueueReducer = mockk<SegmentQueueReducer>(relaxed = true)
    private val mockDomainMapper = mockk<DomainMapper>(relaxed = true)
    private val mockStreamingParser = mockk<StreamingThinkingMLParser>(relaxed = true)
    // TokenRouter已删除，新架构不再需要

    private lateinit var viewModel: ThinkingBoxViewModel
    private val testScope = TestScope()

    @BeforeEach
    fun setUp() {
        clearAllMocks()

        // 设置默认mock行为
        every { mockSegmentQueueReducer.reduce(any(), any()) } returns SegmentQueueReducer.ReduceResult(
            state = SegmentQueueReducer.TBState(),
            effects = emptyList()
        )

        every { mockDomainMapper.mapSemanticToThinking(any(), any()) } returns DomainMapper.MappingResult(
            events = emptyList(),
            context = DomainMapper.MappingContext()
        )

        viewModel = ThinkingBoxViewModel(
            segmentQueueReducer = mockSegmentQueueReducer,
            domainMapper = mockDomainMapper,
            streamingParser = mockStreamingParser,
            tokenRouter = mockTokenRouter
        )
    }

    @Nested
    @DisplayName("初始化测试")
    inner class InitializationTests {

        @Test
        @DisplayName("initialize应该设置messageId并启动Token流监听")
        fun `initialize should set messageId and start token stream listening`() = testScope.runTest {
            // Given
            val messageId = "test-message-123"
            val tokenFlow = flowOf("token1", "token2", "token3")
            every { mockTokenRouter.getTokens(messageId) } returns tokenFlow

            // When
            viewModel.initialize(messageId)
            advanceUntilIdle()

            // Then
            verify { mockTokenRouter.getTokens(messageId) }
            verify { mockStreamingParser.parseTokens(any(), any()) }
        }

        @Test
        @DisplayName("重复初始化相同messageId应该被跳过")
        fun `duplicate initialization with same messageId should be skipped`() = testScope.runTest {
            // Given
            val messageId = "duplicate-test"
            val tokenFlow = flowOf("token")
            every { mockTokenRouter.getTokens(messageId) } returns tokenFlow

            // When
            viewModel.initialize(messageId)
            viewModel.initialize(messageId) // 重复初始化
            advanceUntilIdle()

            // Then - 只应该调用一次
            verify(exactly = 1) { mockTokenRouter.getTokens(messageId) }
        }

        @Test
        @DisplayName("初始化不同messageId应该重置状态")
        fun `initialization with different messageId should reset state`() = testScope.runTest {
            // Given
            val messageId1 = "message-1"
            val messageId2 = "message-2"
            val tokenFlow = flowOf("token")
            every { mockTokenRouter.getTokens(any()) } returns tokenFlow

            // When
            viewModel.initialize(messageId1)
            advanceUntilIdle()
            viewModel.initialize(messageId2)
            advanceUntilIdle()

            // Then
            verify { mockTokenRouter.getTokens(messageId1) }
            verify { mockTokenRouter.getTokens(messageId2) }
        }
    }

    @Nested
    @DisplayName("Token流处理测试")
    inner class TokenStreamProcessingTests {

        @Test
        @DisplayName("Token流应该被正确处理和解析")
        fun `token stream should be processed and parsed correctly`() = testScope.runTest {
            // Given
            val messageId = "token-processing-test"
            val tokens = listOf("<think>", "思考内容", "</think>")
            val tokenFlow = tokens.asFlow()
            every { mockTokenRouter.getTokens(messageId) } returns tokenFlow

            val parseSlot = slot<(Any) -> Unit>()
            every { mockStreamingParser.parseTokens(any(), capture(parseSlot)) } just Runs

            // When
            viewModel.initialize(messageId)
            advanceUntilIdle()

            // Then
            verify { mockStreamingParser.parseTokens(tokens.joinToString(""), any()) }
        }

        @Test
        @DisplayName("Token流错误应该被捕获并处理")
        fun `token stream errors should be caught and handled`() = testScope.runTest {
            // Given
            val messageId = "error-test"
            val errorFlow = flow<String> { throw RuntimeException("Token流错误") }
            every { mockTokenRouter.getTokens(messageId) } returns errorFlow

            // When & Then - 不应该崩溃
            viewModel.initialize(messageId)
            advanceUntilIdle()

            // 应该发出错误Effect
            val currentEffect = viewModel.effect.value
            assertTrue(currentEffect is ThinkingBoxContract.Effect.ShowError)
        }
    }

    @Nested
    @DisplayName("状态管理测试")
    inner class StateManagementTests {

        @Test
        @DisplayName("状态更新应该触发UI刷新")
        fun `state updates should trigger UI refresh`() = testScope.runTest {
            // Given
            val newState = SegmentQueueReducer.TBState(messageId = "state-test")
            every { mockSegmentQueueReducer.reduce(any(), any()) } returns SegmentQueueReducer.ReduceResult(
                state = newState,
                effects = emptyList()
            )

            val messageId = "state-update-test"
            val tokenFlow = flowOf("<think>内容</think>")
            every { mockTokenRouter.getTokens(messageId) } returns tokenFlow

            // When
            viewModel.initialize(messageId)
            advanceUntilIdle()

            // Then - 状态应该被更新
            val currentState = viewModel.state.value
            // 验证状态转换是否正确（这里需要根据实际的convertToContractState实现）
            assertTrue(currentState != ThinkingBoxContract.State())
        }

        @Test
        @DisplayName("Effect应该被正确分发")
        fun `effects should be dispatched correctly`() = testScope.runTest {
            // Given
            val testEffect = ThinkingBoxContract.Effect.ScrollToBottom
            every { mockSegmentQueueReducer.reduce(any(), any()) } returns SegmentQueueReducer.ReduceResult(
                state = SegmentQueueReducer.TBState(),
                effects = listOf(testEffect)
            )

            val messageId = "effect-test"
            val tokenFlow = flowOf("token")
            every { mockTokenRouter.getTokens(messageId) } returns tokenFlow

            // When
            viewModel.initialize(messageId)
            advanceUntilIdle()

            // Then - Effect应该被发出
            assertEquals(testEffect, viewModel.effect.value)
        }
    }

    @Nested
    @DisplayName("UI交互测试")
    inner class UIInteractionTests {

        @Test
        @DisplayName("onSegmentRendered应该处理UI回调")
        fun `onSegmentRendered should handle UI callback`() = testScope.runTest {
            // Given
            val segmentId = "test-segment"

            // When
            viewModel.onSegmentRendered(segmentId)

            // Then - 应该处理UiSegmentRendered事件
            verify { mockSegmentQueueReducer.reduce(any(), any()) }
        }

        @Test
        @DisplayName("reset应该清理所有状态")
        fun `reset should clean all states`() = testScope.runTest {
            // Given - 先初始化
            val messageId = "reset-test"
            val tokenFlow = flowOf("token")
            every { mockTokenRouter.getTokens(messageId) } returns tokenFlow
            viewModel.initialize(messageId)
            advanceUntilIdle()

            // When
            viewModel.reset()

            // Then - 状态应该被重置
            val currentState = viewModel.state.value
            assertEquals(ThinkingBoxContract.State(), currentState)
        }
    }

    @Nested
    @DisplayName("生命周期测试")
    inner class LifecycleTests {

        @Test
        @DisplayName("onCleared应该取消所有协程作业")
        fun `onCleared should cancel all coroutine jobs`() = testScope.runTest {
            // Given
            val messageId = "lifecycle-test"
            val longRunningFlow = flow {
                repeat(100) {
                    emit("token-$it")
                    kotlinx.coroutines.delay(1000) // 长时间延迟
                }
            }
            every { mockTokenRouter.getTokens(messageId) } returns longRunningFlow

            viewModel.initialize(messageId)
            advanceTimeBy(100) // 让流开始

            // When - 模拟ViewModel被清理
            // 注意：由于我们无法直接调用onCleared（它是protected），
            // 我们通过取消testScope来模拟清理行为
            testScope.cancel()

            // Then - 流应该被取消，不会继续处理
            // 这里主要验证没有内存泄漏或异常
        }
    }

    @Nested
    @DisplayName("性能测试")
    inner class PerformanceTests {

        @Test
        @DisplayName("大量Token应该高效处理")
        fun `large amount of tokens should be processed efficiently`() = testScope.runTest {
            // Given
            val messageId = "performance-test"
            val largeTokenCount = 10000
            val tokens = (1..largeTokenCount).map { "token$it" }
            val tokenFlow = tokens.asFlow()
            every { mockTokenRouter.getTokens(messageId) } returns tokenFlow

            // When
            val startTime = System.currentTimeMillis()
            viewModel.initialize(messageId)
            advanceUntilIdle()
            val endTime = System.currentTimeMillis()

            // Then - 应该在合理时间内完成
            assertTrue(endTime - startTime < 5000) // 5秒内完成
            verify { mockStreamingParser.parseTokens(any(), any()) }
        }

        @Test
        @DisplayName("频繁的UI回调应该高效处理")
        fun `frequent UI callbacks should be handled efficiently`() = testScope.runTest {
            // Given
            val segmentIds = (1..1000).map { "segment-$it" }

            // When
            val startTime = System.currentTimeMillis()
            segmentIds.forEach { segmentId ->
                viewModel.onSegmentRendered(segmentId)
            }
            val endTime = System.currentTimeMillis()

            // Then - 应该快速完成
            assertTrue(endTime - startTime < 1000) // 1秒内完成
            verify(exactly = segmentIds.size) { mockSegmentQueueReducer.reduce(any(), any()) }
        }
    }

    @Nested
    @DisplayName("边界情况测试")
    inner class EdgeCaseTests {

        @Test
        @DisplayName("空Token流应该被正确处理")
        fun `empty token stream should be handled correctly`() = testScope.runTest {
            // Given
            val messageId = "empty-stream-test"
            val emptyFlow = emptyFlow<String>()
            every { mockTokenRouter.getTokens(messageId) } returns emptyFlow

            // When & Then - 不应该崩溃
            viewModel.initialize(messageId)
            advanceUntilIdle()
        }

        @Test
        @DisplayName("null或空messageId应该被正确处理")
        fun `null or empty messageId should be handled correctly`() = testScope.runTest {
            // When & Then - 不应该崩溃
            viewModel.initialize("")
            viewModel.initialize("   ") // 空白字符串
            advanceUntilIdle()
        }

        @Test
        @DisplayName("重复的onSegmentRendered调用应该被正确处理")
        fun `duplicate onSegmentRendered calls should be handled correctly`() = testScope.runTest {
            // Given
            val segmentId = "duplicate-segment"

            // When
            viewModel.onSegmentRendered(segmentId)
            viewModel.onSegmentRendered(segmentId)
            viewModel.onSegmentRendered(segmentId)

            // Then - 应该处理所有调用
            verify(exactly = 3) { mockSegmentQueueReducer.reduce(any(), any()) }
        }
    }
}
