package com.example.gymbro.core.network.buffer

import timber.log.Timber
import java.util.ArrayDeque
import javax.inject.Inject

/**
 * 🚀 滑动窗口缓冲器 - 高效的流式数据缓冲策略
 * 
 * 设计目标：
 * - 滑动窗口机制，避免大量数据积压
 * - 批量处理，提高吞吐量
 * - 内存友好，自动清理过期数据
 * - 支持背压控制
 */
class SlidingWindowBuffer<T>(
    private val windowSize: Int = 64,      // 窗口大小：64个token
    private val slideStep: Int = 16,       // 滑动步长：16个token
    private val maxRetentionMs: Long = 5000 // 最大保留时间：5秒
) {
    companion object {
        private const val TAG = "SlidingWindowBuffer"
    }
    
    private val buffer = ArrayDeque<BufferedItem<T>>(windowSize)
    private var processedCount = 0L
    private var droppedCount = 0L
    private var lastSlideTime = System.currentTimeMillis()
    
    /**
     * 添加新项目到缓冲区
     * 
     * @param item 要缓冲的项目
     * @return 如果窗口满了，返回需要处理的项目列表；否则返回空列表
     */
    @Synchronized
    fun add(item: T): List<T> {
        val currentTime = System.currentTimeMillis()
        val bufferedItem = BufferedItem(item, currentTime)
        
        // 清理过期项目
        cleanupExpiredItems(currentTime)
        
        buffer.addLast(bufferedItem)
        
        // 检查是否需要滑动窗口
        return if (buffer.size >= windowSize) {
            slideWindow()
        } else {
            emptyList()
        }
    }
    
    /**
     * 强制刷新缓冲区，返回所有剩余项目
     * 
     * @return 缓冲区中的所有项目
     */
    @Synchronized
    fun flush(): List<T> {
        val result = buffer.map { it.item }.toList()
        val flushedCount = buffer.size
        
        buffer.clear()
        processedCount += flushedCount
        
        if (flushedCount > 0) {
            Timber.tag(TAG).d("🔄 强制刷新缓冲区: flushed=$flushedCount, total_processed=$processedCount")
        }
        
        return result
    }
    
    /**
     * 获取当前缓冲区状态
     */
    @Synchronized
    fun getStatus(): BufferStatus {
        val currentTime = System.currentTimeMillis()
        val oldestItemAge = if (buffer.isNotEmpty()) {
            currentTime - buffer.first().timestamp
        } else {
            0L
        }
        
        return BufferStatus(
            currentSize = buffer.size,
            windowSize = windowSize,
            slideStep = slideStep,
            processedCount = processedCount,
            droppedCount = droppedCount,
            oldestItemAgeMs = oldestItemAge,
            utilizationPercent = buffer.size.toFloat() / windowSize
        )
    }
    
    /**
     * 检查缓冲区是否接近满载
     */
    fun isNearFull(threshold: Float = 0.8f): Boolean {
        return buffer.size >= (windowSize * threshold).toInt()
    }
    
    /**
     * 检查缓冲区是否为空
     */
    fun isEmpty(): Boolean = buffer.isEmpty()
    
    /**
     * 获取当前缓冲区大小
     */
    fun size(): Int = buffer.size
    
    /**
     * 清空缓冲区
     */
    @Synchronized
    fun clear() {
        val clearedCount = buffer.size
        buffer.clear()
        
        if (clearedCount > 0) {
            Timber.tag(TAG).d("🧹 清空缓冲区: cleared=$clearedCount")
        }
    }
    
    /**
     * 滑动窗口，返回需要处理的项目
     */
    private fun slideWindow(): List<T> {
        val currentTime = System.currentTimeMillis()
        val timeSinceLastSlide = currentTime - lastSlideTime
        
        // 计算实际滑动步长（可能根据时间调整）
        val actualSlideStep = if (timeSinceLastSlide > maxRetentionMs) {
            // 如果距离上次滑动时间过长，增加滑动步长
            minOf(slideStep * 2, buffer.size)
        } else {
            minOf(slideStep, buffer.size)
        }
        
        val result = mutableListOf<T>()
        
        // 取出要处理的项目
        repeat(actualSlideStep) {
            if (buffer.isNotEmpty()) {
                result.add(buffer.removeFirst().item)
            }
        }
        
        processedCount += result.size
        lastSlideTime = currentTime
        
        if (result.isNotEmpty()) {
            Timber.tag(TAG).v("📤 滑动窗口: processed=${result.size}, " +
                    "remaining=${buffer.size}, total_processed=$processedCount")
        }
        
        return result
    }
    
    /**
     * 清理过期项目
     */
    private fun cleanupExpiredItems(currentTime: Long) {
        var cleanedCount = 0
        
        while (buffer.isNotEmpty()) {
            val oldestItem = buffer.first()
            val age = currentTime - oldestItem.timestamp
            
            if (age > maxRetentionMs) {
                buffer.removeFirst()
                cleanedCount++
                droppedCount++
            } else {
                break // 队列是按时间顺序的，后面的项目都不会过期
            }
        }
        
        if (cleanedCount > 0) {
            Timber.tag(TAG).w("🗑️ 清理过期项目: dropped=$cleanedCount, " +
                    "total_dropped=$droppedCount, retention_ms=$maxRetentionMs")
        }
    }
}

/**
 * 📦 缓冲项目包装器
 */
private data class BufferedItem<T>(
    val item: T,
    val timestamp: Long
)

/**
 * 📊 缓冲区状态信息
 */
data class BufferStatus(
    val currentSize: Int,           // 当前缓冲区大小
    val windowSize: Int,            // 窗口大小
    val slideStep: Int,             // 滑动步长
    val processedCount: Long,       // 已处理项目总数
    val droppedCount: Long,         // 已丢弃项目总数
    val oldestItemAgeMs: Long,      // 最老项目的年龄（毫秒）
    val utilizationPercent: Float   // 利用率百分比
)

/**
 * 🏭 滑动窗口缓冲器工厂
 */
class SlidingWindowBufferFactory @Inject constructor() {
    
    /**
     * 创建Token专用的滑动窗口缓冲器
     */
    fun createTokenBuffer(): SlidingWindowBuffer<String> {
        return SlidingWindowBuffer(
            windowSize = 64,        // Token窗口：64个
            slideStep = 16,         // Token滑动：16个
            maxRetentionMs = 3000   // Token保留：3秒
        )
    }
    
    /**
     * 创建事件专用的滑动窗口缓冲器
     */
    fun createEventBuffer(): SlidingWindowBuffer<Any> {
        return SlidingWindowBuffer(
            windowSize = 32,        // 事件窗口：32个
            slideStep = 8,          // 事件滑动：8个
            maxRetentionMs = 5000   // 事件保留：5秒
        )
    }
    
    /**
     * 创建自定义配置的滑动窗口缓冲器
     */
    fun <T> createCustomBuffer(
        windowSize: Int,
        slideStep: Int,
        maxRetentionMs: Long
    ): SlidingWindowBuffer<T> {
        require(windowSize > 0) { "窗口大小必须大于0" }
        require(slideStep > 0) { "滑动步长必须大于0" }
        require(slideStep <= windowSize) { "滑动步长不能大于窗口大小" }
        require(maxRetentionMs > 0) { "最大保留时间必须大于0" }
        
        return SlidingWindowBuffer(
            windowSize = windowSize,
            slideStep = slideStep,
            maxRetentionMs = maxRetentionMs
        )
    }
}
