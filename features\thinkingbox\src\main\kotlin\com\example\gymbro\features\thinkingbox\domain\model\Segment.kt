package com.example.gymbro.features.thinkingbox.domain.model

import androidx.compose.runtime.Immutable

/**
 * Segment数据模型 - ThinkingBox队列架构核心
 *
 * 🎯 设计目标：
 * - 实现729方案3.md的Segment队列模型
 * - 支持三类段：PERTHINK、PHASE、FINAL
 * - 管理段的生命周期：创建→填充→闭合→渲染→完成
 * - 确保UI渲染的顺序性和一致性
 *
 * 🔥 架构原则：
 * - 不可变数据类，符合MVI Golden Standard
 * - 简单明确的状态管理
 * - 支持双时序架构：数据流+UI队列
 */

/**
 * 段类型枚举
 * 定义ThinkingBox中不同类型的内容段
 */
enum class SegmentKind {
    /**
     * 预思考段 - 对应<think>或起始纯文本
     * 特点：在Header中显示，用于展示AI的初步思考
     */
    PERTHINK,

    /**
     * 正式阶段段 - 对应<phase id="X">
     * 特点：在思考框中按顺序渲染，包含结构化的思考过程
     */
    PHASE,

    /**
     * 最终段 - 对应</thinking>触发的关闭段
     * 特点：用于关闭思考框，通常内容为空，仅作为UI控制信号
     */
    FINAL,

    /**
     * 🔥 【729方案9强化】最终阶段段 - 对应ParsePhase.FINAL_PHASE
     * 特点：在</thinking>后的阶段，用于标记思考阶段结束
     */
    FINAL_PHASE,
}

/**
 * 段数据模型
 * 表示ThinkingBox中的一个内容段，包含完整的生命周期状态
 *
 * @param id 段的唯一标识符，用于队列管理和UI回调
 * @param kind 段的类型，决定渲染方式和位置
 * @param title 段的标题，可选，用于显示在段的头部
 * @param content 段的文本内容，使用不可变String而非StringBuilder
 * @param closed 段是否已闭合，闭合后不再接收新内容
 * @param rendered 段是否已完成UI渲染，用于队列管理
 */
@Immutable
data class Segment(
    val id: String,
    val kind: SegmentKind,
    val title: String? = null,
    val content: String = "",
    val closed: Boolean = false,
    val rendered: Boolean = false,
) {
    /**
     * 获取段的文本内容
     * 直接返回不可变的content字段
     */
    fun getTextContent(): String = content

    /**
     * 获取段的文本长度
     * 用于性能监控和调试
     */
    fun getTextLength(): Int = content.length

    /**
     * 判断段是否为空
     * 用于优化渲染和队列管理
     */
    fun isEmpty(): Boolean = content.isEmpty()

    /**
     * 判断段是否准备好渲染
     * 只有闭合的段才能进入UI渲染队列
     */
    fun isReadyForRender(): Boolean = closed

    /**
     * 判断段是否可以从队列中移除
     * 已渲染完成的段可以从队列中移除
     */
    fun canBeRemoved(): Boolean = rendered

    /**
     * 添加内容到段中 - 返回新的段实例（不可变更新）
     * 🔥 【MVI修复】替代StringBuilder.append()，维持不可变性
     */
    fun appendContent(newContent: String): Segment = copy(
        content = content + newContent
    )

    /**
     * 标记段为已闭合 - 返回新的段实例（不可变更新）
     */
    fun markClosed(): Segment = copy(closed = true)

    /**
     * 标记段为已渲染 - 返回新的段实例（不可变更新）
     */
    fun markRendered(): Segment = copy(rendered = true)

    companion object {
        /**
         * 创建PERTHINK类型的段
         * 用于处理<think>标签或起始纯文本
         */
        fun createPerthink(content: String = ""): Segment = Segment(
            id = "perthink",
            kind = SegmentKind.PERTHINK,
            title = "Bro is thinking",
            content = content,
        )

        /**
         * 创建PHASE类型的段
         * 用于处理<phase id="X">标签
         */
        fun createPhase(id: String, title: String? = null, content: String = ""): Segment = Segment(
            id = id,
            kind = SegmentKind.PHASE,
            title = title,
            content = content,
        )

        /**
         * 创建FINAL_PHASE类型的段
         * 用于处理</thinking>标签，关闭思考框
         */
        fun createFinalPhase(): Segment = Segment(
            id = "final-phase",
            kind = SegmentKind.FINAL_PHASE,
            title = "Final Answer",
            content = "",
            closed = true, // final-phase段立即闭合
        )
    }
}
