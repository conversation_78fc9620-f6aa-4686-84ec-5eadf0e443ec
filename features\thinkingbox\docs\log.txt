15:36:49.441 TB-COACH                 I  🔍 AwaitingFirstToken状态: 实际AI消息ID=c4234576-5c97-4146-8d33-ebd68e92a1eb
15:36:49.488 TB-VIEWMODEL             D  🚀 ThinkingBoxViewModel 初始化
15:36:49.561 TB-COACH                 I  🚀 ThinkingBox激活: messageId=c4234576-5c97-4146-8d33-ebd68e92a1eb, state=AwaitingFirstToken
15:36:49.562 TB-INIT                  D  🚀 初始化ThinkingBox: c4234576-5c97-4146-8d33-ebd68e92a1eb, hasTokenFlow=false
15:36:49.562 TB-VIEWMODEL             I  🚀 初始化ThinkingBox: messageId=c4234576-5c97-4146-8d33-ebd68e92a1eb
15:36:49.562                          I  🎯 [Token流启动] messageId=c4234576-5c97-4146-8d33-ebd68e92a1eb
15:36:49.563                          I  ✅ [开始处理消息] messageId=c4234576-5c97-4146-8d33-ebd68e92a1eb
15:36:49.565 TB-INIT                  D  🔗 连接HistoryActor到Effect流: c4234576-5c97-4146-8d33-ebd68e92a1eb
15:36:49.572 HISTORY-ACTOR            I  🚀 [初始化] 开始监听ThinkingBox Effect流
15:36:49.618 tflite                   I  Replacing 200 out of 391 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 76 partitions for subgraph 0.
15:36:49.629 TB-COACH                 I  🔍 AwaitingFirstToken状态: 实际AI消息ID=c4234576-5c97-4146-8d33-ebd68e92a1eb
15:36:49.675 .example.gymbro          I  Compiler allocated 7381KB to compile void androidx.compose.material3.internal.TextFieldImplKt.CommonDecorationBox(androidx.compose.material3.internal.TextFieldType, java.lang.String, kotlin.jvm.functions.Function2, androidx.compose.ui.text.input.VisualTransformation, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, kotlin.jvm.functions.Function2, boolean, boolean, boolean, androidx.compose.foundation.interaction.InteractionSource, androidx.compose.foundation.layout.PaddingValues, androidx.compose.material3.TextFieldColors, kotlin.jvm.functions.Function2, androidx.compose.runtime.Composer, int, int, int)
15:36:49.957 tflite                   I  Replacing 200 out of 391 node(s) with delegate (TfLiteXNNPackDelegate) node, yielding 76 partitions for subgraph 0.
15:36:50.163 NEW-ARCH                 I  🧹 启动新架构token流路径：messageId=c4234576-5c97-4146-8d33-ebd68e92a1eb
15:36:50.173                          I  🚀 启动新架构AI请求: messageId=c4234576-5c97-4146-8d33-ebd68e92a1eb, taskType=CHAT
15:36:50.179                          I  ✅ AI请求已启动，token流将通过新架构自动路由到ThinkingBox: messageId=c4234576-5c97-4146-8d33-ebd68e92a1eb
15:36:50.179                          I  🚀 新架构token流路径启动完成
15:36:51.230 .example.gymbro          I  Background concurrent mark compact GC freed 18MB AllocSpace bytes, 22(484KB) LOS objects, 20% free, 93MB/117MB, paused 1.011ms,3.612ms total 261.861ms
15:37:25.142 UnifiedTokenReceiver     I  🔄 开始接收Token流: conversationId=c4234576-5c97-4146-8d33-ebd68e92a1eb, source=HTTP_SSE
15:37:25.188 CNET-TOKEN-RECV          I  📦 [c4234576-5c97-4146-8d33-ebd68e92a1eb] HTTP_SSE → Batch[80 tokens, 310B, 26ms]
15:37:25.189 CNET-TOKEN-BATCH         D  📊 批量刷新完成: received=80, output=0
15:37:25.196 Progressiv...olDetector  I  🔍 最终检测完成: type=PLAIN_TEXT, confidence=0.5
15:37:25.198 UnifiedTokenReceiver     I  🔍 协议检测完成: type=PLAIN_TEXT, tokens=109, time=57ms, confidence=0.5
15:37:25.203 TB-VIEWMODEL             D  📥 [新架构Token] messageId=c4234576-5c97-4146-8d33-ebd68e92a1eb, token='时间', type=JSON_SSE
15:37:25.205 TB-PARSER                I  🚀 [解析启动] 开始解析 token 流: c4234576-5c97-4146-8d33-ebd68e92a1eb
15:37:25.211 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
15:37:25.211                          E  🔍 [Token输出] [0] Text(content=时间)
15:37:25.214 TB-MAPPER                I  📝 [finalmermaid规范] PRE_THINK状态，自动创建perthink段
15:37:25.218 TB-REDUCER               D  🔄 处理事件: SegmentStarted
15:37:25.219                          D  🎯 创建段: perthink (PERTHINK)
15:37:25.223                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
15:37:25.226                          D  🔄 处理事件: SegmentText
15:37:25.226                          D  📝 追加文本到段[perthink]: 时间...
15:37:25.226                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
15:37:25.230 TB-VIEWMODEL             D  📥 [新架构Token] messageId=c4234576-5c97-4146-8d33-ebd68e92a1eb, token='。', type=JSON_SSE
15:37:25.230 TB-PARSER                I  🚀 [解析启动] 开始解析 token 流: c4234576-5c97-4146-8d33-ebd68e92a1eb
15:37:25.231 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
15:37:25.231                          E  🔍 [Token输出] [0] Text(content=。)
15:37:25.232 TB-MAPPER                D  📝 段[perthink]文本: 。...
15:37:25.232 TB-REDUCER               D  🔄 处理事件: SegmentText
15:37:25.232                          D  📝 追加文本到段[perthink]: 。...
15:37:25.232                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
15:37:25.233 TB-VIEWMODEL             D  📥 [新架构Token] messageId=c4234576-5c97-4146-8d33-ebd68e92a1eb, token='训练', type=JSON_SSE
15:37:25.233 TB-PARSER                I  🚀 [解析启动] 开始解析 token 流: c4234576-5c97-4146-8d33-ebd68e92a1eb
15:37:25.236 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
15:37:25.236                          E  🔍 [Token输出] [0] Text(content=训练)
15:37:25.236 TB-MAPPER                D  📝 段[perthink]文本: 训练...
15:37:25.236 TB-REDUCER               D  🔄 处理事件: SegmentText
15:37:25.237                          D  📝 追加文本到段[perthink]: 训练...
15:37:25.237                          D  📊 状态更新: TBState(current=perthink, queue=0, finalBuffer=0, thinkingClosed=false, finalClosed=false, streaming=true)
15:37:25.240 TB-VIEWMODEL             D  📥 [新架构Token] messageId=c4234576-5c97-4146-8d33-ebd68e92a1eb, token='内容', type=JSON_SSE
15:37:25.241 TB-PARSER                I  🚀 [解析启动] 开始解析 token 流: c4234576-5c97-4146-8d33-ebd68e92a1eb
15:37:25.241 TB-XML-OUTPUT            E  🔍 [Token输出] 生成1个tokens:
15:37:25.241                          E  🔍 [Token输出] [0] Text(content=内容)
