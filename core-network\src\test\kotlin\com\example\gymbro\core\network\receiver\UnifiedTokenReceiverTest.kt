package com.example.gymbro.core.network.receiver

import com.example.gymbro.core.network.buffer.AdaptiveBufferManager
import com.example.gymbro.core.network.buffer.PerformanceMonitor
import com.example.gymbro.core.network.buffer.ProcessingMetrics
import com.example.gymbro.core.network.detector.ContentType
import com.example.gymbro.core.network.detector.DetectionResult
import com.example.gymbro.core.network.detector.ProgressiveProtocolDetector
import com.example.gymbro.core.network.processor.StreamingProcessor
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.withTimeout
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

/**
 * 🚀 UnifiedTokenReceiver 单元测试
 *
 * 测试目标：
 * - 验证统一Token接收和处理流程
 * - 测试协议检测集成
 * - 验证性能监控集成
 * - 测试错误处理和恢复
 */
class UnifiedTokenReceiverTest {

    private lateinit var protocolDetector: ProgressiveProtocolDetector
    private lateinit var streamingProcessor: StreamingProcessor
    private lateinit var adaptiveBufferManager: AdaptiveBufferManager
    private lateinit var performanceMonitor: PerformanceMonitor
    private lateinit var unifiedTokenReceiver: UnifiedTokenReceiver

    @BeforeEach
    fun setup() {
        protocolDetector = mockk<ProgressiveProtocolDetector>(relaxed = true)
        streamingProcessor = mockk<StreamingProcessor>(relaxed = true)
        adaptiveBufferManager = mockk<AdaptiveBufferManager>(relaxed = true)
        performanceMonitor = mockk<PerformanceMonitor>(relaxed = true)

        unifiedTokenReceiver = UnifiedTokenReceiver(
            protocolDetector,
            streamingProcessor,
            adaptiveBufferManager,
            performanceMonitor
        )
    }

    @Test
    fun `应该正确处理JSON SSE token流`() = runTest {
        // Given
        val conversationId = "test-conversation"
        val tokens = listOf(
            "data: {\"choices\":[{\"delta\":{\"content\":\"Hello\"}}]}",
            "data: {\"choices\":[{\"delta\":{\"content\":\" World\"}}]}",
            "data: [DONE]"
        )

        val tokenSource = HttpSseTokenSource(flow {
            tokens.forEach { emit(it) }
        })

        // Mock协议检测
        every { protocolDetector.detectWithConfidence(any()) } returns
            DetectionResult.Confirmed(ContentType.JSON_SSE, 0.9f)

        // Mock流式处理
        every { streamingProcessor.processImmediate(any(), any(), any()) } returns "processed"

        // Mock性能监控
        coEvery { performanceMonitor.getCurrentMetrics() } returns ProcessingMetrics(
            tokensPerSecond = 100f,
            networkThroughput = 120f,
            memoryUsagePercent = 0.5f,
            bufferUtilization = 0.6f,
            avgLatencyMs = 20L,
            errorRate = 0.0f
        )

        // When
        val results = unifiedTokenReceiver.receiveTokenStream(tokenSource, conversationId).toList()

        // Then
        assertEquals(3, results.size, "应该处理所有token")
        results.forEach { result ->
            assertEquals("processed", result, "每个token应该被正确处理")
        }

        verify { protocolDetector.reset() }
        verify(atLeast = 1) { protocolDetector.detectWithConfidence(any()) }
        verify(atLeast = 1) { streamingProcessor.processImmediate(any(), ContentType.JSON_SSE, conversationId) }
    }

    @Test
    fun `应该在检测完成后跳过进一步检测`() = runTest {
        // Given
        val conversationId = "test-conversation"
        val tokens = (1..10).map { "token$it" }

        val tokenSource = HttpSseTokenSource(flow {
            tokens.forEach { emit(it) }
        })

        // Mock协议检测 - 第一次确认，后续不再检测
        every { protocolDetector.detectWithConfidence("token1") } returns
            DetectionResult.Confirmed(ContentType.XML_THINKING, 1.0f)
        every { protocolDetector.detectWithConfidence(not("token1")) } returns
            DetectionResult.INSUFFICIENT_DATA

        every { streamingProcessor.processImmediate(any(), any(), any()) } returns "processed"

        coEvery { performanceMonitor.getCurrentMetrics() } returns ProcessingMetrics(
            tokensPerSecond = 100f, networkThroughput = 120f, memoryUsagePercent = 0.5f,
            bufferUtilization = 0.6f, avgLatencyMs = 20L, errorRate = 0.0f
        )

        // When
        val results = unifiedTokenReceiver.receiveTokenStream(tokenSource, conversationId).toList()

        // Then
        assertEquals(10, results.size)
        verify(exactly = 1) { protocolDetector.detectWithConfidence("token1") }
        verify(atLeast = 9) { streamingProcessor.processImmediate(any(), ContentType.XML_THINKING, conversationId) }
    }

    @Test
    fun `应该在检测超时后使用默认处理`() = runTest {
        // Given
        val conversationId = "test-conversation"
        val tokens = (1..250).map { "token$it" } // 超过200个token的检测阈值

        val tokenSource = HttpSseTokenSource(flow {
            tokens.forEach { emit(it) }
        })

        // Mock协议检测 - 始终返回数据不足
        every { protocolDetector.detectWithConfidence(any()) } returns
            DetectionResult.INSUFFICIENT_DATA

        every { streamingProcessor.processImmediate(any(), any(), any()) } returns "processed"

        coEvery { performanceMonitor.getCurrentMetrics() } returns ProcessingMetrics(
            tokensPerSecond = 100f, networkThroughput = 120f, memoryUsagePercent = 0.5f,
            bufferUtilization = 0.6f, avgLatencyMs = 20L, errorRate = 0.0f
        )

        // When - 使用超时控制
        val results = withTimeout(5000) {
            unifiedTokenReceiver.receiveTokenStream(tokenSource, conversationId).toList()
        }

        // 等待所有协程完成
        advanceUntilIdle()

        // Then
        assertEquals(250, results.size)
        // 验证在超时后使用了默认的PLAIN_TEXT类型
        verify(atLeast = 1) { streamingProcessor.processImmediate(any(), ContentType.PLAIN_TEXT, conversationId) }
    }

    @Test
    fun `应该正确处理空token`() = runTest {
        // Given
        val conversationId = "test-conversation"
        val tokens = listOf("", "  ", "valid-token", "")

        val tokenSource = HttpSseTokenSource(flow {
            tokens.filter { it.isNotBlank() }.forEach { emit(it) } // 过滤空白token
        })

        every { protocolDetector.detectWithConfidence("valid-token") } returns
            DetectionResult.Confirmed(ContentType.PLAIN_TEXT, 1.0f)
        every { streamingProcessor.processImmediate("valid-token", ContentType.PLAIN_TEXT, conversationId) } returns "processed"
        every { streamingProcessor.processImmediate(match { it.isBlank() }, any(), any()) } returns ""

        coEvery { performanceMonitor.getCurrentMetrics() } returns ProcessingMetrics(
            tokensPerSecond = 100f, networkThroughput = 120f, memoryUsagePercent = 0.5f,
            bufferUtilization = 0.6f, avgLatencyMs = 20L, errorRate = 0.0f
        )

        // When
        val results = unifiedTokenReceiver.receiveTokenStream(tokenSource, conversationId).toList()

        // 等待所有协程完成
        advanceUntilIdle()

        // Then
        assertEquals(1, results.size, "应该只处理非空token")
        assertEquals("processed", results.first())
    }

    @Test
    fun `应该正确处理处理器异常`() = runTest {
        // Given
        val conversationId = "test-conversation"
        val tokens = listOf("token1", "token2")

        val tokenSource = HttpSseTokenSource(flow {
            tokens.forEach { emit(it) }
        })

        every { protocolDetector.detectWithConfidence(any()) } returns
            DetectionResult.Confirmed(ContentType.JSON_SSE, 1.0f)

        // Mock处理器抛出异常
        every { streamingProcessor.processImmediate(any(), any(), any()) } throws
            RuntimeException("Processing error")

        coEvery { performanceMonitor.getCurrentMetrics() } returns ProcessingMetrics(
            tokensPerSecond = 100f, networkThroughput = 120f, memoryUsagePercent = 0.5f,
            bufferUtilization = 0.6f, avgLatencyMs = 20L, errorRate = 0.0f
        )

        // When & Then
        assertThrows(RuntimeException::class.java) {
            runTest {
                unifiedTokenReceiver.receiveTokenStream(tokenSource, conversationId).toList()
            }
        }
    }

    @Test
    fun `应该正确更新性能指标`() = runTest {
        // Given
        val conversationId = "test-conversation"
        val tokens = listOf("token1", "token2", "token3")

        val tokenSource = HttpSseTokenSource(flow {
            tokens.forEach { emit(it) }
        })

        every { protocolDetector.detectWithConfidence(any()) } returns
            DetectionResult.Confirmed(ContentType.JSON_SSE, 1.0f)
        every { streamingProcessor.processImmediate(any(), any(), any()) } returns "processed"

        coEvery { performanceMonitor.getCurrentMetrics() } returns ProcessingMetrics(
            tokensPerSecond = 100f, networkThroughput = 120f, memoryUsagePercent = 0.5f,
            bufferUtilization = 0.6f, avgLatencyMs = 20L, errorRate = 0.0f
        )

        // When
        unifiedTokenReceiver.receiveTokenStream(tokenSource, conversationId).toList()

        // Then
        coVerify(atLeast = 1) { performanceMonitor.getCurrentMetrics() }
        verify(atLeast = 1) { adaptiveBufferManager.adjustBufferSize(any()) }
    }

    @Test
    fun `获取接收器状态应该返回正确信息`() {
        // Given
        every { adaptiveBufferManager.getStatus() } returns mockk {
            every { currentBufferSize } returns 64
            every { adjustmentCount } returns 5L
        }

        every { protocolDetector.getDetectionStatus() } returns mockk {
            every { currentStage } returns com.example.gymbro.core.network.detector.DetectionStage.STANDARD
            every { detectedType } returns ContentType.JSON_SSE
        }

        // When
        val status = unifiedTokenReceiver.getReceiverStatus()

        // Then
        assertNotNull(status)
        assertEquals(64, status.currentBufferSize)
        assertEquals(5L, status.bufferAdjustments)
        assertEquals(com.example.gymbro.core.network.detector.DetectionStage.STANDARD, status.detectionStage)
        assertEquals(ContentType.JSON_SSE, status.detectedType)
    }

    @Test
    fun `重置统计信息应该清除所有计数器`() {
        // When
        unifiedTokenReceiver.resetStatistics()

        // Then
        val status = unifiedTokenReceiver.getReceiverStatus()
        assertEquals(0L, status.totalTokensReceived)
        assertEquals(0L, status.totalConversations)
    }

    @Test
    fun `不同Token来源类型应该被正确识别`() {
        // Given
        val httpSseSource = HttpSseTokenSource(flow { emit("test") })
        val webSocketSource = WebSocketTokenSource(flow { emit("test") })
        val httpBasicSource = HttpBasicTokenSource(flow { emit("test") })

        // When & Then
        assertEquals("HTTP_SSE", httpSseSource.sourceType)
        assertEquals("WEBSOCKET", webSocketSource.sourceType)
        assertEquals("HTTP_BASIC", httpBasicSource.sourceType)
    }
}
