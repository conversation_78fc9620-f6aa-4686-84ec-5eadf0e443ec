package com.example.gymbro.features.workout.template.edit.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import timber.log.Timber
import javax.inject.Inject

/**
 * 状态管理处理器
 *
 * 🎯 职责：
 * - 处理状态变更相关的 Intent
 * - 管理自动保存状态
 * - 处理版本控制状态
 * - 处理保存结果状态
 *
 * 📋 遵循标准：
 * - 单一职责原则
 * - 纯函数式编程
 * - 不可变状态管理
 */
class StateManagementHandlers @Inject constructor() {

    // === 自动保存状态管理 ===

    fun handleAutoSaveTriggered(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        // 🔥 暂时禁用template自动保存功能
        Timber.d("🚫 [AUTO-SAVE-DISABLED] Template自动保存已被禁用")
        return ReduceResult.noChange(state)
    }

    // AutoSave功能已移除

    // === 版本控制状态管理 ===

    fun handleShowVersionHistory(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(showVersionHistory = true),
        )
    }

    fun handleHideVersionHistory(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(showVersionHistory = false),
        )
    }

    fun handleRestoreFromVersion(
        intent: TemplateEditContract.Intent.RestoreFromVersion,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.withEffect(
            state.copy(isRestoringVersion = true),
            TemplateEditContract.Effect.ShowVersionRestored,
        )
    }

    fun handleVersionCreated(
        intent: TemplateEditContract.Intent.VersionCreated,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                isCreatingVersion = false,
                currentVersion = intent.version.versionNumber,
                hasUnsavedChanges = false,
            ),
        )
    }

    // === 保存结果状态管理 ===

    fun handleSaveSuccess(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        // 简化保存成功状态更新
        val updatedState = state.copy(
            isSaving = false,
            hasUnsavedChanges = false,
            isCreatingVersion = false,
            lastPublishedAt = System.currentTimeMillis(),
        )

        // 🔥 修复：移除保存后的LoadTemplateData effect，避免无限循环
        // 保存成功后不需要重新加载数据，状态已经是最新的
        return ReduceResult.stateOnly(updatedState)
    }

    fun handleDraftSaved(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        Timber.d("🔥 [DRAFT-SAVED] 更新状态: isDraft=true, hasUnsavedChanges=false")
        return ReduceResult.stateOnly(
            state.copy(
                isSaving = false,
                hasUnsavedChanges = false,
                isCreatingVersion = false,
            ),
        )
    }

    /**
     * 🔥 Phase 3 Fix: 处理发布完成的状态同步
     * 确保发布后 isDraft=false, isPublished=true, isSaving=false
     */
    fun handlePublishCompleted(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                isSaving = false,
                isCreatingVersion = false,
                hasUnsavedChanges = false,
                lastPublishedAt = System.currentTimeMillis(),
            ),
        )
    }
}
