以下是针对您最终要求整理的 **ThinkingBox × Coach 重构蓝图**，同时给出文件级别的改动清单以及各标签在数据流与 UI 流中的对应关系。方案采用主题模块中的 **`UnifiedTextRenderer`** 和 **`StreamingFinalRenderer`** 替代旧的打字机动画，实现更流畅的渲染。

---

## 一、总体蓝图

### 1. 架构与数据流

1. **流式数据管道**：Core‑Network 获取 AI token 流后，经 `StreamingThinkingMLParser` 按大纲定义的标签解析为语义事件，再经 `DomainMapper` 映射为 `ThinkingEvent`，`SegmentQueueReducer` 更新内部状态，将闭合的片段 (`Segment`) 入队；UI 只消费队列头部的片段。
2. **事件驱动的状态管理**：`Reducer` 处理 `SegmentStarted`、`SegmentText`、`SegmentClosed`、`ThinkingClosed`、`FinalStart`、`FinalContent`、`FinalClosed` 以及 `UiSegmentRendered` 等事件，不包含任何界面逻辑。
3. **UI 时序独立**：`AIThinkingCard` 通过 `AnimatedVisibility` 控制显示或隐藏，任何时候只渲染 `segmentsQueue.firstOrNull()`，使用 `UnifiedTextRenderer` 完整呈现后通过 `onSegmentRendered(id)` 回调移除队头。`StreamingFinalRenderer` 在思考结束且队列为空时渲染最终富文本。
4. **Coach 与 ThinkingBox 解耦**：Coach 负责组装 Prompt、发送 AI 请求和保存结果。ThinkingBox 专注数据流解析和界面展示，通过 `ThinkingBoxDisplay` 提供 `startDisplaying/stopDisplaying` 接口，并在完成时通过 `ThinkingBoxCompletionListener` 回调思考过程和最终内容。
5. **History 独立写入**：`HistoryActor` 监听 `NotifyHistoryThinking` 和 `NotifyHistoryFinal` 效果，将思考过程与最终答案分别保存至仓库。其工作与 UI 展示时序完全解耦。

### 2. UI 控制开关

在 `ThinkingBoxContract.State` 中提供两个计算属性：

```kotlin
val shouldShowThinkingCard: Boolean
    get() = !thinkingClosed || segmentsQueue.isNotEmpty()

val shouldShowFinalContent: Boolean
    get() = thinkingClosed && segmentsQueue.isEmpty() && finalReady
```

这两个属性决定思考卡片和最终富文本的显示时机，确保“每个阶段必须完整展示之后才进入下一个阶段”。

---

## 二、文件级别改动清单

### 1. 新增文件

| 文件路径                                                                      | 作用                                                                                                         |
| ----------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------ |
| `features/thinkingbox/api/ThinkingBoxDisplay.kt`                              | 定义 `startDisplaying(messageId, completionListener)` 和 `stopDisplaying` 接口，供 Coach 调用启动/停止展示。 |
| `features/thinkingbox/api/ThinkingBoxCompletionListener.kt`                   | 定义 `onDisplayComplete(messageId, thinkingProcess, finalContent, metadata)` 与 `onDisplayError` 回调。      |
| `features/thinkingbox/api/ThinkingBoxLauncher.kt`（可选）                     | 封装 `ThinkingBoxDisplay` 与 `core-network` 的协作，处理初始化和取消逻辑。                                   |
| `shared-models/ChatMessage.kt`、`shared-models/Conversation.kt`               | 统一消息模型，方便 Coach、LayeredPromptBuilder 与 ThinkingBox 共享。                                         |
| `domain/usecase/ConversationUseCase.kt`、`domain/usecase/AiRequestUseCase.kt` | 封装会话管理和 AI 请求构建，减轻 ViewModel 负担。                                                            |

### 2. 修改文件

| 文件路径                                                                  | 核心修改内容                                                                                                                                                                                                                                                       |
| ------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| `features/thinkingbox/domain/parser/StreamingThinkingMLParser.kt`         | 简化为纯语法解析器，只输出合法标签和文本。新增 `<final>` 段解析逻辑，非法标签通过 `StringXmlEscaper` 清理。                                                                                                                                                        |
| `features/thinkingbox/domain/mapper/DomainMapper.kt`                      | 重写映射逻辑，将语义事件映射为新的 `ThinkingEvent`：`SegmentStarted`、`SegmentText`、`SegmentClosed`、`ThinkingClosed`、`FinalStart`、`FinalContent`、`FinalClosed`。                                                                                              |
| `features/thinkingbox/internal/reducer/SegmentQueueReducer.kt`            | 实现队列模型，处理 UI 握手：`UiSegmentRendered` 事件出队，`ThinkingClosed` 仅设置状态，`FinalStart/FinalContent/FinalClosed` 更新 `finalBuffer/finalReady`。                                                                                                       |
| `features/thinkingbox/internal/presentation/ThinkingBoxViewModel.kt`      | 在 State 中添加 `segmentsQueue: List<SegmentUi>`, `finalContent`, `finalReady`, `thinkingClosed`；提供 `shouldShowThinkingCard` 和 `shouldShowFinalContent` 计算属性；处理新事件并发出 `CloseThinkingBox`/`NotifyHistory` 效果；在完成时调用 `completionListener`. |
| `features/thinkingbox/internal/presentation/ui/AIThinkingCard.kt`         | 使用 `AnimatedVisibility` 根据 `state.shouldShowThinkingCard` 控制显示；渲染 `state.segmentsQueue.firstOrNull()`；改用 `UnifiedTextRenderer` 渲染每个 `Segment`，在渲染完成后调用 `onSegmentRendered`.                                                             |
| `features/thinkingbox/internal/presentation/ui/StreamingFinalRenderer.kt` | 使用 `StreamingFinalRenderer` 渲染最终内容，根据 `state.shouldShowFinalContent` 显示；流式渲染 `finalContent`，支持渐入渐出动画。                                                                                                                                  |
| `features/thinkingbox/history/HistoryActor.kt`                            | 调整以响应 `NotifyHistoryThinking` 和 `NotifyHistoryFinal` 效果，分别保存思考过程和最终答案，移除旧的自动保存逻辑。                                                                                                                                                |
| `features/thinkingbox/internal/display/ThinkingBoxDisplayImpl.kt`         | 实现 `ThinkingBoxDisplay` 接口，监听 ViewModel 状态；当 `shouldShowFinalContent` 变为 `false` 且 UI 不再渲染时触发完成回调。                                                                                                                                       |
| `features/thinkingbox/internal/contract/ThinkingBoxContract.kt`           | 更新事件类型和 State 字段，添加 `shouldShowThinkingCard`、`shouldShowFinalContent`。                                                                                                                                                                               |
| `features/coach/aicoach/internal/components/ChatInterface.kt`             | 移除对旧 AI 渲染组件的引用，改用 `ThinkingBoxStaticRenderer` 来渲染历史消息。                                                                                                                                                                                      |
| `features/coach/aicoach/internal/viewmodel/AiCoachSessionHandler.kt`      | 注入 `ThinkingBoxDisplay` 与 `ThinkingBoxCompletionListener`，在发送 AI 请求后调用 `startDisplaying(messageId, completionListener)`；在 `onDisplayComplete` 中保存思考过程和最终答案。                                                                             |
| `features/thinkingbox/di/ThinkingBoxModule.kt`                            | 绑定新的 `ThinkingBoxDisplay`/`CompletionListener` 实现，取消旧的 `HistorySaver` 注入。                                                                                                                                                                            |
| `di/DomainModule.kt` 和 `di/DataModule.kt`                                | 新增 `ConversationUseCase`、`AiRequestUseCase` 等绑定，确保新接口可用。                                                                                                                                                                                            |

### 3. 删除文件

| 文件路径                                                             | 理由                                                   |
| -------------------------------------------------------------------- | ------------------------------------------------------ |
| `features/thinkingbox/internal/history/HistorySaver.kt`              | 历史写入逻辑由 `HistoryActor` 完全接管，避免职责重叠。 |
| `features/coach/aicoach/internal/components/AiResponseComponents.kt` | Coach 不再负责 AI 响应渲染，统一由 ThinkingBox 提供。  |
| 其他冗余旧接口或未使用的测试文件                                     | 清理遗留代码，保证架构简洁。                           |

---

## 三、标签属性与数据流／UI流映射

| 标签/断点              | 解析结果 (`ThinkingEvent`)                                                     | 数据流操作 (Reducer 更新)                                                                             | UI 渲染器                            | UI 行为                                                                                           |
| ---------------------- | ------------------------------------------------------------------------------ | ----------------------------------------------------------------------------------------------------- | ------------------------------------ | ------------------------------------------------------------------------------------------------- |
| **纯文本或 `<think>`** | `SegmentStarted(id="perthink", kind=PERTHINK)`、`SegmentText`、`SegmentClosed` | `currentSegment.kind=PERTHINK`，收集文本；遇 `<thinking>` 时关闭 `perthink` 段并加入队列              | `UnifiedTextRenderer`                | 渲染预思考内容，动画结束后发送 `onSegmentRendered("perthink")`                                    |
| **`<thinking>`**       | 无特殊事件                                                                     | 将当前 `perthink` 段闭合并入队，状态切换至 `FORMAL_PHASE`                                             | -                                    | 不直接影响 UI；UI 继续按队列渲染下一段                                                            |
| **`<phase id="x">`**   | `SegmentStarted(id, kind=PHASE)`                                               | 创建新的 `Segment`，kind=PHASE，title 根据 `<title>` 元素提取                                         | `UnifiedTextRenderer`                | 渲染当前阶段内容，结束后 `onSegmentRendered(id)`                                                  |
| **`</phase>`**         | `SegmentClosed(id)`                                                            | 将当前 `Segment` 置 `closed`，加入队列                                                                | -                                    | UI 在渲染完成后出队，自动显示下一段                                                               |
| **`</thinking>`**      | `ThinkingClosed`                                                               | 仅将 `thinkingClosed` 设为 `true`；不清空队列；发送 `NotifyHistoryThinking` 效果                      | -                                    | `shouldShowThinkingCard` 可能仍为 `true`，UI 继续展示队列中的未渲染段，直至队列为空；此后卡片消失 |
| **`<final>`**          | `FinalStart`                                                                   | 将状态 `finalReady=true`；此后所有 `TextChunk` 映射为 `FinalContent` 加入 `finalBuffer`，而不进入队列 | `StreamingFinalRenderer`（等待显示） | 不渲染任何内容，直到 `shouldShowFinalContent` 为 `true` 时，以流式方式展示最终富文本              |
| **`FinalContent`**     | `FinalContent(text)`                                                           | 追加文本到 `finalBuffer`                                                                              | -                                    | 暂不渲染；等待思考卡片关闭后由 `StreamingFinalRenderer` 统一流式渲染                              |
| **`</final>`**         | `FinalClosed`                                                                  | 设置 `finalClosed=true`；发送 `NotifyHistoryFinal` 效果；标记最终富文本完整                           | `StreamingFinalRenderer`             | 当 `shouldShowFinalContent` 为 `true` 时开始渲染；渲染完毕后触发完成回调                          |

---

## 四、说明与建议

1. **UI 渲染器**：使用 `UnifiedTextRenderer` 替代打字机动画渲染 perthink 和 phase 内容，可以在 `ThinkingStageCard` 内灵活调整速度和样式；最终富文本使用 `StreamingFinalRenderer` 提供平滑的流式展现效果。
2. **握手机制**：UI 必须在每个 `Segment` 动画全部结束后调用 `onSegmentRendered`，否则队列不会出队，后续内容无法呈现。
3. **异步与性能**：所有数据流和渲染操作应基于协程和 Compose 的异步 API，避免阻塞主线程。注意控制动画时长与帧率，符合 PRD 中的性能指标。
4. **测试覆盖**：重构完成后，需要添加针对 `<final>` 逻辑和 `shouldShowFinalContent` 控制的端到端测试，确保在 `</thinking>` 到达后 UI 仍能完整渲染各阶段。

通过以上蓝图和文件级改动清单，可以确保新的 ThinkingBox 满足“数据队列为真理、UI 单段消费、时序独立”这一核心原则，并在体验上通过 `UnifiedTextRenderer` 和 `StreamingFinalRenderer` 提供更加流畅、可控的动画效果。
