@file:OptIn(ExperimentalSerializationApi::class)

import kotlinx.serialization.*
import kotlinx.serialization.json.*
import com.example.gymbro.features.workout.json.processor.*
import com.example.gymbro.shared.models.workout.*
import com.example.gymbro.domain.workout.model.*
import com.example.gymbro.domain.workout.model.plan.DayPlan
import com.example.gymbro.core.ui.text.UiText

/**
 * JSON数据序列化和反序列化验证测试
 * 
 * 🎯 验证目标：
 * 1. JSON序列化/反序列化准确性
 * 2. 命名约定符合Template_Naming_Convention_Baseline.md规范
 * 3. PlanEditScreen使用workout JSON目录文件的情况
 * 4. 字段映射准确性验证
 */

/**
 * 测试JSON序列化精度
 */
fun testJsonSerializationAccuracy() {
    println("🔍 开始JSON序列化精度验证...")
    
    // 测试1: Template相关的JSON处理
    val templateExercise = TemplateExerciseDto(
        id = "template_001",
        exerciseId = "lib_001", 
        exerciseName = "深蹲",
        sets = 3,
        reps = 12,
        targetWeight = 80.5f,  // 注意：Template使用targetWeight
        restTimeSeconds = 90,
        customSets = listOf(
            TemplateSetDto(1, 80.0f, 12, 90),
            TemplateSetDto(2, 85.0f, 10, 90),
            TemplateSetDto(3, 90.0f, 8, 120)
        )
    )
    
    // 使用TemplateJsonProcessor序列化
    val templateJson = TemplateJsonProcessor.run { 
        templateExercise.toWorkoutExerciseJson() 
    }
    println("📄 Template JSON: $templateJson")
    
    // 验证JSON字段是否符合命名规范
    val parsedJson = Json.parseToJsonElement(templateJson).jsonObject
    
    // 检查重量字段命名
    val weightField = parsedJson["weight"]?.jsonPrimitive?.float
    println("✅ 重量字段映射: targetWeight(80.5) → JSON weight($weightField)")
    
    // 检查次数字段命名
    val repsField = parsedJson["reps"]?.jsonPrimitive?.int
    println("✅ 次数字段映射: reps(12) → JSON reps($repsField)")
    
    // 检查休息时间字段
    val restField = parsedJson["restTimeSeconds"]?.jsonPrimitive?.int
    println("✅ 休息时间字段: restTimeSeconds(90) → JSON restTimeSeconds($restField)")
}

/**
 * 测试Plan数据的JSON处理
 */
fun testPlanJsonProcessing() {
    println("\n🔍 开始Plan JSON处理验证...")
    
    // 创建测试用的WorkoutPlan
    val workoutPlan = WorkoutPlan(
        id = "plan_001",
        name = UiText.DynamicString("我的训练计划"),
        userId = "user_123",
        description = UiText.DynamicString("4周训练计划"),
        dailySchedule = mapOf(
            1 to DayPlan.createWorkoutDay(
                dayNumber = 1,
                templateVersionIds = listOf("template_001", "template_002"),
                dayNotes = UiText.DynamicString("第一天训练")
            ),
            2 to DayPlan.createRestDay(2)
        ),
        totalDays = 28
    )
    
    // 使用PlanJsonProcessor处理
    val planJson = PlanJsonProcessor.planToJson(workoutPlan)
    println("📄 Plan JSON: $planJson")
    
    // 测试Calendar数据生成
    val calendarData = workoutPlan.toCalendarJson("2025-01-01")
    val calendarJson = Json.encodeToString(calendarData)
    println("📅 Calendar JSON: $calendarJson")
    
    // 验证反序列化
    val deserializedPlan = PlanJsonProcessor.planFromJson(planJson)
    println("✅ Plan反序列化成功: ${deserializedPlan.name} (ID: ${deserializedPlan.id})")
}

/**
 * 验证命名约定符合情况
 */
fun validateNamingConventions() {
    println("\n🔍 开始命名约定验证...")
    
    // 检查Template命名规范
    val templateTest = TemplateSetDto(
        setNumber = 1,
        targetWeight = 80.0f,    // ✅ Template层使用targetWeight
        targetReps = 12,         // ✅ Template层使用targetReps
        restTimeSeconds = 90     // ✅ 时间字段包含单位
    )
    
    // 检查Exercise命名规范
    val exerciseTest = ExerciseSetDto(
        weight = 80.0f,          // ✅ Exercise层使用weight
        reps = 12,               // ✅ Exercise层使用reps
        restTimeSeconds = 90,    // ✅ 统一的时间字段
        completedAt = null
    )
    
    println("✅ Template字段命名: targetWeight, targetReps, restTimeSeconds")
    println("✅ Exercise字段命名: weight, reps, restTimeSeconds")
    
    // 检查JSON序列化结果
    val templateJson = Json.encodeToString(templateTest)
    val exerciseJson = Json.encodeToString(exerciseTest)
    
    println("📄 Template JSON序列化: $templateJson")
    println("📄 Exercise JSON序列化: $exerciseJson")
    
    // 验证JSON字段名统一性
    val templateParsed = Json.parseToJsonElement(templateJson).jsonObject
    val exerciseParsed = Json.parseToJsonElement(exerciseJson).jsonObject
    
    val templateWeightKey = if (templateParsed.containsKey("weight")) "weight" else "targetWeight"
    val exerciseWeightKey = if (exerciseParsed.containsKey("weight")) "weight" else null
    
    println("🔍 JSON字段映射验证:")
    println("   Template weight字段: $templateWeightKey") 
    println("   Exercise weight字段: $exerciseWeightKey")
    
    if (templateWeightKey == "weight" && exerciseWeightKey == "weight") {
        println("✅ JSON字段命名统一: 都使用'weight'")
    } else {
        println("❌ JSON字段命名不统一!")
    }
}

/**
 * 检查PlanEditScreen对JSON处理器的使用情况
 */
fun checkPlanEditScreenJsonUsage() {
    println("\n🔍 检查PlanEditScreen JSON处理器使用情况...")
    
    // 模拟PlanEditScreen中可能需要JSON处理的场景
    
    // 1. 计划保存时的JSON序列化
    println("📋 场景1: 计划保存 - 应该使用PlanJsonProcessor")
    
    // 2. 日历JSON导出
    println("📅 场景2: 日历导出 - 应该使用toCalendarJson()")
    
    // 3. 模板数据处理 
    println("🏋️ 场景3: 模板处理 - 应该使用TemplateJsonProcessor")
    
    // 建议的改进
    println("\n💡 建议改进:")
    println("1. PlanEditScreen应该导入并使用 PlanJsonProcessor")
    println("2. 保存操作应该使用 planToJson() 进行序列化")
    println("3. 日历导出应该使用 toCalendarJson() 方法")
    println("4. 模板预览应该使用 TemplateJsonProcessor")
}

/**
 * 综合验证报告
 */
fun generateVerificationReport() {
    println("\n" + "=".repeat(60))
    println("📊 JSON验证综合报告")
    println("=".repeat(60))
    
    try {
        testJsonSerializationAccuracy()
        testPlanJsonProcessing()
        validateNamingConventions()
        checkPlanEditScreenJsonUsage()
        
        println("\n✅ 验证完成!")
        println("📋 发现的问题:")
        println("1. PlanEditScreen未使用workout/json目录的JSON处理器")
        println("2. 建议集成PlanJsonProcessor提升数据处理能力")
        println("3. 建议在保存和导出功能中使用标准化JSON处理")
        
    } catch (e: Exception) {
        println("❌ 验证过程中出现错误: ${e.message}")
        e.printStackTrace()
    }
}

/**
 * 主函数
 */
fun main() {
    println("🚀 开始JSON数据验证...")
    generateVerificationReport()
}