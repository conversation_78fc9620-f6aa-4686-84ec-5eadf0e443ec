# Coach-ThinkingBox架构重构计划 (修正版)

## 📋 重构目标

基于用户反馈和GymBro架构原则，重构Coach和ThinkingBox模块，实现清晰的职责分离：
- **Coach**: 负责对话管理和AI请求发送（保存、检索、组装、发送、结果保存）
- **ThinkingBox**: 专注AI响应显示（接收、解析、思考过程显示）

## 🔍 当前架构问题

### 1. 职责边界不清
```
当前流程：
用户输入 → Coach → StreamEffectHandler → AiResponseReceiver → UnifiedTokenReceiver → DirectOutputChannel → ThinkingBox

问题：
- Coach和ThinkingBox之间职责重叠
- AI请求组装逻辑分散
- 缺乏清晰的完成回调机制
```

### 2. 代码重复和复杂性
- 多个Chat相关类：ChatRaw, CoachMessage, ChatRequest, Conversation
- 重复的消息处理逻辑
- 复杂的AI请求构建和token流处理

### 3. 业务逻辑分离不当
- AI请求组装需要读取用户信息、偏好、上下文
- 这些业务逻辑应该在Coach中集中处理
- ThinkingBox承担了过多的业务职责

## 🎯 新架构设计

### 1. 精确的数据流（基于LayeredPromptBuilder）
```
完整数据流：
用户输入 → Coach保存用户消息 → Coach读取用户信息/上下文 →
LayeredPromptBuilder组装完整Prompt → Core-Network发送 → AI处理 →
Core-Network接收响应 → ThinkingBox显示 → 完成回调 → Coach保存结果

关键组件：
- Coach: 业务逻辑控制，调用LayeredPromptBuilder
- LayeredPromptBuilder: 最终的消息组装点（在core模块）
- Core-Network: 网络通信管理
- ThinkingBox: AI响应显示组件
```

### 2. 重新定义的职责分离

#### Coach模块职责 (业务逻辑中心)
- ✅ **对话历史管理**: 存储、检索、显示对话记录
- ✅ **用户消息处理**: 接收用户输入并保存
- ✅ **用户信息读取**: 获取用户档案、偏好、训练数据等上下文
- ✅ **LayeredPromptBuilder调用**: 通过LayeredPromptBuilder组装完整Prompt
- ✅ **AI请求发送**: 通过Core-Network发送组装好的消息
- ✅ **对话列表功能**: 对话搜索、分类、管理
- ✅ **结果保存**: 接收ThinkingBox完成回调并保存AI响应

#### ThinkingBox模块职责 (显示组件)
- ✅ **AI响应接收**: 订阅Core-Network的token流
- ✅ **Token流处理**: 解析和处理AI响应token
- ✅ **思考过程显示**: 实时显示AI思考过程
- ✅ **完成检测**: 检测AI响应完成并回调Coach
- ✅ **显示优化**: 优化token显示性能和用户体验

### 3. 简化的接口设计

#### ThinkingBox显示接口
```kotlin
interface ThinkingBoxDisplay {
    /**
     * 启动ThinkingBox显示AI响应
     *
     * @param messageId 消息ID，用于关联显示会话
     * @param completionListener 完成回调监听器
     */
    fun startDisplaying(
        messageId: String,
        completionListener: ThinkingBoxCompletionListener
    )

    /**
     * 停止正在进行的显示过程
     */
    fun stopDisplaying(messageId: String)
}
```

#### ThinkingBox完成回调接口
```kotlin
interface ThinkingBoxCompletionListener {
    /**
     * ThinkingBox显示完成回调
     *
     * @param messageId 消息ID
     * @param thinkingProcess 思考过程的完整内容
     * @param finalContent 最终的AI响应内容
     * @param metadata 额外的元数据（如处理时间、token数量等）
     */
    fun onDisplayComplete(
        messageId: String,
        thinkingProcess: String,
        finalContent: String,
        metadata: Map<String, Any> = emptyMap()
    )

    /**
     * ThinkingBox显示失败回调
     */
    fun onDisplayError(
        messageId: String,
        error: Throwable,
        partialResult: String? = null
    )
}
```

### 4. 统一的数据模型

#### 消息模型 (shared-models)
```kotlin
// shared-models层 - 统一的消息模型
data class ChatMessage(
    val id: String,
    val conversationId: String,
    val role: MessageRole, // USER, ASSISTANT
    val content: String,
    val thinkingProcess: String? = null, // 仅AI消息有思考过程
    val timestamp: Long,
    val metadata: Map<String, Any> = emptyMap()
)

enum class MessageRole {
    USER, ASSISTANT
}
```

#### 对话模型 (domain层)
```kotlin
// domain层 - 对话业务模型
data class Conversation(
    val id: String,
    val title: String,
    val messages: List<ChatMessage>,
    val lastMessageTime: Long,
    val isActive: Boolean = false
)
```

### 5. MVI架构组件设计

#### Coach模块MVI组件
```kotlin
// Coach Contract
object CoachContract {
    data class State(
        val conversations: List<Conversation> = emptyList(),
        val currentConversation: Conversation? = null,
        val isLoading: Boolean = false,
        val isSendingMessage: Boolean = false,
        val error: UiText? = null
    )

    sealed interface Intent {
        object LoadConversations : Intent
        data class SaveUserMessage(val message: String) : Intent
        data class SendAiRequest(val messageId: String, val userInput: String) : Intent
        data class AiResponseCompleted(val messageId: String, val response: String, val thinkingProcess: String) : Intent
    }

    sealed interface Effect {
        data class BuildAndSendPrompt(
            val messageId: String,
            val userInput: String,
            val conversationHistory: List<ConversationTurn>
        ) : Effect
        data class LaunchThinkingBoxDisplay(val messageId: String) : Effect
        data class ShowError(val message: UiText) : Effect
    }
}

// Coach EffectHandler 关键方法
class CoachEffectHandler @Inject constructor(
    private val layeredPromptBuilder: LayeredPromptBuilder,
    private val aiRequestRepository: AiRequestRepository,
    private val thinkingBoxDisplay: ThinkingBoxDisplay,
    private val conversationRepository: ConversationRepository
) {
    suspend fun buildAndSendPrompt(
        messageId: String,
        userInput: String,
        conversationHistory: List<ConversationTurn>
    ) {
        // 1. 使用LayeredPromptBuilder组装完整消息
        val chatMessages = layeredPromptBuilder.buildChatMessages(
            systemLayer = null, // 使用默认系统层
            userInput = userInput,
            history = conversationHistory,
            userId = getCurrentUserId() // 从用户管理获取
        )

        // 2. 通过Core-Network发送
        aiRequestRepository.sendChatMessages(chatMessages, messageId)

        // 3. 启动ThinkingBox显示
        thinkingBoxDisplay.startDisplaying(messageId, completionListener)
    }
}
```

#### ThinkingBox模块MVI组件
```kotlin
// ThinkingBox Contract
object ThinkingBoxContract {
    data class State(
        val messageId: String? = null,
        val thinkingProcess: String = "",
        val currentToken: String = "",
        val isDisplaying: Boolean = false,
        val isCompleted: Boolean = false,
        val error: UiText? = null
    )

    sealed interface Intent {
        data class StartDisplay(val messageId: String) : Intent
        data class ProcessToken(val token: String) : Intent
        object DisplayCompleted : Intent
    }

    sealed interface Effect {
        data class SubscribeToTokenStream(val messageId: String) : Effect
        data class NotifyCompletion(val messageId: String, val result: String) : Effect
    }
}
```

## 🔧 修正后的实施计划

### 阶段1: 接口设计和基础架构 (1-2天)
1. **创建简化接口**
   - ThinkingBoxDisplay接口
   - ThinkingBoxCompletionListener接口
   - 统一的ChatMessage模型

2. **更新Domain层**
   - 创建ConversationUseCase（Coach业务逻辑）
   - 创建AiRequestUseCase（AI请求组装逻辑）
   - 更新Repository接口

### 阶段2: Coach模块增强 (2-3天)
1. **增强AI请求处理**
   - 实现用户信息读取和上下文组装
   - 增强StreamEffectHandler的AI请求发送能力
   - 集成ThinkingBoxDisplay接口

2. **实现完成回调**
   - 实现ThinkingBoxCompletionListener
   - 添加AI响应结果保存逻辑
   - 更新MVI组件支持新流程

### 阶段3: ThinkingBox模块简化 (1-2天)
1. **简化为显示组件**
   - 实现ThinkingBoxDisplay接口
   - 移除AI请求相关业务逻辑
   - 专注于token流显示和用户体验

2. **优化显示性能**
   - 优化token流处理性能
   - 实现完成状态检测
   - 添加错误处理和恢复机制

## 📊 预期收益

### 1. 架构清晰度
- ✅ **职责分离明确**: Coach专注业务逻辑，ThinkingBox专注显示
- ✅ **模块边界清晰**: 通过接口实现松耦合
- ✅ **依赖关系简化**: 遵循Clean Architecture单向依赖流

### 2. 代码质量
- ✅ **减少代码重复**: 统一消息模型，消除重复的Chat类
- ✅ **提高可维护性**: 业务逻辑集中在Coach，显示逻辑集中在ThinkingBox
- ✅ **增强可测试性**: 清晰的接口边界，便于单元测试和集成测试

### 3. 开发效率
- ✅ **更容易理解**: Coach负责业务，ThinkingBox负责显示，职责明确
- ✅ **减少模块间耦合**: 通过接口通信，降低直接依赖
- ✅ **更好的错误隔离**: 显示错误不影响业务逻辑，业务错误不影响显示

### 4. 性能优化
- ✅ **利用Core-Network新架构**: 充分利用90%延迟减少的性能优势
- ✅ **优化token流处理**: ThinkingBox专注显示优化，提升用户体验
- ✅ **减少不必要的数据转换**: 直接使用Core-Network的输出

## ⚠️ 风险评估

### 1. 兼容性风险
- **风险**: 现有功能可能受影响
- **缓解**: 分阶段实施，确保每个阶段都可编译运行，保持向后兼容

### 2. 业务逻辑复杂性
- **风险**: Coach承担更多业务逻辑可能增加复杂性
- **缓解**: 通过UseCase层合理分解业务逻辑，保持单一职责原则

### 3. 接口设计风险
- **风险**: 接口设计不当可能影响扩展性
- **缓解**: 参考现有成功模式，设计简洁而灵活的接口

## 🎯 成功标准

### 1. 功能完整性
- ✅ 所有现有功能正常工作
- ✅ 用户体验无明显变化或有所提升
- ✅ 性能指标保持或提升（利用Core-Network优势）

### 2. 架构质量
- ✅ **Coach专注业务逻辑**: AI请求组装、发送、结果保存
- ✅ **ThinkingBox专注显示**: token流接收、解析、显示优化
- ✅ **清晰的回调机制**: 通过接口实现模块间通信

### 3. 代码质量
- ✅ 无编译错误和警告
- ✅ 通过所有质量检查（Detekt、KtLint等）
- ✅ 测试覆盖率保持或提升（目标≥90%）
- ✅ 遵循GymBro代码规范和MVI架构原则

## 📋 关键文件修改清单

### 新增文件
1. `shared-models/src/main/kotlin/com/example/gymbro/shared/models/ChatMessage.kt`
2. `features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/api/ThinkingBoxDisplay.kt`
3. `features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/api/ThinkingBoxCompletionListener.kt`
4. `domain/src/main/kotlin/com/example/gymbro/domain/conversation/usecase/ConversationUseCase.kt`
5. `domain/src/main/kotlin/com/example/gymbro/domain/ai/repository/AiRequestRepository.kt` (接口)
6. `data/src/main/kotlin/com/example/gymbro/data/ai/repository/AiRequestRepositoryImpl.kt` (实现)

### 主要修改文件
1. `features/coach/src/main/kotlin/com/example/gymbro/features/coach/internal/presentation/CoachContract.kt`
2. `features/coach/src/main/kotlin/com/example/gymbro/features/coach/internal/presentation/CoachEffectHandler.kt`
3. `features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/presentation/ThinkingBoxContract.kt`
4. `features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/presentation/ThinkingBoxEffectHandler.kt`

### 关键集成点
1. **LayeredPromptBuilder集成**: Coach的EffectHandler需要注入LayeredPromptBuilder
2. **Core-Network接口**: 需要创建AiRequestRepository接口，封装Core-Network的调用
3. **ConversationTurn模型**: 需要与LayeredPromptBuilder的ConversationTurn保持一致

### 依赖注入更新
1. `di/src/main/kotlin/com/example/gymbro/di/DomainModule.kt` - 添加AiRequestRepository绑定
2. `di/src/main/kotlin/com/example/gymbro/di/DataModule.kt` - 添加AiRequestRepositoryImpl绑定
3. `features/coach/src/main/kotlin/com/example/gymbro/features/coach/internal/di/CoachModule.kt` - 注入LayeredPromptBuilder
4. `features/thinkingbox/src/main/kotlin/com/example/gymbro/features/thinkingbox/internal/di/ThinkingBoxModule.kt`

## 📋 详细文件梳理和重构计划

### 🔍 现有文件分析

#### 重复的 Chat 数据模型（需要统一）
1. **ChatRaw** (`data/coach/entity/ChatRaw.kt`) - 数据库实体 ✅ 保留
2. **CoachMessage** (`domain/coach/model/CoachMessage.kt`) - Domain层模型 ✅ 保留
3. **ChatMessage** (`shared-models/ai/ChatMessage.kt`) - 网络DTO ✅ 保留
4. **ChatMessage** (`core/ai/prompt/builder/LayeredPromptBuilder.kt`) - CoreChatMessage ✅ 保留
5. **ChatRequest** (`shared-models/ai/ChatRequest.kt`) - 网络请求DTO ✅ 保留

**结论**: 这些模型各有用途，不需要删除，但需要明确职责边界。

#### Coach 模块现有 MVI 组件
1. **AiCoachContract.kt** - 现有Contract ✅ 需要修改
2. **AiCoachViewModel.kt** - 现有ViewModel ✅ 需要修改
3. **AiCoachReducer.kt** - 现有Reducer ✅ 需要修改
4. **AiCoachEffectHandler.kt** - 现有EffectHandler ✅ 需要重点修改

#### ThinkingBox 模块现有 MVI 组件
1. **ThinkingBoxContract.kt** - 现有Contract ✅ 需要简化
2. **ThinkingBoxViewModel.kt** - 现有ViewModel ✅ 需要简化
3. **SegmentQueueReducer.kt** - 现有Reducer ✅ 保持不变
4. **ThinkingBox.kt** - 公共API ✅ 需要添加完成回调

### 🎯 重构策略：复用优先，安全删除

#### 阶段1: 接口设计（复用现有，最小改动）
**新增文件**（最小必要）:
1. `features/thinkingbox/api/ThinkingBoxDisplay.kt` - 简化的显示接口
2. `features/thinkingbox/api/ThinkingBoxCompletionListener.kt` - 完成回调接口

**修改现有文件**:
1. `features/coach/aicoach/AiCoachContract.kt` - 添加LayeredPromptBuilder相关Intent/Effect
2. `features/coach/aicoach/AiCoachEffectHandler.kt` - 集成LayeredPromptBuilder调用
3. `features/thinkingbox/ThinkingBox.kt` - 添加完成回调支持

#### 阶段2: Coach模块增强（基于现有架构）
**修改文件**:
1. `features/coach/aicoach/AiCoachEffectHandler.kt` - 核心修改点
   - 注入LayeredPromptBuilder
   - 实现buildAndSendPrompt方法
   - 集成ThinkingBoxDisplay接口

2. `features/coach/aicoach/AiCoachContract.kt` - 最小修改
   - 添加BuildAndSendPrompt Effect
   - 添加ThinkingBoxCompleted Intent

#### 阶段3: ThinkingBox模块简化（保持现有复杂度）
**修改文件**:
1. `features/thinkingbox/ThinkingBox.kt` - 添加完成回调
2. `features/thinkingbox/ThinkingBoxViewModel.kt` - 添加完成检测逻辑

**不需要大改的文件**:
- SegmentQueueReducer.kt ✅ 保持现有逻辑
- ThinkingBoxContract.kt ✅ 只需要添加完成回调Effect

### 🗑️ 安全删除清单

#### 可以删除的文件（确认无引用后）
1. 检查并删除重复的Chat相关测试文件
2. 删除未使用的AI请求处理中间层
3. 清理Coach模块中的重复token流处理代码

#### 不能删除的文件
- 所有现有的MVI组件 ✅ 只修改不删除
- 现有的数据模型 ✅ 各有用途
- ThinkingBox的复杂渲染逻辑 ✅ 保持不变

### 📊 工作量评估

**总工作量**: 3-4天（大幅减少）
- **阶段1**: 0.5天（2个新接口文件）
- **阶段2**: 2天（主要修改AiCoachEffectHandler）
- **阶段3**: 1天（ThinkingBox添加回调）
- **测试验证**: 0.5天

### 🔧 关键修改点

#### 1. AiCoachEffectHandler 核心修改
```kotlin
// 新增依赖注入
@Inject constructor(
    private val layeredPromptBuilder: LayeredPromptBuilder,
    private val thinkingBoxDisplay: ThinkingBoxDisplay,
    // ... 现有依赖
)

// 新增方法
suspend fun buildAndSendPrompt(messageId: String, userInput: String) {
    // 1. 使用LayeredPromptBuilder组装
    // 2. 通过现有网络层发送
    // 3. 启动ThinkingBox显示
}
```

#### 2. ThinkingBox 完成回调
```kotlin
// 在ThinkingBox.kt中添加
@Composable
fun ThinkingBox(
    messageId: String,
    modifier: Modifier = Modifier,
    onComplete: ((String, String) -> Unit)? = null // 新增回调
)
```

## 📋 下一步行动

1. **确认简化方案**: 基于现有架构最小改动的重构方案
2. **开始阶段1**: 创建2个新接口文件
3. **重点修改AiCoachEffectHandler**: 集成LayeredPromptBuilder
4. **添加ThinkingBox回调**: 实现完成通知机制
5. **验证功能完整性**: 确保现有功能不受影响

---

**重构原则**: 最大化复用现有代码，最小化架构变动，通过LayeredPromptBuilder实现Coach的AI请求控制，ThinkingBox专注显示并提供完成回调。
