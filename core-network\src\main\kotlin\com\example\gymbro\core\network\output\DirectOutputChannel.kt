package com.example.gymbro.core.network.output

import com.example.gymbro.core.network.logging.TokenLogCollector
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.filter
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 直接输出通道 - 新架构输出层
 *
 * 设计目标：
 * - 直接流式输出到ThinkingBox
 * - 零中间缓冲，最小延迟
 * - 支持多个订阅者
 * - 背压控制和错误恢复
 * - 🔥 【新增】集成RAW TOKEN批量日志采集
 */
@Singleton
class DirectOutputChannel @Inject constructor(
    private val tokenLogCollector: TokenLogCollector // 🔥 【新增】Token日志采集器
) {

    companion object {
        private const val TAG = "DirectOutputChannel"
        private const val OUTPUT_BUFFER_CAPACITY = 64  // 输出缓冲：64个token
        
        // 🔥 【RAW TOKEN日志采集】输出Token批量收集配置
        private const val OUTPUT_TOKEN_BATCH_SIZE = 15  // 每15个输出token批量采集一次
    }

    // 直接输出流
    private val _outputFlow = MutableSharedFlow<OutputToken>(
        replay = 0,
        extraBufferCapacity = OUTPUT_BUFFER_CAPACITY,
        onBufferOverflow = kotlinx.coroutines.channels.BufferOverflow.SUSPEND
    )

    /**
     * 只读的输出流，供ThinkingBox订阅
     */
    val outputFlow: SharedFlow<OutputToken> = _outputFlow.asSharedFlow()

    // 统计信息
    @Volatile
    private var totalTokensOutput = 0L

    @Volatile
    private var activeSubscribers = 0
    
    // 🔥 【RAW TOKEN日志采集】输出Token批量收集缓存
    private val outputTokenCollectionBuffer = mutableListOf<String>()

    /**
     * 发送处理后的token到输出通道
     *
     * @param token 处理后的token内容
     * @param conversationId 会话ID
     * @param contentType 内容类型
     * @param metadata 附加元数据
     */
    suspend fun sendToken(
        token: String,
        conversationId: String,
        contentType: com.example.gymbro.core.network.detector.ContentType,
        metadata: Map<String, Any> = emptyMap()
    ) {
        if (token.isEmpty()) return

        val outputToken = OutputToken(
            content = token,
            conversationId = conversationId,
            contentType = contentType,
            timestamp = System.currentTimeMillis(),
            metadata = metadata
        )

        try {
            _outputFlow.emit(outputToken)
            totalTokensOutput++
            
            // 🔥 【RAW TOKEN日志采集】收集输出token
            collectOutputTokenForLogging(token, "ThinkingBox", conversationId)

            Timber.tag(TAG).v("📤 输出token: conversationId=$conversationId, " +
                    "length=${token.length}, type=$contentType")

        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ 输出token失败: conversationId=$conversationId")
            throw e
        }
    }

    /**
     * 批量发送token（用于缓冲区清空）
     *
     * @param tokens token列表
     * @param conversationId 会话ID
     * @param contentType 内容类型
     */
    suspend fun sendTokenBatch(
        tokens: List<String>,
        conversationId: String,
        contentType: com.example.gymbro.core.network.detector.ContentType
    ) {
        if (tokens.isEmpty()) return

        val timestamp = System.currentTimeMillis()

        tokens.forEach { token ->
            if (token.isNotEmpty()) {
                val outputToken = OutputToken(
                    content = token,
                    conversationId = conversationId,
                    contentType = contentType,
                    timestamp = timestamp,
                    metadata = mapOf("batch" to true)
                )

                _outputFlow.emit(outputToken)
                totalTokensOutput++
                
                // 🔥 【RAW TOKEN日志采集】收集批量输出token
                collectOutputTokenForLogging(token, "ThinkingBox", conversationId)
            }
        }

        Timber.tag(TAG).d("📤 批量输出: conversationId=$conversationId, " +
                "count=${tokens.size}, type=$contentType")
    }

    /**
     * 订阅特定会话的输出流
     *
     * @param conversationId 会话ID
     * @return 过滤后的输出流
     */
    fun subscribeToConversation(conversationId: String): Flow<OutputToken> {
        activeSubscribers++

        return outputFlow.filter { token ->
            token.conversationId == conversationId
        }
    }

    /**
     * 取消订阅
     */
    fun unsubscribe() {
        activeSubscribers = maxOf(0, activeSubscribers - 1)
    }

    /**
     * 获取输出通道状态
     */
    fun getChannelStatus(): OutputChannelStatus {
        return OutputChannelStatus(
            totalTokensOutput = totalTokensOutput,
            activeSubscribers = activeSubscribers,
            bufferCapacity = OUTPUT_BUFFER_CAPACITY
        )
    }

    /**
     * 清理输出通道
     */
    suspend fun cleanup() {
        // 🔥 【RAW TOKEN日志采集】最终刷新输出token缓冲区
        flushOutputTokenCollectionBuffer("ThinkingBox", "cleanup")
        
        Timber.tag(TAG).i("🧹 清理DirectOutputChannel: totalTokens=$totalTokensOutput, " +
                "subscribers=$activeSubscribers")
        activeSubscribers = 0
    }
    
    /**
     * 🔥 【RAW TOKEN日志采集】收集输出token用于批量日志记录
     */
    private suspend fun collectOutputTokenForLogging(token: String, target: String, conversationId: String) {
        // 使用一个局部变量来避免在临界区内调用挂起函数
        val shouldFlush: Boolean
        val tokensToFlush: List<String>
        
        synchronized(outputTokenCollectionBuffer) {
            outputTokenCollectionBuffer.add(token)
            
            // 检查是否需要刷新
            shouldFlush = outputTokenCollectionBuffer.size >= OUTPUT_TOKEN_BATCH_SIZE
            tokensToFlush = if (shouldFlush) {
                val tokens = outputTokenCollectionBuffer.toList()
                outputTokenCollectionBuffer.clear()
                tokens
            } else {
                emptyList()
            }
        }
        
        // 在临界区外执行挂起函数
        if (shouldFlush) {
            try {
                tokenLogCollector.collectOutputTokens(
                    tokens = tokensToFlush,
                    target = target,
                    conversationId = conversationId
                )
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "⚠️ 输出Token日志采集失败: target=$target")
            }
        }
    }
    
    /**
     * 🔥 【RAW TOKEN日志采集】刷新输出缓冲区中剩余的token
     */
    private suspend fun flushOutputTokenCollectionBuffer(target: String, conversationId: String) {
        val tokensToFlush: List<String>
        
        synchronized(outputTokenCollectionBuffer) {
            tokensToFlush = if (outputTokenCollectionBuffer.isNotEmpty()) {
                val tokens = outputTokenCollectionBuffer.toList()
                outputTokenCollectionBuffer.clear()
                tokens
            } else {
                emptyList()
            }
        }
        
        // 在临界区外执行挂起函数
        if (tokensToFlush.isNotEmpty()) {
            try {
                tokenLogCollector.collectOutputTokens(
                    tokens = tokensToFlush,
                    target = target,
                    conversationId = conversationId
                )
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "⚠️ 最终输出Token刷新失败: target=$target")
            }
        }
    }
}

/**
 * 📤 输出Token数据结构
 */
data class OutputToken(
    val content: String,
    val conversationId: String,
    val contentType: com.example.gymbro.core.network.detector.ContentType,
    val timestamp: Long,
    val metadata: Map<String, Any> = emptyMap()
)

/**
 * 📊 输出通道状态信息
 */
data class OutputChannelStatus(
    val totalTokensOutput: Long,
    val activeSubscribers: Int,
    val bufferCapacity: Int
)
