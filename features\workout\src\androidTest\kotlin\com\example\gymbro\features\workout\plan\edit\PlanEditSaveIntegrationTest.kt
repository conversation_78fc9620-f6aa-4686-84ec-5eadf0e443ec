package com.example.gymbro.features.workout.plan.edit

import androidx.compose.ui.test.*
import androidx.compose.ui.test.junit4.createAndroidComposeRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.workout.plan.repository.PlanRepositoryImpl
import com.example.gymbro.domain.workout.model.WorkoutPlan as DomainWorkoutPlan
import com.example.gymbro.domain.workout.model.plan.DayPlan as DomainDayPlan
import com.example.gymbro.domain.workout.port.JsonProcessorPort
import com.example.gymbro.features.workout.json.processor.PlanJsonProcessor
import com.example.gymbro.features.workout.shared.utils.PlanTestDataFactory
import com.example.gymbro.shared.models.workout.PlanCalendarData
import com.example.gymbro.shared.models.workout.PlanProgressStatus
import com.example.gymbro.shared.models.workout.WorkoutPlan
import com.example.gymbro.testutils.HiltTestActivity
import dagger.hilt.android.testing.HiltAndroidRule
import dagger.hilt.android.testing.HiltAndroidTest
import io.mockk.*
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Before
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import javax.inject.Inject
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * Plan编辑和保存JSON序列化集成测试
 * 
 * 🎯 测试目标：
 * - PlanEditScreen的保存操作端到端测试
 * - DB数据到JSON转换的完整流程测试
 * - Repository层的JSON处理集成测试
 * - 多个DayPlan的复杂场景测试
 * - PlanCalendarData的正确生成
 * - PlanFunctionCallData的正确输出
 * 
 * 🔍 测试覆盖范围：
 * - UI层保存按钮交互
 * - ViewModel到Repository数据流
 * - JSON序列化/反序列化完整流程
 * - Database持久化验证
 * - 错误处理和容错机制
 * - 性能测试（大数据量处理）
 * 
 * 🏗️ 架构集成：
 * - Repository层JSON处理
 * - Domain层数据转换
 * - Shared Models序列化
 * - Database存储验证
 * 
 * <AUTHOR> AI Assistant
 * @since 1.0.0
 */
@HiltAndroidTest
@RunWith(AndroidJUnit4::class)
class PlanEditSaveIntegrationTest {
    
    @get:Rule(order = 0)
    val hiltRule = HiltAndroidRule(this)
    
    @get:Rule(order = 1)
    val composeTestRule = createAndroidComposeRule<HiltTestActivity>()
    
    @Inject
    lateinit var planRepository: PlanRepositoryImpl
    
    private val mockJsonProcessor = mockk<JsonProcessorPort>()
    
    @Before
    fun setUp() {
        hiltRule.inject()
        MockKAnnotations.init(this)
        
        // 设置JsonProcessor mock默认行为
        every { mockJsonProcessor.serializePlan(any()) } returns ModernResult.Success("")
        every { mockJsonProcessor.deserializePlan(any()) } returns ModernResult.Success(
            PlanTestDataFactory.createSimpleDomainPlan()
        )
        every { mockJsonProcessor.generatePlanCalendar(any(), any()) } returns ModernResult.Success(
            PlanTestDataFactory.createSimplePlan().toCalendarJson("2023-08-01")
        )
    }
    
    @After
    fun tearDown() {
        clearAllMocks()
    }
    
    // ==================== UI层保存操作测试 ====================
    
    @Test
    fun `when save button clicked should trigger plan save operation`() = runTest {
        // Given: 设置PlanEditScreen
        val testPlan = PlanTestDataFactory.createSimplePlan()
        
        composeTestRule.setContent {
            PlanEditScreen(
                planId = testPlan.id,
                onNavigateBack = { }
            )
        }
        
        // When: 点击保存按钮
        composeTestRule
            .onNodeWithContentDescription("保存计划")
            .performClick()
        
        // Then: 验证保存状态
        composeTestRule
            .onNodeWithText("保存中...")
            .assertExists()
    }
    
    @Test
    fun `when plan data is modified should enable save button`() = runTest {
        // Given: 初始化PlanEditScreen
        val testPlan = PlanTestDataFactory.createSimplePlan()
        
        composeTestRule.setContent {
            PlanEditScreen(
                planId = testPlan.id,
                onNavigateBack = { }
            )
        }
        
        // When: 修改计划名称
        composeTestRule
            .onNodeWithText("计划名称")
            .performTextClearance()
        composeTestRule
            .onNodeWithText("计划名称")
            .performTextInput("修改后的计划名称")
        
        // Then: 保存按钮应该可用
        composeTestRule
            .onNodeWithContentDescription("保存计划")
            .assertIsEnabled()
    }
    
    // ==================== Repository层JSON处理集成测试 ====================
    
    @Test
    fun `repository should serialize plan to json correctly`() = runTest {
        // Given: 创建测试Plan
        val testPlan = PlanTestDataFactory.createSimplePlan()
        
        // When: Repository序列化Plan
        val result = planRepository.serializePlanToJson(testPlan)
        
        // Then: 验证序列化成功
        assertTrue(result is ModernResult.Success, "序列化应该成功")
        val jsonString = (result as ModernResult.Success).data
        assertNotNull(jsonString, "JSON字符串不应为null")
        assertTrue(jsonString.isNotBlank(), "JSON字符串不应为空")
        
        // 验证JSON内容
        assertTrue(jsonString.contains("\"id\":\"${testPlan.id}\""), "应包含正确的计划ID")
        assertTrue(jsonString.contains("\"name\":\"${testPlan.name}\""), "应包含正确的计划名称")
        assertTrue(jsonString.contains("\"dailySchedule\""), "应包含日程安排")
    }
    
    @Test
    fun `repository should deserialize json to plan correctly`() = runTest {
        // Given: 准备JSON数据
        val originalPlan = PlanTestDataFactory.createSimplePlan()
        val jsonString = originalPlan.toJson()
        
        // When: Repository反序列化JSON
        val result = planRepository.deserializePlanFromJson(jsonString)
        
        // Then: 验证反序列化成功
        assertTrue(result is ModernResult.Success, "反序列化应该成功")
        val deserializedPlan = (result as ModernResult.Success).data
        assertNotNull(deserializedPlan, "反序列化的Plan不应为null")
        
        // 验证数据完整性
        assertEquals(originalPlan.id, deserializedPlan.id, "计划ID应匹配")
        assertEquals(originalPlan.name, deserializedPlan.name, "计划名称应匹配")
        assertEquals(originalPlan.userId, deserializedPlan.userId, "用户ID应匹配")
        assertEquals(originalPlan.totalDays, deserializedPlan.totalDays, "总天数应匹配")
        assertEquals(originalPlan.dailySchedule.size, deserializedPlan.dailySchedule.size, "日程数量应匹配")
    }
    
    @Test
    fun `repository should handle complex plan serialization correctly`() = runTest {
        // Given: 创建复杂的测试Plan
        val complexPlan = PlanTestDataFactory.createComplexPlan()
        
        // When: 序列化和反序列化
        val serializeResult = planRepository.serializePlanToJson(complexPlan)
        assertTrue(serializeResult is ModernResult.Success, "序列化应该成功")
        
        val jsonString = (serializeResult as ModernResult.Success).data
        val deserializeResult = planRepository.deserializePlanFromJson(jsonString)
        
        // Then: 验证复杂数据完整性
        assertTrue(deserializeResult is ModernResult.Success, "反序列化应该成功")
        val deserializedPlan = (deserializeResult as ModernResult.Success).data
        
        // 验证Template引用完整性
        complexPlan.dailySchedule.forEach { (dayNumber, originalDayPlan) ->
            val deserializedDayPlan = deserializedPlan.dailySchedule[dayNumber]
            assertNotNull(deserializedDayPlan, "第${dayNumber}天计划不应为null")
            assertEquals(
                originalDayPlan.templateIds.size,
                deserializedDayPlan.templateIds.size,
                "第${dayNumber}天的Template数量应匹配"
            )
            assertEquals(
                originalDayPlan.templateVersionIds.size,
                deserializedDayPlan.templateVersionIds.size,
                "第${dayNumber}天的TemplateVersion数量应匹配"
            )
        }
    }
    
    // ==================== DB数据到JSON转换流程测试 ====================
    
    @Test
    fun `complete flow from db save to json generation should work`() = runTest {
        // Given: 创建测试Plan
        val testPlan = PlanTestDataFactory.createSimplePlan()
        
        // When: 保存Plan到数据库
        val saveResult = planRepository.savePlan(testPlan)
        assertTrue(saveResult is ModernResult.Success, "保存到数据库应该成功")
        
        // 从数据库获取Plan
        val getResult = planRepository.getPlan(testPlan.id)
        assertTrue(getResult is ModernResult.Success, "从数据库获取应该成功")
        val retrievedPlan = (getResult as ModernResult.Success).data
        
        // 生成JSON
        val jsonResult = planRepository.serializePlanToJson(retrievedPlan)
        assertTrue(jsonResult is ModernResult.Success, "JSON生成应该成功")
        
        // Then: 验证完整流程
        val jsonString = (jsonResult as ModernResult.Success).data
        assertTrue(jsonString.contains("\"id\":\"${testPlan.id}\""), "JSON应包含正确的ID")
        assertTrue(jsonString.contains("\"totalDays\":${testPlan.totalDays}"), "JSON应包含正确的总天数")
    }
    
    @Test
    fun `plan calendar json generation should work correctly`() = runTest {
        // Given: 创建测试Plan并保存
        val testPlan = PlanTestDataFactory.createComplexPlan()
        val saveResult = planRepository.savePlan(testPlan)
        assertTrue(saveResult is ModernResult.Success, "保存应该成功")
        
        // When: 生成calendar.json
        val calendarResult = planRepository.generatePlanCalendarJson(testPlan.id, "2023-08-01")
        
        // Then: 验证calendar数据
        assertTrue(calendarResult is ModernResult.Success, "日历生成应该成功")
        val calendarData = (calendarResult as ModernResult.Success).data
        
        assertNotNull(calendarData, "日历数据不应为null")
        assertNotNull(calendarData.planInfo, "计划信息不应为null")
        assertTrue(calendarData.calendarEntries.isNotEmpty(), "日历条目不应为空")
        
        // 验证日历内容
        assertEquals(testPlan.id, calendarData.planInfo.planId, "计划ID应匹配")
        assertEquals(testPlan.name, calendarData.planInfo.planName, "计划名称应匹配")
        assertEquals(testPlan.totalDays, calendarData.planInfo.totalDays, "总天数应匹配")
        
        // 验证每日条目
        calendarData.calendarEntries.forEach { entry ->
            assertTrue(entry.dayNumber > 0, "天数应大于0")
            assertTrue(entry.date.isNotBlank(), "日期不应为空")
        }
    }
    
    // ==================== 多DayPlan复杂场景测试 ====================
    
    @Test
    fun `multiple dayplan serialization should maintain order and references`() = runTest {
        // Given: 创建包含多个DayPlan的复杂Plan
        val complexPlan = PlanTestDataFactory.createLargePlan() // 30天计划
        
        // When: 序列化
        val jsonString = complexPlan.toJson()
        val deserializedPlan = PlanJsonProcessor.fromJson(jsonString)
        
        // Then: 验证DayPlan顺序和引用
        assertNotNull(deserializedPlan, "反序列化应该成功")
        assertEquals(complexPlan.totalDays, deserializedPlan.totalDays, "总天数应匹配")
        
        // 验证每个DayPlan
        (1..complexPlan.totalDays).forEach { dayNumber ->
            val originalDayPlan = complexPlan.dailySchedule[dayNumber]
            val deserializedDayPlan = deserializedPlan.dailySchedule[dayNumber]
            
            if (originalDayPlan != null && deserializedDayPlan != null) {
                assertEquals(originalDayPlan.dayNumber, deserializedDayPlan.dayNumber, "第${dayNumber}天的天数应匹配")
                assertEquals(originalDayPlan.isRestDay, deserializedDayPlan.isRestDay, "第${dayNumber}天的休息日状态应匹配")
                assertEquals(originalDayPlan.templateIds, deserializedDayPlan.templateIds, "第${dayNumber}天的Template引用应匹配")
                assertEquals(originalDayPlan.templateVersionIds, deserializedDayPlan.templateVersionIds, "第${dayNumber}天的TemplateVersion引用应匹配")
            }
        }
    }
    
    @Test
    fun `mixed workout and rest days should serialize correctly`() = runTest {
        // Given: 创建包含训练日和休息日混合的Plan
        val mixedPlan = PlanTestDataFactory.createComplexPlan()
        
        // When: 序列化和反序列化
        val jsonString = mixedPlan.toJson()
        val deserializedPlan = PlanJsonProcessor.fromJson(jsonString)
        
        // Then: 验证训练日和休息日状态
        assertNotNull(deserializedPlan)
        
        val workoutDays = deserializedPlan.dailySchedule.values.filter { !it.isRestDay }
        val restDays = deserializedPlan.dailySchedule.values.filter { it.isRestDay }
        
        assertTrue(workoutDays.isNotEmpty(), "应包含训练日")
        assertTrue(restDays.isNotEmpty(), "应包含休息日")
        
        // 验证训练日有Template引用
        workoutDays.forEach { dayPlan ->
            assertTrue(
                dayPlan.templateIds.isNotEmpty() || dayPlan.templateVersionIds.isNotEmpty(),
                "训练日应包含Template引用"
            )
        }
        
        // 验证休息日没有Template引用
        restDays.forEach { dayPlan ->
            assertTrue(
                dayPlan.templateIds.isEmpty(),
                "休息日不应包含Template引用"
            )
        }
    }
    
    // ==================== 字段命名规范集成测试 ====================
    
    @Test
    fun `json serialization should follow naming convention`() = runTest {
        // Given: 创建测试Plan
        val testPlan = PlanTestDataFactory.createSimplePlan()
        
        // When: 序列化
        val jsonString = testPlan.toJson()
        
        // Then: 验证JSON字段命名符合camelCase规范
        // 验证Plan级别字段
        assertTrue(jsonString.contains("\"userId\""), "应使用camelCase: userId")
        assertTrue(jsonString.contains("\"totalDays\""), "应使用camelCase: totalDays")
        assertTrue(jsonString.contains("\"dailySchedule\""), "应使用camelCase: dailySchedule")
        assertTrue(jsonString.contains("\"createdAt\""), "应使用camelCase: createdAt")
        assertTrue(jsonString.contains("\"updatedAt\""), "应使用camelCase: updatedAt")
        
        // 验证DayPlan级别字段
        assertTrue(jsonString.contains("\"dayNumber\""), "应使用camelCase: dayNumber")
        assertTrue(jsonString.contains("\"isRestDay\""), "应使用camelCase: isRestDay")
        assertTrue(jsonString.contains("\"templateIds\""), "应使用camelCase: templateIds")
        assertTrue(jsonString.contains("\"templateVersionIds\""), "应使用camelCase: templateVersionIds")
        
        // 验证不存在snake_case字段
        assertFalse(jsonString.contains("\"user_id\""), "不应使用snake_case: user_id")
        assertFalse(jsonString.contains("\"total_days\""), "不应使用snake_case: total_days")
        assertFalse(jsonString.contains("\"daily_schedule\""), "不应使用snake_case: daily_schedule")
        assertFalse(jsonString.contains("\"day_number\""), "不应使用snake_case: day_number")
        assertFalse(jsonString.contains("\"is_rest_day\""), "不应使用snake_case: is_rest_day")
    }
    
    // ==================== 性能测试 ====================
    
    @Test
    fun `large plan serialization performance should be acceptable`() = runTest {
        // Given: 创建大型Plan
        val largePlan = PlanTestDataFactory.createPerformanceTestPlan(totalDays = 100, templatesPerDay = 10)
        
        // When: 测量序列化性能
        val startTime = System.currentTimeMillis()
        val jsonString = largePlan.toJson()
        val serializationTime = System.currentTimeMillis() - startTime
        
        // 测量反序列化性能
        val deserializeStartTime = System.currentTimeMillis()
        val deserializedPlan = PlanJsonProcessor.fromJson(jsonString)
        val deserializationTime = System.currentTimeMillis() - deserializeStartTime
        
        // Then: 验证性能指标
        assertTrue(serializationTime < 1000, "序列化应在1秒内完成，实际：${serializationTime}ms")
        assertTrue(deserializationTime < 1000, "反序列化应在1秒内完成，实际：${deserializationTime}ms")
        assertNotNull(deserializedPlan, "大型Plan反序列化应成功")
        assertEquals(largePlan.totalDays, deserializedPlan.totalDays, "大型Plan数据完整性应保持")
        
        println("性能测试结果 - 100天10模板Plan: 序列化${serializationTime}ms, 反序列化${deserializationTime}ms")
    }
    
    // ==================== 错误处理和容错测试 ====================
    
    @Test
    fun `invalid plan data should be handled gracefully`() = runTest {
        // Given: 创建无效数据Plan
        val invalidPlan = PlanTestDataFactory.createInvalidPlan()
        
        // When: 尝试序列化无效Plan（不应抛出异常）
        val jsonString = invalidPlan.toJson()
        
        // Then: 验证容错处理
        assertNotNull(jsonString, "即使数据无效，序列化也应返回结果")
        assertTrue(jsonString.isNotBlank(), "容错处理应产生有效的JSON字符串")
    }
    
    @Test
    fun `repository error handling should return appropriate error results`() = runTest {
        // Given: 准备无效JSON
        val invalidJson = "{ invalid json format }"
        
        // When: 尝试反序列化无效JSON
        val result = planRepository.deserializePlanFromJson(invalidJson)
        
        // Then: 验证错误处理
        assertTrue(result is ModernResult.Error, "无效JSON应返回Error结果")
        val error = (result as ModernResult.Error).error
        assertNotNull(error.uiMessage, "错误应包含用户友好的消息")
    }
    
    // ==================== 数据完整性验证测试 ====================
    
    @Test
    fun `template reference integrity should be maintained through serialization`() = runTest {
        // Given: 创建包含Template引用的Plan
        val planWithTemplates = PlanTestDataFactory.createComplexPlan()
        
        // When: 完整的序列化-反序列化流程
        val jsonString = planWithTemplates.toJson()
        val deserializedPlan = PlanJsonProcessor.fromJson(jsonString)
        
        // Then: 验证Template引用完整性
        assertNotNull(deserializedPlan)
        
        planWithTemplates.dailySchedule.forEach { (dayNumber, originalDayPlan) ->
            val deserializedDayPlan = deserializedPlan.dailySchedule[dayNumber]
            assertNotNull(deserializedDayPlan, "第${dayNumber}天计划应存在")
            
            // 验证Template ID引用
            assertEquals(
                originalDayPlan.templateIds.sorted(),
                deserializedDayPlan.templateIds.sorted(),
                "第${dayNumber}天的templateIds应完全匹配"
            )
            
            // 验证TemplateVersion ID引用
            assertEquals(
                originalDayPlan.templateVersionIds.sorted(),
                deserializedDayPlan.templateVersionIds.sorted(),
                "第${dayNumber}天的templateVersionIds应完全匹配"
            )
            
            // 验证进度状态
            assertEquals(
                originalDayPlan.progress,
                deserializedDayPlan.progress,
                "第${dayNumber}天的进度状态应匹配"
            )
        }
    }
    
    private fun assertFalse(condition: Boolean, message: String) {
        assertTrue(!condition, message)
    }
}