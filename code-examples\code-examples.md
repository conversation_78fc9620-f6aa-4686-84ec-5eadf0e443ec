package com.example.gymbro.examples.golden_standard

import androidx.compose.runtime.Immutable
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.ui.text.UiText
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * =========================================================================================
 * 🔥 GymBro MVI 黄金标准 v7.0 - 最终代码参考 🔥
 * =========================================================================================
 *
 * 本文件是 GymBro 项目所有架构规范和最佳实践的【最终代码实现】。
 * 它通过一个完整的垂直功能切片——“编辑用户个人简介”——展示了从 UI 到数据层的完整流程。
 *
 * 🎯 本示例的核心目标：
 * 1.  **连接所有规范**: 将 MVI 黄金标准、Repository、UseCase、UI 组件规范等无缝连接。
 * 2.  **提供单一参考**: 作为新功能开发的【首要参考】和模板。
 * 3.  **展示最佳实践**: 演示如何处理用户输入、业务验证、状态更新和错误恢复。
 * 4.  **100% 规范合规**: 完全遵循项目已定义的所有架构原则和质量标准。
 *
 * -----------------------------------------------------------------------------------------
 * 功能场景：用户在个人资料页编辑并保存自己的个人简介。
 * -----------------------------------------------------------------------------------------
 */


// =========================================================================================
//  tầng 1: Presentation Layer - MVI Contract (The API of the Feature)
// =========================================================================================

object ProfileBioContract {

    /**
     * Intent - 用户意图
     *
     * 规范：
     * - 动词命名，清晰表达用户或系统的意图。
     * - `...Result` 后缀用于从异步操作返回的内部 Intent。
     */
    sealed interface Intent : AppIntent {
        // 用户交互触发
        data class LoadInitialBio(val userId: String) : Intent
        data class BioTextChanged(val newText: String) : Intent
        object SaveBioClicked : Intent

        // 内部结果 Intent
        data class LoadBioResult(val result: ModernResult<String>) : Intent
        data class SaveBioResult(val result: ModernResult<Unit>) : Intent
    }

    /**
     * State - UI 状态快照
     *
     * 规范：
     * - 必须使用 @Immutable 注解。
     * - 所有属性都是 `val`。
     * - 包含数据状态、UI 瞬时状态（如加载）和错误状态。
     * - 使用派生属性（get()）来计算衍生值，避免在 UI 层计算。
     */
    @Immutable
    data class State(
        val userId: String? = null,
        val currentBio: String = "",
        val isLoading: Boolean = false,
        val error: UiText? = null,
        val canBeSaved: Boolean = false, // 由 Reducer 计算，UI 直接使用
        val lastSaved: Long? = null
    ) : UiState {
        val characterCount: Int get() = currentBio.length
    }

    /**
     * Effect - 副作用
     *
     * 规范：
     * - 动词命名，描述一个需要执行的、一次性的事件。
     * - 不包含任何执行逻辑，仅为数据载体。
     * - 由 ViewModel 发出，UI 层监听并执行。
     */
    sealed interface Effect : UiEffect {
        data class ShowSuccessToast(val message: UiText) : Effect
        object NavigateBack : Effect
    }
}


// =========================================================================================
// tầng 2: Presentation Layer - MVI Reducer (The Pure State Machine)
// =========================================================================================

class ProfileBioReducer @Inject constructor() :
    Reducer<ProfileBioContract.Intent, ProfileBioContract.State, ProfileBioContract.Effect> {

    override fun reduce(
        intent: ProfileBioContract.Intent,
        currentState: ProfileBioContract.State
    ): ReduceResult<ProfileBioContract.State, ProfileBioContract.Effect> {

        return when (intent) {
            is ProfileBioContract.Intent.LoadInitialBio -> {
                ReduceResult.stateOnly(currentState.copy(isLoading = true, userId = intent.userId))
            }

            is ProfileBioContract.Intent.BioTextChanged -> {
                val canSave = intent.newText.isNotBlank() && intent.newText != currentState.currentBio
                ReduceResult.stateOnly(currentState.copy(currentBio = intent.newText, canBeSaved = canSave))
            }

            is ProfileBioContract.Intent.SaveBioClicked -> {
                ReduceResult.stateOnly(currentState.copy(isLoading = true))
            }

            is ProfileBioContract.Intent.LoadBioResult -> {
                when (val result = intent.result) {
                    is ModernResult.Success -> ReduceResult.stateOnly(
                        currentState.copy(isLoading = false, currentBio = result.data, error = null)
                    )
                    is ModernResult.Error -> ReduceResult.stateOnly(
                        currentState.copy(isLoading = false, error = result.error.uiMessage)
                    )
                    is ModernResult.Loading -> ReduceResult.stateOnly(
                        currentState.copy(isLoading = true)
                    )
                }
            }

            is ProfileBioContract.Intent.SaveBioResult -> {
                when (val result = intent.result) {
                    is ModernResult.Success -> ReduceResult.withEffect(
                        newState = currentState.copy(
                            isLoading = false,
                            canBeSaved = false,
                            lastSaved = System.currentTimeMillis()
                        ),
                        effect = ProfileBioContract.Effect.ShowSuccessToast(UiText.DynamicString("保存成功！"))
                    )
                    is ModernResult.Error -> ReduceResult.withEffect(
                        newState = currentState.copy(isLoading = false),
                        effect = ProfileBioContract.Effect.ShowSuccessToast(result.error.uiMessage)
                    )
                    is ModernResult.Loading -> ReduceResult.stateOnly(
                        currentState.copy(isLoading = true)
                    )
                }
            }
        }
    }
}


// =========================================================================================
// tầng 3: Presentation Layer - MVI ViewModel (The Conductor)
// =========================================================================================

@HiltViewModel
class ProfileBioViewModel @Inject constructor(
    private val reducer: ProfileBioReducer,
    private val updateUserBioUseCase: UpdateUserBioUseCase, // 🔥 注入 Domain 层的 UseCase
    private val getUserBioUseCase: GetUserBioUseCase
) : BaseMviViewModel<ProfileBioContract.Intent, ProfileBioContract.State, ProfileBioContract.Effect>(
    initialState = ProfileBioContract.State()
) {
    override val reducer: Reducer<ProfileBioContract.Intent, ProfileBioContract.State, ProfileBioContract.Effect> = this.reducer

    init {
        initializeEffectHandler()
    }

    override fun initializeEffectHandler() {
        // Effect 的处理逻辑可以放在这里，但对于纯 UI Effect（如 Toast），通常由 UI 层直接处理。
        // 此处为空，因为本示例的 Effects 都是 UI 行为。
    }

    // 重写 dispatch 以处理需要副作用的 Intent
    override fun dispatch(intent: ProfileBioContract.Intent) {
        super.dispatch(intent) // 首先通过 Reducer 更新状态

        viewModelScope.launch {
            when (intent) {
                is ProfileBioContract.Intent.LoadInitialBio -> {
                    val result = getUserBioUseCase(GetUserBioUseCase.Params(intent.userId))
                    super.dispatch(ProfileBioContract.Intent.LoadBioResult(result))
                }
                is ProfileBioContract.Intent.SaveBioClicked -> {
                    val userId = currentState.userId ?: return@launch
                    val result = updateUserBioUseCase(
                        UpdateUserBioUseCase.Params(userId, currentState.currentBio)
                    )
                    super.dispatch(ProfileBioContract.Intent.SaveBioResult(result))
                }
                else -> { /* No side-effect needed */ }
            }
        }
    }
}


// =========================================================================================
// tầng 4: Domain Layer - UseCase (The Business Logic)
// =========================================================================================

/**
 * 更新用户简介的 UseCase
 *
 * 规范：
 * - 封装单一、具体的业务规则。
 * - 无状态，纯 Kotlin，不依赖 Android 框架。
 * - 通过构造函数注入 Repository 接口。
 * - 执行业务验证（例如，长度限制）。
 */
class UpdateUserBioUseCase @Inject constructor(
    private val userRepository: UserRepository
) {
    data class Params(val userId: String, val newBio: String)

    suspend operator fun invoke(params: Params): ModernResult<Unit> {
        // 业务规则 1: 简介不能为空
        if (params.newBio.isBlank()) {
            return ModernResult.Error(
                ModernDataError(
                    "UpdateUserBioUseCase",
                    GlobalErrorType.Data.ValidationError,
                    ErrorCategory.DATA,
                    UiText.DynamicString("个人简介不能为空。")
                )
            )
        }
        // 业务规则 2: 长度不能超过 200
        if (params.newBio.length > 200) {
            return ModernResult.Error(
                ModernDataError(
                    "UpdateUserBioUseCase",
                    GlobalErrorType.Data.ValidationError,
                    ErrorCategory.DATA,
                    UiText.DynamicString("个人简介不能超过 200 个字符。")
                )
            )
        }

        Timber.d("UseCase: 验证通过，调用 Repository 更新简介。")
        return userRepository.updateUserBio(params.userId, params.newBio)
    }
}

class GetUserBioUseCase @Inject constructor(
    private val userRepository: UserRepository
) {
    data class Params(val userId: String)

    suspend operator fun invoke(params: Params): ModernResult<String> {
        return userRepository.getUserBio(params.userId)
    }
}


// =========================================================================================
// tầng 5: Data Layer - Repository (The Data Abstraction)
// =========================================================================================

/**
 * 用户 Repository 接口
 *
 * 规范：
 * - 定义 Domain 层与 Data 层之间的契约。
 * - 方法返回 `ModernResult` 或 `Flow<ModernResult>`。
 * - 隐藏数据来源（本地数据库、网络 API）的复杂性。
 */
interface UserRepository {
    suspend fun getUserBio(userId: String): ModernResult<String>
    suspend fun updateUserBio(userId: String, newBio: String): ModernResult<Unit>
}

/**
 * Repository 实现
 *
 * 规范：
 * - 实现 Repository 接口。
 * - 遵循 Local-First 架构：先操作本地，再同步到网络。
 * - 注入 DAO (本地) 和 ApiService (远程)。
 * - 统一处理并转换数据源的错误。
 */
class UserRepositoryImpl @Inject constructor(
    // private val userDao: UserDao,
    // private val userApiService: UserApiService
) : UserRepository {

    // 模拟实现
    private var userBioStore: MutableMap<String, String> = mutableMapOf("user123" to "初始个人简介...")

    override suspend fun getUserBio(userId: String): ModernResult<String> {
        return try {
            Timber.d("Repository: 正在从数据源获取用户 ($userId) 的简介...")
            val bio = userBioStore[userId]
            if (bio != null) {
                ModernResult.Success(bio)
            } else {
                ModernResult.Error(ModernDataError("getUserBio", GlobalErrorType.Data.NotFound, ErrorCategory.DATA, UiText.DynamicString("未找到用户")))
            }
        } catch (e: Exception) {
            ModernResult.Error(ModernDataError("getUserBio", GlobalErrorType.System.General, ErrorCategory.SYSTEM, UiText.DynamicString(e.message ?: "未知错误")))
        }
    }

    override suspend fun updateUserBio(userId: String, newBio: String): ModernResult<Unit> {
        return try {
            Timber.d("Repository: 正在将用户 ($userId) 的简介更新为 '$newBio'...")
            userBioStore[userId] = newBio
            // 在真实场景中，这里会先更新 Room，然后发起网络请求
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            ModernResult.Error(ModernDataError("updateUserBio", GlobalErrorType.System.General, ErrorCategory.SYSTEM, UiText.DynamicString(e.message ?: "未知错误")))
        }
    }
}


// =========================================================================================
// tầng 6: UI Layer - Composable (The View)
// =========================================================================================

/**
 * ProfileBioScreen (示例 Composable)
 *
 * 规范：
 * - 尽可能是无状态的 (Stateless)。
 * - 接收 State 对象来渲染 UI。
 * - 通过 lambda 回调发送 Intent，而不是直接调用 ViewModel 方法。
 * - 100% 使用 designSystem Tokens，无硬编码值。
 * - 遵循 Box+LazyColumn+Surface 布局模式（如果适用）。
 *
 * @Composable
 * fun ProfileBioScreen(
 * viewModel: ProfileBioViewModel = hiltViewModel()
 * ) {
 * val state by viewModel.state.collectAsStateWithLifecycle()
 * val context = LocalContext.current
 *
 * // 监听一次性 Effect
 * LaunchedEffect(Unit) {
 * viewModel.effects.collect { effect ->
 * when (effect) {
 * is ProfileBioContract.Effect.ShowSuccessToast -> {
 * Toast.makeText(context, effect.message.asString(context), Toast.LENGTH_SHORT).show()
 * }
 * is ProfileBioContract.Effect.NavigateBack -> {
 * // navController.popBackStack()
 * }
 * }
 * }
 * }
 *
 * ProfileBioContent(
 * state = state,
 * onIntent = viewModel::dispatch // 将 dispatch 方法作为回调传递
 * )
 * }
 *
 * @Composable
 * fun ProfileBioContent(
 * state: ProfileBioContract.State,
 * onIntent: (ProfileBioContract.Intent) -> Unit
 * ) {
 * Column(
 * modifier = Modifier
 * .fillMaxSize()
 * .padding(Tokens.Spacing.Medium) // 🔥 Token化
 * ) {
 * OutlinedTextField(
 * value = state.currentBio,
 * onValueChange = { newText ->
 * onIntent(ProfileBioContract.Intent.BioTextChanged(newText)) // 🔥 发送 Intent
 * },
 * label = { Text("个人简介") },
 * modifier = Modifier.fillMaxWidth().weight(1f),
 * isError = state.error != null
 * )
 *
 * state.error?.let {
 * Text(
 * text = it.asString(),
 * color = MaterialTheme.colorScheme.error,
 * style = MaterialTheme.typography.bodySmall
 * )
 * }
 *
 * Spacer(modifier = Modifier.height(Tokens.Spacing.Small))
 *
 * Text(
 * text = "${state.characterCount} / 200",
 * style = MaterialTheme.typography.bodySmall,
 * modifier = Modifier.align(Alignment.End)
 * )
 *
 * Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))
 *
 * Button(
 * onClick = { onIntent(ProfileBioContract.Intent.SaveBioClicked) }, // 🔥 发送 Intent
 * enabled = state.canBeSaved && !state.isLoading,
 * modifier = Modifier.fillMaxWidth()
 * ) {
 * if (state.isLoading) {
 * CircularProgressIndicator(modifier = Modifier.size(Tokens.Size.IconSmall))
 * } else {
 * Text("保存")
 * }
 * }
 * }
 * }
 */
