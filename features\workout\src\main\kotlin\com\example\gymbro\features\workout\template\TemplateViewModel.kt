﻿package com.example.gymbro.features.workout.template

import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.features.workout.template.internal.effect.TemplateEffectHandler
import com.example.gymbro.features.workout.template.internal.reducer.TemplateReducer
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * =========================================================================================
 * 🔥 GymBro Template ViewModel - 遵循黄金标准 🔥
 * =========================================================================================
 *
 * 本ViewModel遵循ProfileBio黄金标准，作为MVI架构的指挥者。
 *
 * 🎯 核心职责：
 * 1. 协调Reducer和EffectHandler
 * 2. 管理ViewModelScope中的副作用
 * 3. 提供统一的dispatch入口
 * 4. 初始化数据加载
 *
 * ✅ 已优化：
 * - 遵循BaseMviViewModel继承
 * - 正确的Effect处理初始化
 * - 简化的Reducer集成
 */
@HiltViewModel
class TemplateViewModel @Inject internal constructor(
    private val templateReducer: TemplateReducer,
    private val templateEffectHandler: TemplateEffectHandler,
) : BaseMviViewModel<TemplateContract.Intent, TemplateContract.State, TemplateContract.Effect>(
    initialState = TemplateContract.State(),
) {

    override val reducer: Reducer<TemplateContract.Intent, TemplateContract.State, TemplateContract.Effect> =
        templateReducer

    init {
        initializeEffectHandler()
        // 初始化数据加载
        dispatch(TemplateContract.Intent.LoadTemplates)
        dispatch(TemplateContract.Intent.LoadDrafts)
    }

    override fun initializeEffectHandler() {
        viewModelScope.launch {
            effect.collectLatest { effect ->
                templateEffectHandler.handleEffect(
                    effect = effect,
                    effectScope = viewModelScope,
                    dispatch = ::dispatch
                )
            }
        }
    }
}
