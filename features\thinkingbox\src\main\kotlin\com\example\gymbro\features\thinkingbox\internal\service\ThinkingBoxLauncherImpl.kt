package com.example.gymbro.features.thinkingbox.internal.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.domain.coach.repository.AICoachRepository
import com.example.gymbro.features.thinkingbox.api.ThinkingBoxCompletionListener
import com.example.gymbro.features.thinkingbox.api.ThinkingBoxError
import com.example.gymbro.features.thinkingbox.api.ThinkingBoxLauncher
import com.example.gymbro.features.thinkingbox.api.ThinkingBoxStatus
import com.example.gymbro.features.thinkingbox.api.ThinkingProgress
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ThinkingBoxLauncher实现类
 *
 * 🔥 【职责分离重构】核心功能：
 * - 启动AI处理并委托给AICoachRepository
 * - 管理处理状态和生命周期
 * - 处理错误和超时情况
 * - 提供取消和状态查询功能
 */
@Singleton
class ThinkingBoxLauncherImpl @Inject constructor(
    private val aiCoachRepository: AICoachRepository,
    private val ioDispatcher: CoroutineDispatcher
) : ThinkingBoxLauncher {

    private val scope = CoroutineScope(SupervisorJob() + ioDispatcher)
    private val activeJobs = ConcurrentHashMap<String, Job>()
    private val processingStatus = ConcurrentHashMap<String, ThinkingBoxStatus>()

    override suspend fun startThinking(
        messageId: String,
        userPrompt: String,
        completionListener: ThinkingBoxCompletionListener
    ): ModernResult<Unit> {
        Timber.d("TB-Launcher: 🚀 启动AI思考: messageId=$messageId")

        return try {
            // 取消之前的任务（如果存在）
            cancelThinking(messageId)

            // 设置初始状态
            processingStatus[messageId] = ThinkingBoxStatus.Processing()

            // 使用AICoachRepository的流式响应方法
            val job = aiCoachRepository.getStreamingResponseLegacy(messageId, userPrompt)
                .onEach { token ->
                    Timber.d("TB-Launcher: 📡 收到AI token: messageId=$messageId, token=${token.take(50)}...")
                    // 这里可以处理流式响应token
                }
                .catch { error ->
                    Timber.e(error, "TB-Launcher: ❌ AI请求失败: messageId=$messageId")
                    processingStatus[messageId] = ThinkingBoxStatus.Failed(
                        error = ThinkingBoxError.AiRequestFailed(error.message ?: "Unknown error"),
                        failedAt = System.currentTimeMillis()
                    )
                    completionListener.onDisplayError(messageId, error, null)
                }
                .onCompletion { cause ->
                    if (cause == null) {
                        Timber.d("TB-Launcher: ✅ AI处理完成: messageId=$messageId")
                        processingStatus[messageId] = ThinkingBoxStatus.Completed(
                            completedAt = System.currentTimeMillis(),
                            duration = 0 // TODO: 计算实际持续时间
                        )
                        // 模拟完成回调 - 在实际实现中需要从AI响应中提取内容
                        completionListener.onDisplayComplete(
                            messageId = messageId,
                            thinkingProcess = "思考过程占位符", // TODO: 从响应中提取
                            finalContent = "最终内容占位符", // TODO: 从响应中提取
                            metadata = mapOf(
                                "completedAt" to System.currentTimeMillis()
                            )
                        )
                    }
                    activeJobs.remove(messageId)
                }
                .flowOn(ioDispatcher)
                .launchIn(scope)

            activeJobs[messageId] = job

            Timber.d("TB-Launcher: ✅ AI思考已启动: messageId=$messageId")
            ModernResult.Success(Unit)

        } catch (e: Exception) {
            Timber.e(e, "TB-Launcher: ❌ 启动失败: messageId=$messageId")
            processingStatus[messageId] = ThinkingBoxStatus.Failed(
                error = ThinkingBoxError.UnknownError(e.message ?: "Unknown error"),
                failedAt = System.currentTimeMillis()
            )
            ModernResult.Error(
                error = ModernDataError(
                    operationName = "startThinking",
                    errorType = GlobalErrorType.System.General,
                    category = ErrorCategory.SYSTEM,
                    cause = e
                )
            )
        }
    }

    override suspend fun cancelThinking(messageId: String): ModernResult<Unit> {
        Timber.d("TB-Launcher: 🛑 取消AI思考: messageId=$messageId")

        return try {
            activeJobs[messageId]?.cancel()
            activeJobs.remove(messageId)
            processingStatus[messageId] = ThinkingBoxStatus.Idle

            Timber.d("TB-Launcher: ✅ AI思考已取消: messageId=$messageId")
            ModernResult.Success(Unit)

        } catch (e: Exception) {
            Timber.e(e, "TB-Launcher: ❌ 取消失败: messageId=$messageId")
            ModernResult.Error(
                error = ModernDataError(
                    operationName = "cancelThinking", 
                    errorType = GlobalErrorType.System.General,
                    category = ErrorCategory.SYSTEM,
                    cause = e
                )
            )
        }
    }

    override suspend fun getThinkingStatus(messageId: String): ModernResult<ThinkingBoxStatus> {
        Timber.d("TB-Launcher: 🔍 查询思考状态: messageId=$messageId")

        return try {
            val status = processingStatus[messageId] ?: ThinkingBoxStatus.Idle
            ModernResult.Success(status)

        } catch (e: Exception) {
            Timber.e(e, "TB-Launcher: ❌ 状态查询失败: messageId=$messageId")
            ModernResult.Error(
                error = ModernDataError(
                    operationName = "getThinkingStatus",
                    errorType = GlobalErrorType.System.General,
                    category = ErrorCategory.SYSTEM,
                    cause = e
                )
            )
        }
    }

    override suspend fun getActiveThinkingSessions(): ModernResult<List<String>> {
        Timber.d("TB-Launcher: 📋 获取活跃会话列表")

        return try {
            val activeSessions = activeJobs.keys.toList()
            Timber.d("TB-Launcher: ✅ 活跃会话数量: ${activeSessions.size}")
            ModernResult.Success(activeSessions)

        } catch (e: Exception) {
            Timber.e(e, "TB-Launcher: ❌ 获取活跃会话失败")
            ModernResult.Error(
                error = ModernDataError(
                    operationName = "getActiveThinkingSessions",
                    errorType = GlobalErrorType.System.General,
                    category = ErrorCategory.SYSTEM,
                    cause = e
                )
            )
        }
    }

    override suspend fun clearAllThinkingSessions(): ModernResult<Unit> {
        Timber.d("TB-Launcher: 🧹 清理所有活跃会话")

        return try {
            activeJobs.values.forEach { job ->
                job.cancel()
            }
            activeJobs.clear()
            processingStatus.clear()

            Timber.d("TB-Launcher: ✅ 所有活跃会话已清理")
            ModernResult.Success(Unit)

        } catch (e: Exception) {
            Timber.e(e, "TB-Launcher: ❌ 清理会话失败")
            ModernResult.Error(
                error = ModernDataError(
                    operationName = "clearAllThinkingSessions",
                    errorType = GlobalErrorType.System.General,
                    category = ErrorCategory.SYSTEM,
                    cause = e
                )
            )
        }
    }
}