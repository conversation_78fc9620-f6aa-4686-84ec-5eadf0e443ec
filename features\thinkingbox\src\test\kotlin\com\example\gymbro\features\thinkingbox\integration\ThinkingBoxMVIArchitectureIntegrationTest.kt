package com.example.gymbro.features.thinkingbox.integration

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import com.example.gymbro.features.thinkingbox.internal.reducer.SegmentQueueReducer
import com.example.gymbro.features.thinkingbox.internal.reducer.ThinkingBoxReducer
import io.mockk.*
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * ThinkingBox MVI架构集成测试
 *
 * 🎯 测试目标：验证MVI架构在ThinkingBox中的正确实现
 * 📊 覆盖率目标：≥90%
 * 🔧 测试框架：JUnit 5 + MockK + kotlin.test
 * 🔥 重点：MVI合规性、状态不可变性、Effect处理、四条铁律
 * 
 * 测试覆盖：
 * - Intent → State → Effect 完整MVI流程
 * - 状态不可变性验证
 * - Effect生成和处理验证
 - ThinkingEvent与Contract Intent的集成
 * - 四条铁律在MVI架构下的实现
 */
@DisplayName("ThinkingBox MVI架构集成测试")
class ThinkingBoxMVIIntegrationTest {

    private lateinit var segmentQueueReducer: SegmentQueueReducer
    private lateinit var thinkingBoxReducer: ThinkingBoxReducer

    @BeforeEach
    fun setUp() {
        segmentQueueReducer = SegmentQueueReducer()
        thinkingBoxReducer = ThinkingBoxReducer(segmentQueueReducer)
    }

    @Nested
    @DisplayName("MVI完整流程集成测试")
    inner class MVICompleteFlowIntegrationTests {

        @Test
        @DisplayName("完整MVI流程：Intent → State → Effect")
        fun `complete MVI flow Intent to State to Effect`() = runTest {
            // Given - 初始状态
            val initialState = ThinkingBoxContract.State()
            val messageId = "mvi-flow-test"

            // When - 处理初始化Intent
            val initIntent = ThinkingBoxContract.Intent.Initialize(messageId)
            val initResult = thinkingBoxReducer.reduceInternal(initIntent, initialState)

            // Then - 验证状态转换
            assertEquals(messageId, initResult.state.messageId)
            assertFalse(initResult.state.isLoading)
            assertTrue(initResult.state.segmentsQueue.isEmpty())
            assertFalse(initResult.state.thinkingClosed)
            assertFalse(initResult.state.finalReady)

            // 验证Effect生成
            assertEquals(1, initResult.effects.size)
            val effect = initResult.effects.first()
            assertTrue(effect is ThinkingBoxContract.Effect.StartTokenStreamListening)
            assertEquals(messageId, (effect as ThinkingBoxContract.Effect.StartTokenStreamListening).messageId)

            // 验证原始状态不变
            assertEquals("", initialState.messageId)
            assertFalse(initialState.isLoading)
        }

        @Test
        @DisplayName("ThinkingEvent处理应该正确更新Contract State")
        fun `ThinkingEvent processing should correctly update Contract State`() = runTest {
            // Given - 已初始化的状态
            val messageId = "thinking-event-test"
            val initializedState = ThinkingBoxContract.State(messageId = messageId)

            // When - 处理一系列ThinkingEvent
            val events = listOf(
                ThinkingEvent.SegmentStarted("perthink", SegmentKind.PERTHINK, "Bro is thinking"),
                ThinkingEvent.SegmentText("预思考内容"),
                ThinkingEvent.SegmentClosed("perthink"),
                ThinkingEvent.SegmentStarted("analysis", SegmentKind.PHASE, "分析阶段"),
                ThinkingEvent.SegmentText("分析内容"),
                ThinkingEvent.ThinkingClosed
            )

            var currentState = initializedState
            val allEffects = mutableListOf<ThinkingBoxContract.Effect>()

            events.forEach { event ->
                val result = thinkingBoxReducer.handleThinkingEvent(event, currentState)
                currentState = result.state
                allEffects.addAll(result.effects)
            }

            // Then - 验证状态正确更新
            assertTrue(currentState.thinkingClosed)
            assertTrue(currentState.segmentsQueue.isNotEmpty())
            
            // 验证段队列内容
            val segments = currentState.segmentsQueue
            assertTrue(segments.any { it.id == "perthink" && it.kind == SegmentKind.PERTHINK })
            assertTrue(segments.any { it.id == "analysis" && it.kind == SegmentKind.PHASE })
            assertTrue(segments.any { it.content.contains("预思考内容") })
            assertTrue(segments.any { it.content.contains("分析内容") })

            // 验证Effects
            assertTrue(allEffects.any { it is ThinkingBoxContract.Effect.NotifyHistoryThinking })
        }

        @Test
        @DisplayName("UI交互Intent应该正确处理")
        fun `UI interaction Intent should be handled correctly`() = runTest {
            // Given - 有segments的状态
            val messageId = "ui-interaction-test"
            val stateWithSegments = ThinkingBoxContract.State(
                messageId = messageId,
                segmentsQueue = listOf(
                    ThinkingBoxContract.SegmentUi("seg1", SegmentKind.PHASE, "Phase 1", "content", true, false)
                )
            )

            // When - 处理UI段渲染完成Intent
            val uiIntent = ThinkingBoxContract.Intent.UiSegmentRendered("seg1")
            val result = thinkingBoxReducer.reduceInternal(uiIntent, stateWithSegments)

            // Then - 验证状态更新
            assertNotNull(result.state)
            assertEquals(messageId, result.state.messageId)
            
            // 验证segment的rendered状态可能被更新（取决于内部逻辑）
            assertTrue(result.state.segmentsQueue.isNotEmpty())
        }
    }

    @Nested
    @DisplayName("状态不可变性验证")
    inner class StateImmutabilityVerification {

        @Test
        @DisplayName("State对象应该是不可变的")
        fun `State objects should be immutable`() = runTest {
            // Given - 原始状态
            val originalState = ThinkingBoxContract.State(
                messageId = "immutable-test",
                segmentsQueue = listOf(
                    ThinkingBoxContract.SegmentUi("test", SegmentKind.PHASE, "Test", "content", true)
                ),
                finalReady = false,
                thinkingClosed = false
            )

            // When - 执行状态变更操作
            val resetIntent = ThinkingBoxContract.Intent.Reset
            val result = thinkingBoxReducer.reduceInternal(resetIntent, originalState)

            // Then - 原始状态应该不变
            assertEquals("immutable-test", originalState.messageId)
            assertEquals(1, originalState.segmentsQueue.size)
            assertFalse(originalState.finalReady)
            assertFalse(originalState.thinkingClosed)

            // 新状态应该不同
            assertEquals("", result.state.messageId)
            assertTrue(result.state.segmentsQueue.isEmpty())
            assertFalse(result.state.finalReady)
            assertFalse(result.state.thinkingClosed)
        }

        @Test
        @DisplayName("SegmentUi应该是不可变的")
        fun `SegmentUi should be immutable`() = runTest {
            // Given - 包含SegmentUi的状态
            val originalSegment = ThinkingBoxContract.SegmentUi(
                id = "immutable-segment",
                kind = SegmentKind.PHASE,
                title = "Original Title",
                content = "Original Content",
                isComplete = false,
                isRendered = false
            )
            
            val stateWithSegment = ThinkingBoxContract.State(
                messageId = "segment-immutable-test",
                segmentsQueue = listOf(originalSegment)
            )

            // When - 处理可能影响segment的操作
            val uiIntent = ThinkingBoxContract.Intent.UiSegmentRendered("immutable-segment")
            val result = thinkingBoxReducer.reduceInternal(uiIntent, stateWithSegment)

            // Then - 原始segment对象应该不变
            assertEquals("immutable-segment", originalSegment.id)
            assertEquals("Original Title", originalSegment.title)
            assertEquals("Original Content", originalSegment.content)
            assertFalse(originalSegment.isComplete)
            assertFalse(originalSegment.isRendered)

            // 新状态中的segment可能有不同的isRendered值，但应该是新对象
            val resultSegments = result.state.segmentsQueue
            if (resultSegments.isNotEmpty()) {
                val resultSegment = resultSegments.find { it.id == "immutable-segment" }
                if (resultSegment != null) {
                    // 验证这是不同的对象实例（如果有变化的话）
                    assertTrue(resultSegment.id == originalSegment.id)
                }
            }
        }

        @Test
        @DisplayName("嵌套状态更新应该保持不可变性")
        fun `nested state updates should maintain immutability`() = runTest {
            // Given - 复杂的嵌套状态
            val complexState = ThinkingBoxContract.State(
                messageId = "nested-test",
                segmentsQueue = listOf(
                    ThinkingBoxContract.SegmentUi("seg1", SegmentKind.PERTHINK, "Pre", "content1", true),
                    ThinkingBoxContract.SegmentUi("seg2", SegmentKind.PHASE, "Phase", "content2", false)
                ),
                finalContent = "Original final content",
                thinkingClosed = false
            )

            // When - 执行多个操作
            val operations = listOf(
                ThinkingBoxContract.Intent.UiSegmentRendered("seg1"),
                ThinkingBoxContract.Intent.UiSegmentRendered("seg2"),
                ThinkingBoxContract.Intent.ClearError
            )

            val originalSegmentsSize = complexState.segmentsQueue.size
            val originalFinalContent = complexState.finalContent
            
            var currentState = complexState
            operations.forEach { intent ->
                val result = thinkingBoxReducer.reduceInternal(intent, currentState)
                currentState = result.state
            }

            // Then - 原始状态应该完全不变
            assertEquals("nested-test", complexState.messageId)
            assertEquals(originalSegmentsSize, complexState.segmentsQueue.size)
            assertEquals(originalFinalContent, complexState.finalContent)
            assertFalse(complexState.thinkingClosed)

            // 验证原始segments没有被修改
            val originalSeg1 = complexState.segmentsQueue[0]
            val originalSeg2 = complexState.segmentsQueue[1]
            assertEquals("seg1", originalSeg1.id)
            assertEquals("seg2", originalSeg2.id)
            assertTrue(originalSeg1.isComplete)
            assertFalse(originalSeg2.isComplete)
        }
    }

    @Nested
    @DisplayName("Effect生成和处理验证")
    inner class EffectGenerationAndHandlingVerification {

        @Test
        @DisplayName("应该在正确时机生成History Effects")
        fun `should generate History Effects at correct timing`() = runTest {
            // Given - 初始化状态
            val messageId = "history-effect-test"
            val initialState = ThinkingBoxContract.State(messageId = messageId)

            // When - 模拟完整的思考流程
            val thinkingEvents = listOf(
                ThinkingEvent.SegmentStarted("test-segment", SegmentKind.PHASE, "Test Phase"),
                ThinkingEvent.SegmentText("思考内容"),
                ThinkingEvent.SegmentClosed("test-segment"),
                ThinkingEvent.ThinkingClosed,  // 应该触发NotifyHistoryThinking
                ThinkingEvent.FinalStart,
                ThinkingEvent.FinalContent("最终答案"),
                ThinkingEvent.FinalComplete    // 应该触发NotifyHistoryFinal
            )

            var currentState = initialState
            val allEffects = mutableListOf<ThinkingBoxContract.Effect>()

            thinkingEvents.forEach { event ->
                val result = thinkingBoxReducer.handleThinkingEvent(event, currentState)
                currentState = result.state
                allEffects.addAll(result.effects)
            }

            // Then - 验证History Effects生成
            val thinkingHistoryEffects = allEffects.filterIsInstance<ThinkingBoxContract.Effect.NotifyHistoryThinking>()
            val finalHistoryEffects = allEffects.filterIsInstance<ThinkingBoxContract.Effect.NotifyHistoryFinal>()

            assertTrue(thinkingHistoryEffects.isNotEmpty(), "应该生成思考History Effect")
            assertTrue(finalHistoryEffects.isNotEmpty(), "应该生成最终答案History Effect")

            // 验证Effect内容
            val thinkingEffect = thinkingHistoryEffects.first()
            assertEquals(messageId, thinkingEffect.messageId)
            assertTrue(thinkingEffect.thinkingMarkdown.isNotEmpty())

            val finalEffect = finalHistoryEffects.first()
            assertEquals(messageId, finalEffect.messageId)
            assertTrue(finalEffect.finalMarkdown.contains("最终答案"))
        }

        @Test
        @DisplayName("Effect应该包含正确的数据")
        fun `Effects should contain correct data`() = runTest {
            // Given
            val messageId = "effect-data-test"
            val initialState = ThinkingBoxContract.State()

            // When - 初始化
            val initIntent = ThinkingBoxContract.Intent.Initialize(messageId)
            val initResult = thinkingBoxReducer.reduceInternal(initIntent, initialState)

            // Then - 验证StartTokenStreamListening Effect
            assertEquals(1, initResult.effects.size)
            val effect = initResult.effects.first() as ThinkingBoxContract.Effect.StartTokenStreamListening
            assertEquals(messageId, effect.messageId)
        }

        @Test
        @DisplayName("错误状态应该生成适当的Effects")
        fun `error states should generate appropriate Effects`() = runTest {
            // Given - 有错误的状态
            val errorState = ThinkingBoxContract.State(
                messageId = "error-effect-test",
                error = com.example.gymbro.core.ui.text.UiText.StringResource(
                    com.example.gymbro.core.ui.text.UiText.StringResource.ResId(android.R.string.unknownName)
                )
            )

            // When - 清除错误
            val clearErrorIntent = ThinkingBoxContract.Intent.ClearError
            val result = thinkingBoxReducer.reduceInternal(clearErrorIntent, errorState)

            // Then - 验证错误被清除且没有不必要的Effects
            assertNull(result.state.error)
            assertTrue(result.effects.isEmpty()) // ClearError不应该产生Effects
        }
    }

    @Nested
    @DisplayName("四条铁律MVI实现验证")
    inner class FourIronLawsMVIImplementationVerification {

        @Test
        @DisplayName("【铁律1】UI绝对不重组刷新 - 通过segmentsQueue增量更新实现")
        fun `Iron Law 1 - UI absolute no recomposition through incremental segmentsQueue updates`() = runTest {
            // Given
            val messageId = "iron-law-1-test"
            val initialState = ThinkingBoxContract.State(messageId = messageId)

            // When - 逐步添加segments（模拟增量更新）
            val events = listOf(
                ThinkingEvent.SegmentStarted("seg1", SegmentKind.PERTHINK, "First"),
                ThinkingEvent.SegmentText("First content"),
                ThinkingEvent.SegmentStarted("seg2", SegmentKind.PHASE, "Second"),
                ThinkingEvent.SegmentText("Second content")
            )

            var currentState = initialState
            val stateSnapshots = mutableListOf<ThinkingBoxContract.State>()

            events.forEach { event ->
                val result = thinkingBoxReducer.handleThinkingEvent(event, currentState)
                currentState = result.state
                stateSnapshots.add(currentState)
            }

            // Then - 验证segmentsQueue只增长，不重组
            assertEquals(4, stateSnapshots.size)
            
            // 第一个快照：应该有1个segment
            assertTrue(stateSnapshots[0].segmentsQueue.size >= 1)
            
            // 后续快照：segment数量只增不减（符合增量更新原则）
            for (i in 1 until stateSnapshots.size) {
                assertTrue(
                    stateSnapshots[i].segmentsQueue.size >= stateSnapshots[i-1].segmentsQueue.size,
                    "段队列只能增长，不能减少（避免重组）"
                )
            }
        }

        @Test
        @DisplayName("【铁律2】优雅1秒30字符显示 - 通过isComplete状态控制实现")
        fun `Iron Law 2 - Elegant 1 second 30 characters display through isComplete state control`() = runTest {
            // Given
            val messageId = "iron-law-2-test"
            val initialState = ThinkingBoxContract.State(messageId = messageId)

            // When - 创建30字符的段
            val thirtyCharContent = "这是一个恰好三十个字符的测试内容用于验证显示速度控制功能"
            assertTrue(thirtyCharContent.length >= 30, "内容应该至少30字符")

            val events = listOf(
                ThinkingEvent.SegmentStarted("speed-test", SegmentKind.PHASE, "Speed Test"),
                ThinkingEvent.SegmentText(thirtyCharContent),
                ThinkingEvent.SegmentClosed("speed-test")
            )

            var currentState = initialState
            events.forEach { event ->
                val result = thinkingBoxReducer.handleThinkingEvent(event, currentState)
                currentState = result.state
            }

            // Then - 验证段的完成状态用于控制显示速度
            val segment = currentState.segmentsQueue.find { it.id == "speed-test" }
            assertNotNull(segment)
            assertTrue(segment!!.isComplete) // 段闭合后应该标记为完成
            assertEquals(thirtyCharContent, segment.content)
            
            // 验证isComplete可以用于UI层控制33ms/字符的显示速度
            assertTrue(segment.content.isNotEmpty())
        }

        @Test
        @DisplayName("【铁律3】思考框硬限制1/3屏高 - 通过shouldShowAIThinkingCard控制")
        fun `Iron Law 3 - ThinkingBox hard limit 1-3 screen height through shouldShowAIThinkingCard control`() = runTest {
            // Given
            val messageId = "iron-law-3-test"
            val initialState = ThinkingBoxContract.State(messageId = messageId)

            // When - 添加多个segments（模拟可能超出1/3屏高的情况）
            val manyEvents = (1..10).flatMap { i ->
                listOf(
                    ThinkingEvent.SegmentStarted("seg$i", SegmentKind.PHASE, "Phase $i"),
                    ThinkingEvent.SegmentText("Content for phase $i with some length"),
                    ThinkingEvent.SegmentClosed("seg$i")
                )
            }

            var currentState = initialState
            manyEvents.forEach { event ->
                val result = thinkingBoxReducer.handleThinkingEvent(event, currentState)
                currentState = result.state
            }

            // Then - 验证shouldShowAIThinkingCard属性用于控制显示
            assertTrue(currentState.shouldShowAIThinkingCard) // 有内容时应该显示
            assertTrue(currentState.segmentsQueue.isNotEmpty())
            
            // 验证UI层可以通过shouldShowAIThinkingCard + segmentsQueue.size来控制1/3屏高限制
            assertTrue(currentState.segmentsQueue.size > 0)
        }

        @Test
        @DisplayName("【铁律4】文本内容8行溢出省略 - 通过content字段实现截断")
        fun `Iron Law 4 - Text content 8-line overflow ellipsis through content field truncation`() = runTest {
            // Given
            val messageId = "iron-law-4-test"
            val initialState = ThinkingBoxContract.State(messageId = messageId)

            // When - 创建超过8行的长文本
            val longContent = (1..12).joinToString("\n") { "这是第${it}行内容，用于测试8行溢出省略功能的正确实现" }
            assertTrue(longContent.split("\n").size > 8, "内容应该超过8行")

            val events = listOf(
                ThinkingEvent.SegmentStarted("long-text", SegmentKind.PHASE, "Long Text"),
                ThinkingEvent.SegmentText(longContent),
                ThinkingEvent.SegmentClosed("long-text")
            )

            var currentState = initialState
            events.forEach { event ->
                val result = thinkingBoxReducer.handleThinkingEvent(event, currentState)
                currentState = result.state
            }

            // Then - 验证content字段包含完整内容（截断在UI层处理）
            val segment = currentState.segmentsQueue.find { it.id == "long-text" }
            assertNotNull(segment)
            assertEquals(longContent, segment!!.content) // MVI层保存完整内容
            assertTrue(segment.content.split("\n").size > 8) // 确认确实超过8行
            
            // 验证UI层可以基于content字段实现8行截断
            assertTrue(segment.content.isNotEmpty())
        }

        @Test
        @DisplayName("四条铁律综合验证")
        fun `Four Iron Laws comprehensive verification`() = runTest {
            // Given - 综合场景
            val messageId = "comprehensive-iron-laws-test"
            val initialState = ThinkingBoxContract.State(messageId = messageId)

            // When - 模拟复杂的思考流程
            val comprehensiveEvents = listOf(
                // 多个段（测试铁律1和3）
                ThinkingEvent.SegmentStarted("perthink", SegmentKind.PERTHINK, "预思考"),
                ThinkingEvent.SegmentText("预思考内容"),
                ThinkingEvent.SegmentClosed("perthink"),
                
                // 30字符段（测试铁律2）
                ThinkingEvent.SegmentStarted("speed", SegmentKind.PHASE, "速度测试"),
                ThinkingEvent.SegmentText("这是恰好三十个字符的内容用于测试显示速度"),
                ThinkingEvent.SegmentClosed("speed"),
                
                // 长文本段（测试铁律4）
                ThinkingEvent.SegmentStarted("long", SegmentKind.PHASE, "长文本"),
                ThinkingEvent.SegmentText((1..10).joinToString("\n") { "第${it}行" }),
                ThinkingEvent.SegmentClosed("long"),
                
                ThinkingEvent.ThinkingClosed
            )

            var currentState = initialState
            comprehensiveEvents.forEach { event ->
                val result = thinkingBoxReducer.handleThinkingEvent(event, currentState)
                currentState = result.state
            }

            // Then - 综合验证四条铁律
            
            // 铁律1：增量更新，不重组
            assertTrue(currentState.segmentsQueue.size >= 3, "应该有多个段支持增量显示")
            
            // 铁律2：段完成状态
            val speedSegment = currentState.segmentsQueue.find { it.id == "speed" }
            assertNotNull(speedSegment)
            assertTrue(speedSegment!!.isComplete)
            assertTrue(speedSegment.content.length >= 30)
            
            // 铁律3：显示控制
            assertTrue(currentState.shouldShowAIThinkingCard)
            assertTrue(currentState.thinkingClosed)
            
            // 铁律4：完整内容保存
            val longSegment = currentState.segmentsQueue.find { it.id == "long" }
            assertNotNull(longSegment)
            assertTrue(longSegment!!.content.split("\n").size >= 8)
            
            println("✅ 四条铁律MVI实现验证通过:")
            println("   🔹 铁律1: segmentsQueue增量更新 ✓")
            println("   🔹 铁律2: isComplete状态控制显示速度 ✓") 
            println("   🔹 铁律3: shouldShowAIThinkingCard控制显示 ✓")
            println("   🔹 铁律4: content字段保存完整内容 ✓")
        }
    }
}