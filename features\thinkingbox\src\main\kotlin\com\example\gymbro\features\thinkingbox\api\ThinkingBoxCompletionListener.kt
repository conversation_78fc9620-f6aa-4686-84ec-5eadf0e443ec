package com.example.gymbro.features.thinkingbox.api

/**
 * ThinkingBoxCompletionListener - ThinkingBox完成回调接口
 *
 * 🎯 职责分离架构设计：
 * - 当ThinkingBox完成AI响应显示时，通过此接口回调Coach模块进行结果保存
 * - 遵循简化的职责分离原则：ThinkingBox完成显示后通知Coach保存结果
 * - 支持完整的错误处理和部分结果恢复机制
 *
 * 🔥 核心功能：
 * - 处理思考过程完成的成功回调
 * - 处理思考过程中的错误回调
 * - 传递完整的思考过程和最终答案内容
 * - 提供元数据支持（处理时间、token数量等）
 *
 * 架构原则：
 * - 异步回调：支持非阻塞的结果通知
 * - 错误恢复：提供部分结果恢复能力
 * - 元数据传递：支持丰富的上下文信息
 * - 类型安全：严格的参数类型定义
 *
 * @since Coach-ThinkingBox重构v2.0
 */
interface ThinkingBoxCompletionListener {

    /**
     * ThinkingBox显示完成回调
     *
     * 当AI思考过程完整显示完成后调用此方法。
     *
     * @param messageId 消息唯一标识符
     * @param thinkingProcess 思考过程的完整Markdown内容，包含所有思考步骤和推理过程
     * @param finalContent 最终的AI响应内容，用户最终看到的答案
     * @param metadata 额外的元数据信息
     *
     * metadata可能包含的字段：
     * - "processingTimeMs": Long - 处理总时长（毫秒）
     * - "tokenCount": Int - 处理的token总数
     * - "thinkingSteps": Int - 思考步骤数量
     * - "finalTokenCount": Int - 最终答案的token数量
     * - "networkLatency": Long - 网络延迟（毫秒）
     * - "renderingTimeMs": Long - UI渲染时长（毫秒）
     * - "streamingDuration": Long - 流式传输持续时间（毫秒）
     *
     * 实现注意事项：
     * - 此方法在后台线程调用，如需更新UI请切换到主线程
     * - 应该持久化保存thinkingProcess和finalContent
     * - 处理过程中的异常不应传播到ThinkingBox模块
     * - 建议实现幂等性，避免重复处理同一messageId
     */
    fun onDisplayComplete(
        messageId: String,
        thinkingProcess: String,
        finalContent: String,
        metadata: Map<String, Any> = emptyMap()
    )

    /**
     * ThinkingBox显示失败回调
     *
     * 当AI思考过程显示失败或中断时调用此方法。
     *
     * @param messageId 消息唯一标识符
     * @param error 错误异常信息，包含失败的具体原因
     * @param partialResult 部分结果内容（如果有），用于错误恢复
     *
     * partialResult结构：
     * - 可能包含部分完成的思考过程
     * - 可能包含部分的最终答案
     * - 格式与完整结果保持一致
     * - null表示没有任何可用的部分结果
     *
     * 常见错误类型：
     * - 网络连接失败：NetworkException
     * - 解析错误：ParseException
     * - 超时错误：TimeoutException
     * - AI服务异常：AiServiceException
     * - 内部系统错误：SystemException
     *
     * 实现注意事项：
     * - 此方法在后台线程调用，如需更新UI请切换到主线程
     * - 建议记录错误日志用于调试和监控
     * - 如果有partialResult，考虑保存以便用户查看
     * - 应该给用户提供友好的错误提示
     * - 处理过程中的异常不应传播到ThinkingBox模块
     */
    fun onDisplayError(
        messageId: String,
        error: Throwable,
        partialResult: String? = null
    )

    /**
     * ThinkingBox显示进度回调（可选实现）
     *
     * 用于报告思考过程的实时进度，允许Coach模块更新UI状态。
     *
     * @param messageId 消息唯一标识符
     * @param progress 进度信息，包含当前阶段和完成度
     *
     * 默认实现为空，子类可选择实现此方法以获得进度通知。
     */
    fun onDisplayProgress(
        messageId: String,
        progress: ThinkingProgress
    ) {
        // 默认空实现，子类可选择重写
    }
}

/**
 * ThinkingProgress - 思考进度信息
 *
 * 用于描述AI思考过程的当前进度状态。
 */
data class ThinkingProgress(
    /**
     * 当前阶段描述
     * 例如："解析用户需求"、"生成训练计划"、"优化建议"
     */
    val currentStage: String,

    /**
     * 总体完成度百分比 (0.0 - 1.0)
     */
    val completionRatio: Float,

    /**
     * 已完成的思考步骤数量
     */
    val completedSteps: Int,

    /**
     * 预估的总步骤数量（可能动态变化）
     */
    val estimatedTotalSteps: Int,

    /**
     * 当前处理的内容摘要
     */
    val currentContent: String = "",

    /**
     * 额外的进度元数据
     */
    val metadata: Map<String, Any> = emptyMap()
) {
    /**
     * 计算进度百分比文本
     */
    val progressPercentage: String
        get() = "${(completionRatio * 100).toInt()}%"

    /**
     * 检查是否已完成
     */
    val isCompleted: Boolean
        get() = completionRatio >= 1.0f
}
