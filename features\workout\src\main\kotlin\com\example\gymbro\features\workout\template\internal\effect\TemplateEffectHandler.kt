﻿package com.example.gymbro.features.workout.template.internal.effect

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.usecase.template.TemplateManagementUseCase
import com.example.gymbro.features.workout.template.TemplateContract
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * =========================================================================================
 * 🔥 GymBro Template EffectHandler - 遵循黄金标准 🔥
 * =========================================================================================
 *
 * 本EffectHandler遵循ProfileBio黄金标准，专注于核心副作用处理。
 *
 * 🎯 核心职责：
 * 1. 数据加载副作用 (LoadTemplatesData, LoadDraftsData等)
 * 2. 导航副作用 (NavigateToCreateTemplate, NavigateToEditTemplate)
 * 3. 数据操作副作用 (DeleteTemplate)
 * 4. UI反馈副作用 (ShowToast, ShowError)
 *
 * 🔄 优化数据流：
 * Effect -> EffectHandler -> UseCase -> Result -> Intent
 *
 * ✅ 已优化：
 * - Timber日志优化：仅在核心功能处保留1个关键日志点
 * - 错误处理优化：仅在核心错误处理位置保留1个错误处理逻辑
 */
class TemplateEffectHandler @Inject constructor(
    private val templateManagementUseCase: TemplateManagementUseCase,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val logger: Logger,
) {
    /**
     * 处理Effect的副作用 - 遵循黄金标准
     */
    fun handleEffect(
        effect: TemplateContract.Effect,
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
    ) {
        when (effect) {
            // === 数据加载Effect ===
            is TemplateContract.Effect.LoadTemplatesData -> {
                loadTemplates(effectScope, dispatch)
            }

            is TemplateContract.Effect.RefreshTemplatesData -> {
                loadTemplates(effectScope, dispatch)
            }

            is TemplateContract.Effect.LoadDraftsData -> {
                loadDrafts(effectScope, dispatch)
            }

            is TemplateContract.Effect.RefreshDraftsData -> {
                loadDrafts(effectScope, dispatch)
            }

            // === 数据操作Effect ===
            is TemplateContract.Effect.DeleteTemplate -> {
                deleteTemplate(effect.templateId, effectScope, dispatch)
            }

            // === 导航Effect ===
            is TemplateContract.Effect.NavigateToCreateTemplate -> {
                // 导航逻辑由UI层处理
            }

            is TemplateContract.Effect.NavigateToEditTemplate -> {
                // 导航逻辑由UI层处理
            }

            // === UI反馈Effect ===
            is TemplateContract.Effect.ShowToast -> {
                // Toast显示逻辑由UI层处理
            }

            is TemplateContract.Effect.ShowError -> {
                // 错误显示逻辑由UI层处理
            }
        }
    }

    // === 私有方法 ===

    private fun loadTemplates(
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
    ) {
        effectScope.launch(ioDispatcher) {
            templateManagementUseCase.GetTemplates()
                .invoke(Unit)
                .catch { throwable ->
                    logger.e("TemplateEffectHandler", "加载模板失败", throwable)
                    dispatch(TemplateContract.Intent.LoadError(UiText.DynamicString("加载模板失败")))
                }
                .onEach { result ->
                    when (result) {
                        is ModernResult.Success -> {
                            dispatch(TemplateContract.Intent.TemplatesLoaded(result.data))
                        }
                        is ModernResult.Error -> {
                            dispatch(TemplateContract.Intent.LoadError(UiText.DynamicString("加载模板失败")))
                        }
                        is ModernResult.Loading -> {
                            // Loading状态由Reducer处理
                        }
                    }
                }
                .launchIn(effectScope)
        }
    }

    private fun loadDrafts(
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
    ) {
        effectScope.launch(ioDispatcher) {
            // 草稿功能暂时返回空列表
            dispatch(TemplateContract.Intent.DraftsLoaded(emptyList()))
        }
    }

    private fun deleteTemplate(
        templateId: String,
        effectScope: CoroutineScope,
        dispatch: (TemplateContract.Intent) -> Unit,
    ) {
        effectScope.launch(ioDispatcher) {
            try {
                val result = templateManagementUseCase.DeleteTemplate().invoke(templateId)
                when (result) {
                    is ModernResult.Success -> {
                        dispatch(TemplateContract.Intent.TemplateDeleted(templateId))
                    }
                    is ModernResult.Error -> {
                        dispatch(TemplateContract.Intent.LoadError(UiText.DynamicString("删除模板失败")))
                    }
                    is ModernResult.Loading -> {
                        // Loading状态由Reducer处理
                    }
                }
            } catch (throwable: Throwable) {
                logger.e("TemplateEffectHandler", "删除模板失败", throwable)
                dispatch(TemplateContract.Intent.LoadError(UiText.DynamicString("删除模板失败")))
            }
        }
    }
}
