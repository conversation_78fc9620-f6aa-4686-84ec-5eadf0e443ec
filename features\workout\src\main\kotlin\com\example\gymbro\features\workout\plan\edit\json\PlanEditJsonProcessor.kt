package com.example.gymbro.features.workout.plan.edit.json

import com.example.gymbro.features.workout.plan.edit.PlanEditContract
import com.example.gymbro.shared.models.workout.PlanCalendarData
import com.example.gymbro.shared.models.workout.PlanCalendarInfo
import com.example.gymbro.shared.models.workout.CalendarEntryData
import kotlinx.serialization.SerializationException
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json
import timber.log.Timber

/**
 * Plan Edit JSON Processor - 训练计划编辑JSON处理器
 *
 * 🎯 核心功能：
 * - PlanEditContract.State JSON序列化/反序列化
 * - 与GymBro JSON规范完全兼容
 * - 支持计划数据的持久化和恢复
 * - 提供日历格式输出支持
 *
 * 🏗️ 架构特点：
 * - 遵循GymBro JSON处理标准
 * - 支持错误恢复和容错机制
 * - 与shared-models完全兼容
 * - 支持版本兼容性处理
 */
object PlanEditJsonProcessor {

    /**
     * JSON配置 - 遵循GymBro标准
     */
    private val json = Json {
        prettyPrint = true
        ignoreUnknownKeys = true
        encodeDefaults = true
        isLenient = true
    }

    /**
     * 将PlanEditContract.State序列化为JSON
     *
     * @param state 计划编辑状态
     * @return JSON字符串
     */
    fun stateToJson(state: PlanEditContract.State): String {
        return try {
            json.encodeToString(state)
        } catch (e: SerializationException) {
            Timber.e(e, "PlanEdit状态序列化失败")
            // 返回安全的默认JSON
            createDefaultStateJson()
        }
    }

    /**
     * 从JSON反序列化PlanEditContract.State
     *
     * @param jsonString JSON字符串
     * @return 计划编辑状态
     */
    fun stateFromJson(jsonString: String): PlanEditContract.State {
        return try {
            json.decodeFromString<PlanEditContract.State>(jsonString)
        } catch (e: SerializationException) {
            Timber.e(e, "PlanEdit状态反序列化失败，使用默认状态")
            // 返回默认状态
            PlanEditContract.State()
        }
    }

    /**
     * 将计划状态转换为日历格式JSON
     * 遵循shared-models/PlanCalendarPayload规范
     *
     * @param state 计划编辑状态
     * @return 日历格式JSON字符串
     */
    fun stateToCalendarJson(state: PlanEditContract.State): String {
        return try {
            val calendarData = convertStateToCalendarData(state)
            json.encodeToString(calendarData)
        } catch (e: Exception) {
            Timber.e(e, "计划状态转换为日历JSON失败")
            createDefaultCalendarJson()
        }
    }

    /**
     * 验证JSON格式是否有效
     *
     * @param jsonString JSON字符串
     * @return 是否有效
     */
    fun validateStateJson(jsonString: String): Boolean {
        return try {
            json.decodeFromString<PlanEditContract.State>(jsonString)
            true
        } catch (e: Exception) {
            Timber.w("PlanEdit JSON验证失败: ${e.message}")
            false
        }
    }

    /**
     * 安全的JSON转换，带错误恢复
     *
     * @param state 计划编辑状态
     * @return JSON字符串，保证不为null
     */
    fun safeStateToJson(state: PlanEditContract.State): String {
        return try {
            stateToJson(state)
        } catch (e: Exception) {
            Timber.e(e, "安全JSON转换失败，使用默认JSON")
            createDefaultStateJson()
        }
    }

    /**
     * 从损坏的JSON恢复状态
     *
     * @param corruptedJson 损坏的JSON
     * @param fallbackState 备用状态
     * @return 恢复的状态
     */
    fun recoverStateFromCorruptedJson(
        corruptedJson: String,
        fallbackState: PlanEditContract.State = PlanEditContract.State()
    ): PlanEditContract.State {
        return try {
            // 尝试部分解析
            val partialState = attemptPartialParsing(corruptedJson)
            partialState ?: fallbackState
        } catch (e: Exception) {
            Timber.e(e, "JSON恢复失败，使用备用状态")
            fallbackState
        }
    }

    // === 私有辅助方法 ===

    /**
     * 创建默认状态JSON
     */
    private fun createDefaultStateJson(): String {
        val defaultState = PlanEditContract.State()
        return json.encodeToString(defaultState)
    }

    /**
     * 创建默认日历JSON
     */
    private fun createDefaultCalendarJson(): String {
        val defaultCalendar = PlanCalendarData(
            planInfo = PlanCalendarInfo(
                planId = "",
                planName = "新建计划",
                description = null,
                totalDays = 0,
                workoutDays = 0,
                restDays = 0,
                createdAt = System.currentTimeMillis(),
                updatedAt = System.currentTimeMillis()
            ),
            calendarEntries = emptyList()
        )
        return json.encodeToString(defaultCalendar)
    }

    /**
     * 将状态转换为日历数据
     */
    private fun convertStateToCalendarData(state: PlanEditContract.State): PlanCalendarData {
        val allDays = state.weekPlans.values.flatten()
        val workoutDays = allDays.count { !it.isRestDay }
        val restDays = allDays.count { it.isRestDay }

        val planInfo = PlanCalendarInfo(
            planId = state.planId,
            planName = state.planName,
            description = null,
            totalDays = allDays.size,
            workoutDays = workoutDays,
            restDays = restDays,
            createdAt = state.lastSavedTime,
            updatedAt = System.currentTimeMillis()
        )

        val calendarEntries = allDays.mapIndexed { index, dayPlan ->
            CalendarEntryData(
                date = "", // 需要根据实际日期计算
                dayNumber = dayPlan.dayNumber,
                isRestDay = dayPlan.isRestDay,
                templateIds = dayPlan.templateVersionIds,
                workoutCount = dayPlan.templateVersionIds.size,
                notes = dayPlan.dayNotes?.toString(),
                estimatedDuration = null,
                isCompleted = dayPlan.isCompleted
            )
        }

        return PlanCalendarData(
            planInfo = planInfo,
            calendarEntries = calendarEntries
        )
    }

    /**
     * 尝试部分解析损坏的JSON
     */
    private fun attemptPartialParsing(corruptedJson: String): PlanEditContract.State? {
        return try {
            // 简单的字段提取逻辑
            val planId = extractJsonField(corruptedJson, "plan_id") ?: ""
            val planName = extractJsonField(corruptedJson, "plan_name") ?: "新建计划"
            
            PlanEditContract.State(
                planId = planId,
                planName = planName
            )
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 从JSON字符串中提取字段值
     */
    private fun extractJsonField(jsonString: String, fieldName: String): String? {
        return try {
            val pattern = "\"$fieldName\"\\s*:\\s*\"([^\"]*)\""
            val regex = Regex(pattern)
            regex.find(jsonString)?.groupValues?.get(1)
        } catch (e: Exception) {
            null
        }
    }
}
