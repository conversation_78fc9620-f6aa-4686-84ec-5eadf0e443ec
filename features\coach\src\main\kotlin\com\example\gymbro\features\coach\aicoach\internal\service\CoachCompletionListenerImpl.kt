package com.example.gymbro.features.coach.aicoach.internal.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.CoachMessage
import com.example.gymbro.domain.coach.repository.ChatRepository
import com.example.gymbro.features.thinkingbox.api.ThinkingBoxCompletionListener
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Coach完成监听器实现
 *
 * 🔥 【职责分离重构】核心功能：
 * - 接收ThinkingBox的完成回调
 * - 保存AI响应到Coach的对话历史
 * - 处理失败情况并保存错误消息
 * - 更新会话状态
 */
@Singleton
class CoachCompletionListenerImpl @Inject constructor(
    private val chatRepository: ChatRepository,
    private val ioDispatcher: CoroutineDispatcher
) : ThinkingBoxCompletionListener {

    private val scope = CoroutineScope(SupervisorJob() + ioDispatcher)

    override fun onDisplayComplete(
        messageId: String,
        thinkingProcess: String,
        finalContent: String,
        metadata: Map<String, Any>
    ) {
        scope.launch {
            try {
                Timber.tag("COACH-LISTENER").i("📥 接收ThinkingBox完成回调: messageId=$messageId")

                // 创建AI消息
                val aiMessage = CoachMessage.AiMessage(
                    id = messageId,
                    content = finalContent,
                    finalMarkdown = finalContent,
                    summary = generateSummary(finalContent),
                    rawTokens = thinkingProcess
                )

                // 保存AI响应
                val saveResult = chatRepository.saveMessage(aiMessage)
                
                when (saveResult) {
                    is ModernResult.Success -> {
                        Timber.tag("COACH-LISTENER").i("✅ AI响应保存成功: messageId=$messageId")
                    }
                    is ModernResult.Error -> {
                        Timber.tag("COACH-LISTENER").e("❌ AI响应保存失败: messageId=$messageId, error=${saveResult.error}")
                    }
                    is ModernResult.Loading -> {
                        Timber.tag("COACH-LISTENER").d("⏳ AI响应保存中: messageId=$messageId")
                    }
                }

            } catch (e: Exception) {
                Timber.tag("COACH-LISTENER").e(e, "❌ 处理完成回调异常: messageId=$messageId")
            }
        }
    }

    override fun onDisplayError(
        messageId: String,
        error: Throwable,
        partialResult: String?
    ) {
        scope.launch {
            try {
                Timber.tag("COACH-LISTENER").w("⚠️ 接收ThinkingBox错误回调: messageId=$messageId, error=${error.message}")

                // 创建错误消息
                val errorContent = partialResult ?: "AI处理失败: ${error.message}"
                val errorMessage = CoachMessage.AiMessage(
                    id = messageId,
                    content = errorContent,
                    finalMarkdown = errorContent,
                    summary = "处理失败",
                    rawTokens = "错误: ${error.message}"
                )

                // 保存错误消息
                val saveResult = chatRepository.saveMessage(errorMessage)
                
                when (saveResult) {
                    is ModernResult.Success -> {
                        Timber.tag("COACH-LISTENER").i("✅ 错误消息保存成功: messageId=$messageId")
                    }
                    is ModernResult.Error -> {
                        Timber.tag("COACH-LISTENER").e("❌ 错误消息保存失败: messageId=$messageId, error=${saveResult.error}")
                    }
                    is ModernResult.Loading -> {
                        Timber.tag("COACH-LISTENER").d("⏳ 错误消息保存中: messageId=$messageId")
                    }
                }

            } catch (e: Exception) {
                Timber.tag("COACH-LISTENER").e(e, "❌ 处理错误回调异常: messageId=$messageId")
            }
        }
    }

    /**
     * 生成消息摘要
     */
    private fun generateSummary(content: String): String {
        return when {
            content.length <= 50 -> content
            content.contains("训练计划") -> "训练计划建议"
            content.contains("动作") -> "动作指导"
            content.contains("营养") -> "营养建议"
            content.contains("恢复") -> "恢复建议"
            else -> content.take(50) + "..."
        }
    }

    /**
     * 🔥 【扩展接口】为了兼容新的架构，添加额外的回调方法
     */
    suspend fun onThinkingCompleted(
        sessionId: String,
        messageId: String,
        finalContent: String,
        thinkingProcess: String? = null
    ): ModernResult<Unit, Exception> {
        return try {
            onDisplayComplete(
                messageId = messageId,
                thinkingProcess = thinkingProcess ?: "",
                finalContent = finalContent,
                metadata = mapOf("sessionId" to sessionId)
            )
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            ModernResult.Error(e)
        }
    }

    /**
     * 🔥 【扩展接口】处理失败回调
     */
    suspend fun onThinkingFailed(
        sessionId: String,
        messageId: String,
        error: com.example.gymbro.features.thinkingbox.api.ThinkingBoxError
    ): ModernResult<Unit, Exception> {
        return try {
            onDisplayError(
                messageId = messageId,
                error = RuntimeException(error.message),
                partialResult = null
            )
            ModernResult.Success(Unit)
        } catch (e: Exception) {
            ModernResult.Error(e)
        }
    }
}
