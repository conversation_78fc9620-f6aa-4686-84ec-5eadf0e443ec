# =============================================================================
# ## Gradle 终极性能优化配置 v4.0 (Lockdown Edition)
# =============================================================================
# 本配置旨在通过强制统一设置，彻底解决 IDE 与项目配置冲突导致的问题。
# =============================================================================


# =============================================================================
# ## 1. JVM 内存与GC优化 (JVM Memory & GC Optimization)
# =============================================================================

# --- Gradle Daemon JVM 参数 ---
# -Xmx16g: 为您的 48GB 内存，分配 16GB 给 Gradle 主进程，确保其有足够空间处理大型项目。
# -XX:+UseParallelGC: 强制使用并行垃圾回收器，优化编译吞吐量，更积极地释放内存。
org.gradle.jvmargs=-Xmx16g -XX:+UseParallelGC -Dfile.encoding=UTF-8

# --- Kotlin Daemon JVM 参数 ---
# -Xmx6g: 🔥【关键提升】为 Kotlin 编译器分配 6GB 内存。
#          对于大型项目，充足的内存是 Kotlin 编译速度的关键。
kotlin.daemon.jvm.options=-Xmx6g


# =============================================================================
# ## 2. 强制 Kotlin 编译器在 Gradle 进程内运行
# =============================================================================
# 🔥【终极锁定】这是解决多个 Java 进程问题的关键之一。
# 此设置会强制 Kotlin 编译器在 Gradle Daemon 的主进程内运行，
# 而不是启动一个独立的 Kotlin Daemon 进程。这能有效减少进程数量和内存碎片，
# 让 org.gradle.jvmargs 和 kotlin.daemon.jvm.options 共同作用于同一个进程。
kotlin.compiler.execution.strategy=in-process


# =============================================================================
# ## 3. 并行与缓存配置 (Parallel & Caching)
# =============================================================================
# 保持所有最佳配置启用
org.gradle.daemon=true
org.gradle.caching=true
org.gradle.configuration-cache=true
org.gradle.parallel=true

# 匹配您的 16 核 CPU，最大化并行任务执行。
org.gradle.workers.max=16


# =============================================================================
# ## 4. Android Gradle Plugin (AGP) 与 KSP 优化
# =============================================================================
android.nonFinalResIds=true
android.nonTransitiveRClass=true
android.useAndroidX=true
ksp.incremental=true
ksp.incremental.intermodule=true
