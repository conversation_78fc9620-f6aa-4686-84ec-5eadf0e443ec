# PlanEdit JSON Serialization Guide

## 📋 概述

PlanEditScreen 现已完全支持 JSON 序列化/反序列化，确保与 GymBro JSON 规范完全兼容。本文档详细说明如何使用这些功能进行数据持久化、备份和系统集成。

## 🎯 核心功能

### ✅ 已实现功能
- **完整状态序列化**: PlanEditContract.State 完全支持 JSON 转换
- **GymBro 规范兼容**: 遵循项目 JSON 命名和结构标准
- **错误恢复机制**: 损坏 JSON 的安全恢复
- **日历格式导出**: 与 shared-models 兼容的日历数据
- **统计信息提取**: 计划数据的分析和报告
- **类型安全**: 完整的 Kotlin 类型安全保证

### 🏗️ 架构特点
- **扩展函数**: 便捷的 API 调用
- **错误安全**: 所有操作都有错误处理
- **性能优化**: 高效的序列化/反序列化
- **版本兼容**: 支持未来版本扩展

## 📊 JSON 格式规范

### State JSON 结构
```json
{
  "plan_id": "plan_123",
  "plan_name": "我的训练计划",
  "is_loading": false,
  "is_saving": false,
  "show_calendar_view": true,
  "show_template_selector": false,
  "selected_week": 1,
  "selected_day": 1,
  "week_plans": {
    "1": [
      {
        "day_number": 1,
        "template_version_ids": ["template_1", "template_2"],
        "is_rest_day": false,
        "day_notes": {
          "type": "DynamicString",
          "value": "胸部训练日"
        },
        "is_completed": false,
        "progress": "IN_PROGRESS"
      }
    ]
  },
  "has_unsaved_changes": true,
  "last_saved_time": 1640995200000
}
```

### 字段说明
- **plan_id**: 计划唯一标识符
- **plan_name**: 计划名称
- **week_plans**: 周计划数据，Map<Int, List<DayPlan>>
- **template_version_ids**: 使用的模板版本ID列表
- **progress**: 进度状态 (NOT_STARTED, IN_PROGRESS, COMPLETED)

## 🚀 使用方法

### 1. 基础序列化操作

```kotlin
// 导入扩展函数
import com.example.gymbro.features.workout.plan.edit.json.*

// 序列化状态为 JSON
val state = PlanEditContract.State(planId = "test", planName = "测试计划")
val jsonString = state.toJson()

// 从 JSON 反序列化状态
val recoveredState = jsonString.toPlanEditState()

// 安全序列化（保证不抛异常）
val safeJson = state.toSafeJson()
```

### 2. ViewModel 集成

```kotlin
class PlanEditViewModel {
    // 导出当前状态
    fun exportPlan(): String = exportStateAsJson()
    
    // 导入计划数据
    fun importPlan(jsonString: String) = importStateFromJson(jsonString)
    
    // 获取统计信息
    fun getPlanStats(): Map<String, Any> = getStateStatistics()
    
    // 检查是否可导出
    fun canExport(): Boolean = canExportState()
}
```

### 3. 日历格式导出

```kotlin
// 导出为日历格式（与 shared-models 兼容）
val calendarJson = state.toCalendarJson()

// 日历 JSON 包含：
// - planInfo: 计划基本信息
// - calendarEntries: 日历条目列表
```

### 4. 数据分析和统计

```kotlin
// 获取详细统计信息
val stats = state.getStatistics()
println("总周数: ${stats["total_weeks"]}")
println("训练天数: ${stats["workout_days"]}")
println("完成率: ${stats["completion_rate"]}")

// 检查计划状态
val isEmpty = state.isEmpty()
val hasWorkouts = state.hasValidWorkouts()
val progress = state.getCompletionProgress()

// 模板使用分析
val allTemplates = state.getAllTemplateIds()
val usesTemplate = state.usesTemplate("template_1")
val usageCount = state.getTemplateUsageCount("template_1")
```

### 5. 错误处理和恢复

```kotlin
// 验证 JSON 有效性
val isValid = jsonString.isValidPlanEditStateJson()

// 从损坏的 JSON 恢复
val fallbackState = PlanEditContract.State()
val recoveredState = corruptedJson.recoverToPlanEditState(fallbackState)

// 使用 PlanEditJsonProcessor 进行高级操作
val processor = PlanEditJsonProcessor
val validatedJson = processor.validateStateJson(jsonString)
val safeState = processor.recoverStateFromCorruptedJson(corruptedJson)
```

## 🔧 高级功能

### 深拷贝和比较

```kotlin
// 创建状态的深拷贝
val copiedState = originalState.deepCopy()

// 比较两个状态的差异
val differences = state1.diffWith(state2)
println("计划名称是否改变: ${differences["plan_name_changed"]}")
```

### 周级别分析

```kotlin
// 获取指定周的信息
val workoutDays = state.getWorkoutDaysInWeek(1)
val restDays = state.getRestDaysInWeek(1)
val hasWorkouts = state.hasWorkoutsInWeek(1)
```

### 摘要信息

```kotlin
// 获取 JSON 摘要（用于日志）
val summary = state.toSummaryJson()

// 在 ViewModel 中获取可读摘要
val readableSummary = viewModel.getStateSummary()
// 输出: "计划: 我的训练计划 | 周数: 2 | 训练天: 5 | 完成率: 60.0%"
```

## 🧪 测试支持

### 单元测试示例

```kotlin
@Test
fun `test plan serialization`() {
    // Given
    val state = PlanEditContract.State(
        planId = "test_plan",
        planName = "测试计划"
    )
    
    // When
    val json = state.toJson()
    val recovered = json.toPlanEditState()
    
    // Then
    assertEquals(state.planId, recovered.planId)
    assertEquals(state.planName, recovered.planName)
}
```

### 集成测试

```kotlin
@Test
fun `test end to end JSON workflow`() {
    // 1. 创建状态
    val originalState = createTestState()
    
    // 2. 序列化
    val json = originalState.toJson()
    
    // 3. 反序列化
    val recoveredState = json.toPlanEditState()
    
    // 4. 验证数据完整性
    assertStatesEqual(originalState, recoveredState)
}
```

## 📈 性能考虑

### 最佳实践
- **批量操作**: 避免频繁的序列化/反序列化
- **异步处理**: 大型计划数据使用协程处理
- **缓存策略**: 缓存序列化结果避免重复计算
- **内存管理**: 及时释放大型 JSON 字符串

### 性能监控
```kotlin
// 监控序列化性能
val startTime = System.currentTimeMillis()
val json = state.toJson()
val duration = System.currentTimeMillis() - startTime
Timber.d("序列化耗时: ${duration}ms")
```

## 🔒 安全考虑

### 数据验证
- **输入验证**: 所有 JSON 输入都经过验证
- **类型安全**: 强类型检查防止数据损坏
- **错误边界**: 完善的错误处理机制
- **默认值**: 安全的默认值策略

### 隐私保护
- **敏感数据**: UiText 等 UI 相关数据不序列化
- **用户数据**: 遵循数据保护最佳实践
- **日志安全**: 避免在日志中暴露敏感信息

## 🔄 版本兼容性

### 向前兼容
- **字段添加**: 新字段使用默认值
- **字段重命名**: 保持旧字段名兼容
- **结构变更**: 渐进式迁移策略

### 迁移策略
```kotlin
// 版本检测和迁移
fun migrateFromOldVersion(oldJson: String): PlanEditContract.State {
    return try {
        // 尝试新格式
        oldJson.toPlanEditState()
    } catch (e: Exception) {
        // 回退到旧格式处理
        migrateFromLegacyFormat(oldJson)
    }
}
```

## 📚 相关文档

- [GymBro JSON 规范](../../../json/README.md)
- [Shared Models 文档](../../../../../shared-models/README.md)
- [MVI 架构指南](../../../../../core-arch/README.md)
- [错误处理规范](../../../../../core/README.md)

## 🐛 故障排除

### 常见问题

1. **序列化失败**: 检查数据类型和注解
2. **反序列化错误**: 验证 JSON 格式和字段名
3. **性能问题**: 使用异步处理和缓存
4. **内存泄漏**: 及时释放大型对象引用

### 调试技巧

```kotlin
// 启用详细日志
Timber.plant(Timber.DebugTree())

// 验证 JSON 结构
val isValid = jsonString.isValidPlanEditStateJson()
if (!isValid) {
    Timber.e("JSON 格式无效: $jsonString")
}

// 使用摘要进行快速调试
Timber.d("状态摘要: ${state.toSummaryJson()}")
```
