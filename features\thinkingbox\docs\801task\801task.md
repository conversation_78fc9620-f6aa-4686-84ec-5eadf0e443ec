ThinkingBox × Coach 重构实施方案
1 调研总结
1.1 项目概况
模块定位：ThinkingBox 是 GymBro 应用中负责可视化 AI 思考过程的核心模块，采用 MVI 2.0 + Clean Architecture 架构。代码已达到 v6.0，完全符合设计系统规范，无硬编码值，单元测试覆盖率高。
代码结构：项目分为 domain、internal 和 presentation 等层级，包含 StreamingThinkingMLParser、DomainMapper、SegmentQueueReducer、ThinkingBoxViewModel 等核心组件，以及历史写入、日志、指标收集等辅助模块
。与 Coach 模块之间存在复杂的耦合。
PRD 要求：实时渲染 AI 思考过程、阶段分段展示、打字机效果、自动滚动、段落结构化展示、暗亮主题切换、完善的错误处理与性能指标
。
大纲 v5：明确了标签协议（<think>、<thinking>、<phase id=...>、</thinking>、<final>）、Segment 队列模型、双时序（token 流与 UI 渲染队列）以及 History 写入策略
。Coach 作为纯消费者，不再操控 ThinkingBox 内部状态
。
当前问题：现有 ThinkingBox/Coach 接口仍然混合业务逻辑与显示逻辑，Token 流正确但思考框未按大纲实现；缺乏统一的完成回调和统一的消息模型；代码重复、职责不清，无法满足新版规范和性能要求。
1.2 现状梳理
1.token 流管道已搭建：Core-Network 通过 DirectOutputChannel 向 ThinkingBox 推送 token 流，StreamingParser 能解析流式 XML 标签；但 <final> 标签未单独缓冲，无法区分思考过程与最终富文本。
2.Coach 模块负责用户消息保存、组装 AI 请求和显示思考框，但部分逻辑（例如 Prompt 组装、会话管理）散落在多个 Handler 中。当前 AiCoachEffectHandler、AiCoachViewModel 与 ThinkingBox 的耦合度较高，缺乏统一的回调接口。
3.ThinkingBox 模块采用 SegmentQueueReducer 管理 state，但仍使用旧版布尔握手机制；ThinkingBoxViewModel 没有处理 <final> 缓存及完成回调；UI 组件未完全按照 new Segment 队列模型实现；History 写入逻辑分散在多个地方。
4.文档差异：大纲 v5 和 PRD 1.0/2.0 中定义的行为（Segment 队列、History 写入时机、Coach 与 ThinkingBox 边界）在当前代码中未完全实现，需要补齐。
5.新架构验证测试：architecture-validation-test.md 提供了验证步骤和测试场景，用于确认重构后流程是否满足性能和职责分离要求。当前版本无法通过所有检查点，需按计划修复。
2 总体改动清单
为满足 PRD 与大纲 v5 要求并通过验证测试，需要对代码库进行以下核心改动：
1.分离职责和接口设计
2.新增 ThinkingBoxDisplay 与 ThinkingBoxCompletionListener 接口，定义 start/stop 和完成/错误回调，完全移除 Coach 对 ThinkingBox 内部状态的直接操作。
3.新增 ThinkingBoxLauncher（或与 Display 合并）作为统一入口，由 Coach 调用以启动或停止显示。
4.定义 ChatMessage 与 Conversation 等统一数据模型，替代重复的 ChatRaw/ChatRequest 等数据类。
5.在 domain 层新增 ConversationUseCase 与 AiRequestUseCase（或 Repository 接口），封装会话管理和 AI 请求构建逻辑。
6.思考内容解析与状态管理更新
7.XML 解析器：更新 StreamingThinkingMLParser 使其只识别大纲规定的五类标签（文本/<thinking>/<phase>/</thinking>/<final>），过滤非法标签（通过 StringXmlEscaper），并确保 <final> 后 token 不进入 UI 队列而是累积到 finalBuffer。
8.DomainMapper：根据大纲定义的 ThinkingEvent 类型更新映射逻辑，生成 SegmentStarted、SegmentText、SegmentClosed、ThinkingClosed、FinalStart、FinalContent、FinalClosed 和 UiSegmentRendered 事件。移除旧的双握手布尔标记，改用 ThinkingClosed/FinalClosed。
9.SegmentQueueReducer：实现队列模型：
a.SegmentStarted 创建/替换当前 Segment；SegmentClosed 将其加入 queue，等待 UI 消费。
b.ThinkingClosed 设置 thinkingClosed=true 并推入空的 “final-phase” Segment；
c.FinalStart/FinalContent 将内容累积到 finalBuffer；FinalClosed 将 finalClosed=true 并生成 NotifyHistoryFinal Effect。
d.处理 UiSegmentRendered 事件以从队列头移除已渲染的 Segment。
10.ThinkingBoxViewModel：更新 State 定义，加上 queue、finalBuffer、thinkingClosed 与 finalClosed，订阅 DomainEffect 并在思考框关闭后渲染 finalBuffer；触发 CloseThinkingBox Effect；在完成或错误时调用 CompletionListener。
11.UI 渲染策略调整
12.修改 UI 组件（AIThinkingCard、ThinkingStageCard、StreamingFinalRenderer 等）以按队列模式渲染：只显示 queue.first()，在动画完成后发送 UiSegmentRendered(id) 给 ViewModel，确保数据流领先 UI 流；动画持续 ≤ 300 ms，支撑打字机效果；自动滚动与阶段切换均在 Effect 层处理。
13.思考框关闭逻辑：当 thinkingClosed && queue.isEmpty() 或收到 CloseThinkingBox Effect 时，动画收起思考框，显示最终 Markdown；支持用户手动展开/收起。
14.新增暗色/亮色主题切换开关，保持设计系统 token 使用；改善无障碍支持（TalkBack 标签、可缩放文本）。
15.History 写入与完成回调
16.重构 History 保存：在 Reducer 中触发 NotifyHistoryThinking Effect（ThinkingClosed 事件）和 NotifyHistoryFinal Effect（FinalClosed 事件）。
17.实现 HistoryActor 或合并到 HistorySaver，根据 Effect 保存 thinkingMarkdown 和 finalMarkdown 到 HistoryRepository。确保异步写入并添加去抖动机制，防止重复写入。
18.在 ThinkingBox ViewModel 中监听完成事件并调用 ThinkingBoxCompletionListener.onDisplayComplete，传入 messageId、完整思考过程 Markdown、最终富文本 Markdown 及元数据（时间、token 数量等）。错误时调用 onDisplayError。
19.Coach 模块重构
20.更新 MVI 架构：在 AiCoachContract 添加 Intent/Effect，如 SaveUserMessage、SendAiRequest、AiResponseCompleted、LaunchThinkingBoxDisplay 等；在 Reducer 中处理逻辑；在 EffectHandler 中注入 LayeredPromptBuilder、AiRequestRepository、ThinkingBoxDisplay 并实现 buildAndSendPrompt 与 startDisplaying，按顺序保存用户消息、构建 Prompt、发送网络请求，然后启动 ThinkingBox。
21.完成回调：实现 ThinkingBoxCompletionListener（例如 CoachCompletionListenerImpl），在 onDisplayComplete 中将 AI 思考过程与最终内容保存到 ConversationRepository；触发 Intent AiResponseCompleted 以更新会话。
22.简化 Token 流处理：Coach 不再直接处理 Token，仅监听完成回调和错误回调；简化 StreamEffectHandler，移除无关逻辑。
23.依赖注入与配置更新
24.在 ThinkingBoxModule 中绑定 ThinkingBoxDisplay 与 ThinkingBoxLauncherImpl；提供 ThinkingBoxCompletionListener 的 DI 绑定（由 Coach Module 提供）。
25.在 Domain 与 Data 层新增 Repository 接口和实现，如 AiRequestRepository；更新 AiCoachRepository 和 ConversationRepository；将 LayeredPromptBuilder 注入 Coach 模块。
26.更新配置文件与 DI 模块，确保无循环依赖且能通过编译；删除未使用的旧接口和测试文件。
27.测试和质量保证
28.更新单元测试与集成测试，覆盖新的解析逻辑、Reducer 行为、ViewModel 完成回调以及 Coach 与 ThinkingBox 的交互。
29.编写端到端测试以模拟正常、失败和用户取消等流程，确保能通过 architecture-validation-test.md 中定义的检查点：编译无错、端到端流程顺畅、完成回调正常触发、错误处理有效、性能指标达标。
30.运行性能测试，确保首段延迟 ≤ 2 秒、段落切换 ≤ 300 毫秒、打字机动画 ≥ 30 FPS、内存 ≤ 50 MB，Crash 率 ≤ 0.1%，P95 响应时间 ≤ 3 秒等 PRD 指标。
3 实施步骤与时间规划
阶段 1：接口与数据模型设计 (约 0.5–1 天)
1.新增 shared-models 层文件：定义统一的 ChatMessage 和 Conversation 数据类（包括 id、conversationId、role、content、thinkingProcess、timestamp、metadata 等）。
2.新增 API 接口文件：在 features/thinkingbox/api/ 下创建 ThinkingBoxDisplay.kt、ThinkingBoxCompletionListener.kt；在 features/thinkingbox/api/ 或 internal/presentation 下创建 ThinkingBoxLauncher.kt 定义 start/stop 方法。
3.更新 Domain 层：新增 ConversationUseCase、AiRequestUseCase（或 Repository）接口；定义 AiRequestRepository 并在 Data 层提供实现。
4.更新 DI 模块：在 CoreNetworkModule、DomainModule、DataModule、CoachModule 和 ThinkingBoxModule 中绑定新的接口和实现。
阶段 2：思考过程解析与 Reducer 改造 (约 1–2 天)
1.StreamingThinkingMLParser 改造：重写标签解析逻辑，使其遵循 v5 大纲的合法标签集合，支持流式 token 处理和非法标签清理；为 <final> 添加单独的 FinalContent 输出事件。
2.DomainMapper 更新：调整语义事件到思考事件的映射，提供 SegmentStarted、SegmentText、SegmentClosed、ThinkingClosed、FinalStart、FinalContent、FinalClosed 和 UiSegmentRendered 事件定义。
3.SegmentQueueReducer 更新：实现新的状态管理逻辑，包括 Segment 队列、finalBuffer、thinkingClosed、finalClosed 和 streaming 标记；添加 Effect 触发 NotifyHistoryThinking 与 NotifyHistoryFinal。
4.ThinkingBoxViewModel 更新：修改 State 为 messageId、queue、finalBuffer、thinkingClosed、finalClosed 等字段；处理新的事件并转发 Effect；在思考完成后调用 completionListener；支持取消与错误状态。
5.历史写入模块：将 HistoryActor 改造为通过 Effect 接收 NotifyHistoryThinking 和 NotifyHistoryFinal 保存 Markdown，并提供去抖动逻辑防止重复保存。
阶段 3：UI 与交互优化 (约 1 天)
1.UI 组件调整：更新 AIThinkingCard、ThinkingStageCard、StreamingFinalRenderer 等以仅渲染队列头的 Segment，并在动画结束时发送 UiSegmentRendered 事件；增加打字机效果、自动滚动及展开/收起交互，确保响应时间 ≤ 100 毫秒。
2.主题与无障碍：确保所有组件使用设计系统 token，支持暗亮主题切换，并加入 TalkBack 标签和可缩放文本。对于 Markdown 渲染可采用现有的 Markdown 组件，确保内容层次清晰。
3.思考框关闭逻辑：在 UI 层监听 CloseThinkingBox Effect 并执行关闭动画；当关闭后渲染 finalBuffer 为 Markdown；提供按钮/手势切换思考过程和最终答案。
4.错误处理 UI：设计并实现统一的错误提示组件，显示网络错误、解析错误或用户取消信息，提供重试与取消操作。
阶段 4：Coach 模块重构 (约 1–2 天)
1.MVI 组件修改：在 AiCoachContract 定义新的 State、Intent 和 Effect；更新 ViewModel 和 Reducer，以响应保存用户消息、构建 Prompt、发送 AI 请求、启动显示、完成回调等流程。
2.LayeredPromptBuilder 集成：在 AiCoachEffectHandler 中注入 LayeredPromptBuilder；实现 buildAndSendPrompt 方法：读取用户信息和会话历史，通过 Builder 生成完整 ChatMessage 列表，调用 AiRequestRepository.sendChatMessages，然后调用 ThinkingBoxDisplay.startDisplaying(messageId, completionListener)。
3.完成回调实现：实现 ThinkingBoxCompletionListener（如 CoachCompletionListenerImpl），在 onDisplayComplete 中将 AI 思考过程与最终内容保存到 ConversationRepository；触发 Intent 更新 UI；在 onDisplayError 中保存错误信息并反馈给用户。
4.简化旧逻辑：清理 Coach 中重复的 Chat 类和 token 流处理逻辑；保留用户消息保存与结果保存；更新 DI 绑定和测试。
阶段 5：测试与验证 (约 1 天)
1.单元测试更新：编写或修改测试用例验证解析器输出、Reducer 行为、ViewModel 状态转换、History 写入、完成回调等；确保测试覆盖率 ≥ 90%。
2.集成测试：按照 architecture-validation-test.md 的场景编写端到端测试：正常 AI 响应、网络错误、用户取消；验证编译无错误、完成回调触发、错误处理有效、性能指标达标。
3.性能测试：利用性能监控工具收集 Token 处理速度、UI 渲染响应时间、内存使用等指标，优化实现以满足 PRD 要求（首段渲染 ≤ 2 秒、段落切换 ≤ 300 毫秒、打字机动画 ≥ 30 FPS、内存 ≤ 50 MB）。
4.质量检查：运行 Lint/Detekt 检查确保零警告；生成覆盖率报告；执行设计系统合规验证，保证 100% 使用 token，无硬编码值。
4 风险与缓解措施
1.接口设计不合理导致可扩展性差：严格遵循大纲和 PRD，要保持接口最小化且通用，使用数据类封装参数，避免过度暴露内部逻辑。
2.重构范围过大影响稳定性：分阶段实施，每一步均可独立编译运行，通过单元测试和集成测试确认后再进入下一阶段；确保向后兼容和渐进式迁移。
3.业务逻辑复杂导致 Coach 模块膨胀：将复杂逻辑拆分到 UseCase 层和 Repository 层，保持 ViewModel 和 EffectHandler 的简洁；遵循单一职责原则。
4.性能指标不达标：在实现中优化解析器和渲染器的算法，使用协程和 Channel 处理异步任务，避免主线程阻塞；增加监控和日志收集，及时发现瓶颈。
5.用户体验退化：保持现有 UI 风格，新增的打字机效果、分页、主题切换需经过 UX 评审；在错误场景下提供友好的提示和恢复选项。




* **数据队列模型**：以 perthink → phase → final-phase → final 富文本的顺序管理数据流，确保任何时刻只用 `segmentsQueue` 的队头驱动 UI。
* **UI 单段展示**：UI 组件在展示完一个段落并完成其动画后，必须主动通过 `onSegmentRendered` 回调通知 Reducer 出队，这是连接数据流和 UI 时序的唯一桥梁。
* **状态驱动显示**：通过在 `ThinkingBoxContract.State` 中添加 `shouldShowThinkingCard` 和 `shouldShowFinalContent` 这样的计算属性，让思考卡片和最终内容的出现与消失完全由状态决定，而不再依赖显式的布尔开关。
* **Reducer 简化**：`</thinking>` 等数据断点只更新内部状态，不直接触发 UI 关闭；UI 的退出和最终内容的呈现由队列为空和 `finalReady` 共同决定。

这些“黄金法则”与我先前给出的修改方案是一致且互补的。我的方案已经涵盖了 Segment 队列模型、UI 和 Reducer 的双握手机制、History 写入逻辑和 Coach / ThinkingBox 职责分离。此次校对后，需要在以下方面进一步细化以完全吻合您的最终指示：

1. **在 State 中增加 `shouldShowThinkingCard`、`shouldShowFinalContent` 等计算属性**，并在 UI 层使用 `AnimatedVisibility` 等方式根据这些属性控制组件显隐。
2. **确保 Reducer 处理 `ThinkingClosed` 事件时仅更新状态，不直接关闭 UI**，而是等待 UI 完成所有队列片段后自动消失。
3. **完善 `ThinkingBoxDisplayImpl` 的完成回调条件**，基于“队列为空且最终内容准备完毕”来触发 `onDisplayComplete`。
4. **在端到端测试中验证这一时序**：即使 `</thinking>` 已到达，UI 仍会依次渲染完所有已闭合的段落，然后才退出并展示最终富文本。

综合来看，您的“黄金法则”进一步明确了实现细节和时序控制，补充了我前述规划中的关键点。后续实施中我会将这些要求纳入最终重构蓝图，保证数据流与 UI 流完全解耦，并按照最新标准完成重构。
