package com.example.gymbro.core.arch.mvi

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import timber.log.Timber
import kotlin.reflect.full.memberProperties

/**
 * =========================================================================================
 * 🔥 MVI 架构的基础 ViewModel - v7.1-STABLE 🔥
 * =========================================================================================
 *
 * 核心特性 (v7.1):
 * 1.  **✅ 智能加载状态管理 (Smart Loading)**: **完整保留**并优化了 v7.0 的核心功能。
 * - **机制**: 通过反射自动检测并追踪 State 中所有命名包含 "loading" 的布尔字段。
 * - **注意**: 为确保此功能正常工作，请在 Release 版本中配置 Proguard/R8 规则，以防止混淆 State 数据类中的加载状态字段。
 * ```proguard
 * # Proguard/R8 rule for MVI State classes
 * -keepclassmembers public class * extends com.example.gymbro.core.arch.mvi.UiState {
 * public <fields>;
 * }
 * ```
 *
 * 2.  **🪵 日志系统优化**: 精简了日志输出，使其在开发环境中更具可读性，同时保留了关键的调试信息。
 *
 * 3.  **🛡️ 保持健壮**: 继承了 v7.0 的所有优点，包括：
 * - 完整的 Effect 分发机制。
 * - 用于副作用处理的独立 `handlerScope`。
 * - 经过优化的 `SharedFlow` 缓冲区，防止 Effect 丢失。
 *
 * MVI 数据流: UI → Intent → Reducer → State + Effect → (UI or ViewModel for side-effects)
 *
 * @param I Intent 类型
 * @param S State 类型
 * @param E Effect 类型
 */
abstract class BaseMviViewModel<I : AppIntent, S : UiState, E : UiEffect>(
    initialState: S,
) : ViewModel() {

    // State 管理 - UI 状态的单一数据源
    protected val _state = MutableStateFlow(initialState)
    val state: StateFlow<S> = _state.asStateFlow()

    // Effect 管理 - 用于处理一次性事件（如Toast、导航）
    protected val _effect =
        MutableSharedFlow<E>(
            replay = 0,
            extraBufferCapacity = 32, // 提供适中缓冲区，应对UI事件突发
            onBufferOverflow = BufferOverflow.SUSPEND, // 使用背压策略，确保Effect不丢失
        )
    val effect: SharedFlow<E> = _effect.asSharedFlow()

    // 为副作用（Side Effects）提供一个独立的、可管理的协程作用域
    protected val handlerScope: CoroutineScope =
        viewModelScope + SupervisorJob() + CoroutineName("MviEffectScope-${this::class.simpleName}")

    // 快速访问当前状态
    protected val currentState: S
        get() = _state.value

    // Reducer 实例 - 子类必须提供
    protected abstract val reducer: Reducer<I, S, E>

    /**
     * 分发 Intent - MVI 架构的统一入口。
     */
    open fun dispatch(intent: I) {
        val intentName = intent::class.simpleName ?: "UnknownIntent"
        Timber.d("🎯 [${this::class.simpleName}] Received Intent: $intentName")

        try {
            val result = reducer.reduce(intent, currentState)

            if (result.newState != currentState) {
                _state.value = result.newState
                Timber.v("🔄 [${this::class.simpleName}] State updated by: $intentName")
                // 智能加载状态分析
                performLoadingStateAnalysis(result.newState, intentName)
            }

            if (result.effects.isNotEmpty()) {
                viewModelScope.launch {
                    result.effects.forEach { effect ->
                        _effect.emit(effect)
                        Timber.v("✨ [${this::class.simpleName}] Effect emitted: ${effect::class.simpleName}")
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ [${this::class.simpleName}] Exception during dispatch for intent: $intentName")
            handleDispatchError(intent, e)
        }
    }

    /**
     * 处理在 dispatch 过程中发生的异常。
     */
    protected open fun handleDispatchError(intent: I, error: Throwable) {
        // 默认行为仅记录错误。
    }

    /**
     * 便捷的状态更新方法，支持 lambda 表达式。
     */
    protected fun updateState(update: (S) -> S) {
        val newState = update(currentState)
        if (newState != currentState) {
            _state.value = newState
        }
    }

    /**
     * 便捷的 Effect 发送方法。
     */
    protected fun sendEffect(effect: E) {
        viewModelScope.launch {
            _effect.emit(effect)
        }
    }

    /**
     * EffectHandler 初始化 - 子类应在 `init` 块中调用。
     */
    protected open fun initializeEffectHandler() {
        // 子类可以重写此方法来初始化 EffectHandler。
    }

    // =========================================================================================
    //  SMART LOADING IMPLEMENTATION
    // =========================================================================================

    private fun performLoadingStateAnalysis(newState: S, intentName: String) {
        try {
            val loadingFields = detectLoadingFields(newState)
            if (loadingFields.isNotEmpty()) {
                val activeCount = loadingFields.count { it.value }
                Timber.tag("MVI-DEBUG").v(
                    "🔍 [SMART-LOADING] [$intentName] Found ${loadingFields.size} loading fields. Active: $activeCount"
                )
            }
        } catch (e: Exception) {
            Timber.w(e, "Smart Loading analysis failed. Ensure Proguard rules are set for UiState classes.")
        }
    }

    private fun detectLoadingFields(state: S): Map<String, Boolean> {
        return state::class.memberProperties
            .filter { prop ->
                prop.returnType.classifier == Boolean::class &&
                prop.name.contains("loading", ignoreCase = true)
            }
            .associate { prop ->
                prop.name to (prop.getter.call(state) as? Boolean ?: false)
            }
    }

    override fun onCleared() {
        super.onCleared()
        Timber.d("🧹 [${this::class.simpleName}] ViewModel cleared")
    }
}

/**
 * 适用于简单场景的简化版 MVI ViewModel。
 * 当功能不需要复杂的副作用（Effect）处理时，可以使用此类来减少样板代码。
 */
abstract class SimpleBaseMviViewModel<I : Any, S : Any>(
    initialState: S,
) : ViewModel() {
    private val _state = MutableStateFlow(initialState)
    val state: StateFlow<S> = _state.asStateFlow()

    protected val currentState: S
        get() = _state.value

    protected abstract fun reduce(currentState: S, intent: I): S

    fun dispatch(intent: I) {
        try {
            val newState = reduce(currentState, intent)
            if (newState != currentState) {
                _state.value = newState
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ [${this::class.simpleName}] Exception during dispatch")
        }
    }

    protected fun updateState(update: (S) -> S) {
        val newState = update(currentState)
        if (newState != currentState) {
            _state.value = newState
        }
    }
}

/**
 * 智能加载状态管理的扩展函数。
 * 允许在 UI 层或其他地方方便地检查加载状态。
 */
fun UiState.hasAnyLoading(): Boolean {
    return try {
        this::class.memberProperties.any { prop ->
            prop.returnType.classifier == Boolean::class &&
            prop.name.contains("loading", ignoreCase = true) &&
            prop.getter.call(this) as? Boolean == true
        }
    } catch (e: Exception) {
        false
    }
}
