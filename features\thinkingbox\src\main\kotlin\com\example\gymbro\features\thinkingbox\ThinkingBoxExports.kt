package com.example.gymbro.features.thinkingbox

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import kotlinx.coroutines.flow.Flow

/**
 * ThinkingBoxExports - ThinkingBox模块的公共API导出
 *
 * 🔥 【完整导出】提供完整的ThinkingBox功能导出：
 * - ThinkingBox: 主要的实时思考组件
 * - ThinkingBoxStaticRenderer: 静态历史渲染器
 * - 其他辅助组件的导出
 */

/**
 * ThinkingBox - 主要的公共API组件
 * 完整的思考过程UI组件，包括所有子组件的整合
 */
@Composable
fun ThinkingBox(
    messageId: String,
    modifier: Modifier = Modifier,
    tokenFlow: Flow<String>? = null,
) {
    // 直接调用内部实现
    ThinkingBoxInternal(
        messageId = messageId,
        modifier = modifier,
        tokenFlow = tokenFlow,
    )
}

/**
 * ThinkingBoxStaticRenderer - 静态渲染器
 * 用于显示历史消息的静态内容
 */
@Composable
fun ThinkingBoxStaticRenderer(
    finalMarkdown: String,
    modifier: Modifier = Modifier,
) {
    if (finalMarkdown.isNotBlank()) {
        com.example.gymbro.features.thinkingbox.internal.presentation.ui.StreamingFinalRenderer(
            finalTokens = listOf(finalMarkdown),
            isFinalStreaming = false,
            onRenderingComplete = null,
            modifier = modifier,
        )
    }
}

/**
 * SimpleSummaryText - 简单摘要文本组件导出
 * 供外部模块使用的摘要显示组件
 */
@Composable
fun SimpleSummaryText(
    content: String,
    modifier: Modifier = Modifier,
) {
    com.example.gymbro.features.thinkingbox.internal.presentation.ui.SimpleSummaryText(
        content = content,
        modifier = modifier,
    )
}
