package com.example.gymbro.features.workout.plan.edit

import androidx.compose.runtime.Immutable
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.plan.DayPlan
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

/**
 * Plan Edit Contract - 训练计划编辑模块的MVI 2.0契约
 *
 * 🎯 核心功能：
 * - 4周x7天训练计划编辑
 * - 模板选择和安排
 * - 日历/周视图切换
 * - 实时保存和同步
 *
 * 🏗️ 架构特点：
 * - 严格MVI 2.0合规
 * - 100% Token化设计
 * - 废弃复杂Canvas系统
 * - 简化的Compose组件实现
 */
object PlanEditContract {

    /**
     * Plan Edit State - 计划编辑界面状态
     *
     * 🔥 MVI 2.0规范：
     * - @Immutable注解确保不可变性
     * - 实现UiState接口
     * - 派生状态使用lazy计算
     * - 完整的错误状态管理
     * - JSON序列化支持，遵循GymBro JSON规范
     */
    @Immutable
    @Serializable
    data class State(
        @SerialName("plan_id") val planId: String = "",
        @SerialName("plan_name") val planName: String = "新建计划",
        @SerialName("is_loading") val isLoading: Boolean = false,
        @SerialName("is_saving") val isSaving: Boolean = false,
        @Transient val error: UiText? = null, // UiText不序列化，仅用于UI状态

        // 视图状态
        @SerialName("show_calendar_view") val showCalendarView: Boolean = false,
        @SerialName("show_template_selector") val showTemplateSelector: Boolean = false,
        @SerialName("selected_week") val selectedWeek: Int = 1,
        @SerialName("selected_day") val selectedDay: Int = 1,

        // 计划数据
        @SerialName("week_plans") val weekPlans: Map<Int, List<DayPlan>> = emptyMap(),
        @Transient val availableTemplates: ImmutableList<WorkoutTemplate> = persistentListOf(), // 模板数据不序列化，运行时加载

        // 编辑状态
        @SerialName("has_unsaved_changes") val hasUnsavedChanges: Boolean = false,
        @SerialName("last_saved_time") val lastSavedTime: Long = 0L,
    ) : UiState {

        // 🔥 派生状态：避免在Composable中计算
        val currentWeekPlans: List<DayPlan> by lazy {
            weekPlans[selectedWeek] ?: List(7) { dayIndex ->
                DayPlan.createWorkoutDay(dayIndex + 1, emptyList())
            }
        }

        val totalTrainingDays: Int by lazy {
            weekPlans.values.flatten().count { !it.isRestDay }
        }

        val totalRestDays: Int by lazy {
            weekPlans.values.flatten().count { it.isRestDay }
        }

        val canSave: Boolean by lazy {
            hasUnsavedChanges && !isSaving && !isLoading
        }

        /**
         * 获取指定周的计划
         */
        fun getWeekPlans(week: Int): List<DayPlan> {
            return weekPlans[week] ?: List(7) { dayIndex ->
                DayPlan.createWorkoutDay(dayIndex + 1, emptyList())
            }
        }

        /**
         * 获取指定日期的计划
         */
        fun getDayPlan(week: Int, day: Int): DayPlan {
            return weekPlans[week]?.getOrNull(day - 1)
                ?: DayPlan.createWorkoutDay(day, emptyList())
        }
    }

    /**
     * Plan Edit Intent - 用户意图定义
     *
     * 🔥 MVI 2.0规范：
     * - 使用动词命名
     * - 实现AppIntent接口
     * - 内部结果Intent以...Result结尾
     * - 完整的用户交互覆盖
     */
    sealed interface Intent : AppIntent {

        // === 计划管理 ===
        data class LoadPlan(val planId: String) : Intent
        data object SavePlan : Intent
        data object CreateNewPlan : Intent
        data class UpdatePlanName(val name: String) : Intent

        // === 视图控制 ===
        data object ToggleCalendarView : Intent
        data class SelectWeek(val week: Int) : Intent
        data class SelectDay(val day: Int) : Intent

        // === 日计划编辑 ===
        data class UpdateDayPlan(val week: Int, val day: Int, val dayPlan: DayPlan) : Intent
        data class ToggleRestDay(val week: Int, val day: Int) : Intent
        data class AddTemplateToDay(val week: Int, val day: Int, val templateId: String) : Intent
        data class RemoveTemplateFromDay(val week: Int, val day: Int, val templateIndex: Int) : Intent

        // === 模板选择 ===
        data class ShowTemplateSelector(val week: Int, val day: Int) : Intent
        data object HideTemplateSelector : Intent
        data class SelectTemplate(val template: WorkoutTemplate) : Intent
        data object LoadTemplates : Intent

        // === 错误处理 ===
        data object RetryLastAction : Intent
        data object ClearError : Intent

        // === 内部结果Intent ===
        data class LoadPlanResult(val planId: String, val weekPlans: Map<Int, List<DayPlan>>) : Intent
        data class LoadTemplatesResult(val templates: ImmutableList<WorkoutTemplate>) : Intent
        data class SavePlanResult(val success: Boolean, val error: UiText? = null) : Intent
        data class ErrorResult(val error: UiText) : Intent
    }

    /**
     * Plan Edit Effect - 副作用定义
     *
     * 🔥 MVI 2.0规范：
     * - 只描述副作用，不含逻辑
     * - 实现UiEffect接口
     * - 完整的UI交互支持
     */
    sealed interface Effect : UiEffect {
        data object NavigateBack : Effect
        data class ShowSnackbar(val message: UiText) : Effect
        data class ShowToast(val message: UiText) : Effect
        data object ShowSaveSuccess : Effect
        data class ShowError(val error: UiText) : Effect
        data object HideKeyboard : Effect
        data object ShowKeyboard : Effect
        data class ScrollToWeek(val week: Int) : Effect
        data class ScrollToDay(val week: Int, val day: Int) : Effect
        data object HapticFeedback : Effect
        data class SharePlan(val planId: String) : Effect
        data class ExportPlan(val planId: String) : Effect
    }
}
