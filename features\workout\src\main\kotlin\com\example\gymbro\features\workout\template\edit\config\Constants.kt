package com.example.gymbro.features.workout.template.edit.config

/**
 * 模板编辑常量配置
 * 
 * 包含模板编辑过程中使用的各种常量值
 */
object Constants {
    
    // === 默认值常量 ===
    
    /**
     * 默认组数
     */
    const val DEFAULT_SETS = 3
    
    /**
     * 默认次数
     */
    const val DEFAULT_REPS = 10
    
    /**
     * 默认重量（公斤）
     */
    const val DEFAULT_WEIGHT = 0f
    
    /**
     * 默认休息时间（秒）
     */
    const val DEFAULT_REST_TIME_SECONDS = 60
    
    /**
     * 最小组数
     */
    const val MIN_SETS = 1
    
    /**
     * 最大组数
     */
    const val MAX_SETS = 10
    
    /**
     * 最小次数
     */
    const val MIN_REPS = 1
    
    /**
     * 最大次数
     */
    const val MAX_REPS = 100
    
    /**
     * 最小重量
     */
    const val MIN_WEIGHT = 0f
    
    /**
     * 最大重量
     */
    const val MAX_WEIGHT = 1000f
    
    /**
     * 最小休息时间（秒）
     */
    const val MIN_REST_TIME = 10
    
    /**
     * 最大休息时间（秒）
     */
    const val MAX_REST_TIME = 600
    
    // === 模板限制常量 ===
    
    /**
     * 模板名称最大长度
     */
    const val MAX_TEMPLATE_NAME_LENGTH = 50
    
    /**
     * 模板描述最大长度
     */
    const val MAX_TEMPLATE_DESCRIPTION_LENGTH = 200
    
    /**
     * 模板最大动作数量
     */
    const val MAX_EXERCISES_PER_TEMPLATE = 20
    
    /**
     * 动作备注最大长度
     */
    const val MAX_EXERCISE_NOTES_LENGTH = 100
    
    // === UI相关常量 ===
    
    /**
     * 动画持续时间（毫秒）
     */
    const val ANIMATION_DURATION_MS = 300L
    
    /**
     * 长按延迟时间（毫秒）
     */
    const val LONG_PRESS_DELAY_MS = 500L
    
    /**
     * 自动保存延迟时间（毫秒）
     */
    const val AUTO_SAVE_DELAY_MS = 2000L
    
    // === 验证相关常量 ===
    
    /**
     * 模板名称最小长度
     */
    const val MIN_TEMPLATE_NAME_LENGTH = 1
    
    /**
     * 模板最少动作数量
     */
    const val MIN_EXERCISES_PER_TEMPLATE = 1
}
