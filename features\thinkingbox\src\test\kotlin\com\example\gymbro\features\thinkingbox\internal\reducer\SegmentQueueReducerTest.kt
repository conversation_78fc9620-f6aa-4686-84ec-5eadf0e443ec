package com.example.gymbro.features.thinkingbox.internal.reducer

import com.example.gymbro.features.thinkingbox.domain.model.Segment
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.util.ArrayDeque
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * SegmentQueueReducer完整测试套件
 *
 * 🎯 测试目标：验证Segment队列架构的状态管理
 * 📊 覆盖率目标：≥90%
 * 🔧 测试框架：JUnit 5 + kotlin.test
 * 🔥 重点：四条铁律支持、队列管理、History写入验证
 * 
 * 测试覆盖：
 * - Segment生命周期管理
 * - 三断点规则处理 
 * - UI渲染队列管理
 * - Final content处理
 * - 纯函数验证
 * - 边界条件处理
 */
@DisplayName("SegmentQueueReducer")
class SegmentQueueReducerTest {

    private lateinit var reducer: SegmentQueueReducer
    private lateinit var initialState: SegmentQueueReducer.TBState
    private val testMessageId = "test-message-123"

    @BeforeEach
    fun setUp() {
        reducer = SegmentQueueReducer()
        initialState = SegmentQueueReducer.TBState(messageId = testMessageId)
    }

    @Nested
    @DisplayName("Segment生命周期管理")
    inner class SegmentLifecycleManagement {

        @Test
        @DisplayName("SegmentStarted应该创建新段并闭合当前段")
        fun `SegmentStarted应该创建新段并闭合当前段`() {
            // Given - 有当前段的状态
            val existingState = SegmentQueueReducer.TBState(
                current = Segment.create("old-seg", SegmentKind.PHASE, "Old Title").apply {
                    appendText("Existing content")
                },
                messageId = testMessageId
            )

            val event = ThinkingEvent.SegmentStarted("new-seg", SegmentKind.PHASE, "New Title")

            // When
            val result = reducer.reduce(existingState, event)

            // Then - 旧段应该被闭合并入队
            assertEquals(1, result.state.queue.size)
            val enqueuedSegment = result.state.queue.first()
            assertEquals("old-seg", enqueuedSegment.id)
            assertTrue(enqueuedSegment.closed)
            assertEquals("Existing content", enqueuedSegment.getTextContent())

            // 新段应该成为current
            assertNotNull(result.state.current)
            assertEquals("new-seg", result.state.current!!.id)
            assertEquals("New Title", result.state.current!!.title)
            assertFalse(result.state.current!!.closed)
        }

        @Test
        @DisplayName("SegmentText应该追加文本到当前段")
        fun `SegmentText应该追加文本到当前段`() {
            // Given - 有当前段
            val currentSegment = Segment.create("test-seg", SegmentKind.PHASE, "Test Title")
            val state = SegmentQueueReducer.TBState(
                current = currentSegment,
                messageId = testMessageId
            )

            val event = ThinkingEvent.SegmentText("New content")

            // When
            val result = reducer.reduce(state, event)

            // Then
            assertNotNull(result.state.current)
            assertEquals("New content", result.state.current!!.getTextContent())
            assertTrue(result.state.queue.isEmpty()) // 不应该影响队列
        }

        @Test
        @DisplayName("SegmentClosed应该闭合当前段并入队")
        fun `SegmentClosed应该闭合当前段并入队`() {
            // Given - 有当前段
            val currentSegment = Segment.create("test-seg", SegmentKind.PHASE, "Test Title")
            currentSegment.appendText("Test content")
            val state = SegmentQueueReducer.TBState(
                current = currentSegment,
                messageId = testMessageId
            )

            val event = ThinkingEvent.SegmentClosed("test-seg")

            // When
            val result = reducer.reduce(state, event)

            // Then - 段应该被闭合并入队
            assertEquals(1, result.state.queue.size)
            val enqueuedSegment = result.state.queue.first()
            assertEquals("test-seg", enqueuedSegment.id)
            assertTrue(enqueuedSegment.closed)
            assertEquals("Test content", enqueuedSegment.getTextContent())

            // current应该被清空
            assertNull(result.state.current)
        }

        @Test
        @DisplayName("空内容段应该被过滤掉")
        fun `空内容段应该被过滤掉`() {
            // Given - 空内容的当前段
            val emptySegment = Segment.create("empty-seg", SegmentKind.PHASE, "Empty Title")
            // 不添加任何文本内容
            val state = SegmentQueueReducer.TBState(
                current = emptySegment,
                messageId = testMessageId
            )

            val event = ThinkingEvent.SegmentClosed("empty-seg")

            // When
            val result = reducer.reduce(state, event)

            // Then - 空段不应该入队
            assertTrue(result.state.queue.isEmpty())
            assertNull(result.state.current)
        }
    }

    @Nested
    @DisplayName("UI渲染队列管理")
    inner class UIRenderingQueueManagement {

        @Test
        @DisplayName("UiSegmentRendered应该更新段的rendered状态")
        fun `UiSegmentRendered应该更新段的rendered状态`() {
            // Given - 队列中有未渲染的段
            val segment = Segment.create("test-seg", SegmentKind.PHASE, "Test")
            segment.appendText("Content")
            segment.close()
            
            val state = SegmentQueueReducer.TBState(
                queue = ArrayDeque(listOf(segment)),
                messageId = testMessageId
            )

            val event = ThinkingEvent.UiSegmentRendered("test-seg")

            // When
            val result = reducer.reduce(state, event)

            // Then - 段的rendered状态应该被更新
            assertNotNull(result.state.queue.find { it.id == "test-seg" })
            val renderedSegment = result.state.queue.find { it.id == "test-seg" }!!
            assertTrue(renderedSegment.rendered)
        }

        @Test
        @DisplayName("渲染不存在的段ID应该不影响状态")
        fun `渲染不存在的段ID应该不影响状态`() {
            // Given - 空队列状态
            val state = SegmentQueueReducer.TBState(messageId = testMessageId)
            val event = ThinkingEvent.UiSegmentRendered("non-existent-seg")

            // When
            val result = reducer.reduce(state, event)

            // Then - 状态应该保持不变
            assertEquals(state.queue.size, result.state.queue.size)
        }
    }

    @Nested
    @DisplayName("三断点规则处理")
    inner class ThreeBreakpointRules {

        @Test
        @DisplayName("ThinkingClosed应该设置thinkingClosed标志")
        fun `ThinkingClosed应该设置thinkingClosed标志`() {
            // Given - 有active段的状态
            val state = SegmentQueueReducer.TBState(
                current = Segment.create("last-seg", SegmentKind.PHASE, "Last").apply {
                    appendText("Final thinking")
                },
                messageId = testMessageId
            )

            val event = ThinkingEvent.ThinkingClosed

            // When
            val result = reducer.reduce(state, event)

            // Then
            assertTrue(result.state.thinkingClosed)
            
            // 当前段应该被闭合并入队
            assertEquals(1, result.state.queue.size)
            assertNull(result.state.current)
            
            // 应该产生History写入Effect
            assertEquals(1, result.effects.size)
            assertTrue(result.effects.first() is ThinkingBoxContract.Effect.NotifyHistoryThinking)
        }

        @Test
        @DisplayName("FinalContent应该累积到finalBuffer")
        fun `FinalContent应该累积到finalBuffer`() {
            // Given
            val state = SegmentQueueReducer.TBState(
                thinkingClosed = true,
                finalBuffer = StringBuilder("Existing final content\n"),
                messageId = testMessageId
            )

            val event = ThinkingEvent.FinalContent("Additional final content")

            // When
            val result = reducer.reduce(state, event)

            // Then
            val finalContent = result.state.getFinalContent()
            assertTrue(finalContent.contains("Existing final content"))
            assertTrue(finalContent.contains("Additional final content"))
        }

        @Test
        @DisplayName("FinalClosed应该完成Final内容并产生History效果")
        fun `FinalClosed应该完成Final内容并产生History效果`() {
            // Given - 有final内容的状态
            val state = SegmentQueueReducer.TBState(
                thinkingClosed = true,
                finalBuffer = StringBuilder("Complete final answer"),
                messageId = testMessageId
            )

            val event = ThinkingEvent.FinalClosed

            // When
            val result = reducer.reduce(state, event)

            // Then
            assertTrue(result.state.finalClosed)
            
            // 应该产生Final History写入Effect
            assertTrue(result.effects.any { it is ThinkingBoxContract.Effect.NotifyHistoryFinal })
        }
    }

    @Nested
    @DisplayName("状态计算属性验证")
    inner class StateComputedPropertiesVerification {

        @Test
        @DisplayName("shouldCloseThinkingBox在正确条件下返回true")
        fun `shouldCloseThinkingBox在正确条件下返回true`() {
            // Given - 思考结束且队列为空
            val state = SegmentQueueReducer.TBState(
                thinkingClosed = true,
                queue = ArrayDeque(), // 空队列
                messageId = testMessageId
            )

            // When & Then
            assertTrue(state.shouldCloseThinkingBox())
        }

        @Test
        @DisplayName("shouldCloseThinkingBox在队列非空时返回false")
        fun `shouldCloseThinkingBox在队列非空时返回false`() {
            // Given - 思考结束但队列非空
            val segment = Segment.create("remaining", SegmentKind.PHASE, "Remaining")
            val state = SegmentQueueReducer.TBState(
                thinkingClosed = true,
                queue = ArrayDeque(listOf(segment)),
                messageId = testMessageId
            )

            // When & Then
            assertFalse(state.shouldCloseThinkingBox())
        }

        @Test
        @DisplayName("isFinalReadyToRender应该正确判断Final内容是否就绪")
        fun `isFinalReadyToRender应该正确判断Final内容是否就绪`() {
            // Given - 思考结束且有Final内容
            val state = SegmentQueueReducer.TBState(
                thinkingClosed = true,
                finalBuffer = StringBuilder("Final content ready"),
                queue = ArrayDeque(), // UI空闲
                messageId = testMessageId
            )

            // When & Then
            assertTrue(state.isFinalReadyToRender())
        }

        @Test
        @DisplayName("getNextSegmentToRender应该返回正确的下一个段")
        fun `getNextSegmentToRender应该返回正确的下一个段`() {
            // Given - current段已闭合，队列有段
            val currentSegment = Segment.create("current", SegmentKind.PHASE, "Current")
            currentSegment.appendText("Current content")
            currentSegment.close()
            
            val queuedSegment = Segment.create("queued", SegmentKind.PHASE, "Queued")
            queuedSegment.appendText("Queued content")
            queuedSegment.close()
            
            val state = SegmentQueueReducer.TBState(
                current = currentSegment,
                queue = ArrayDeque(listOf(queuedSegment)),
                messageId = testMessageId
            )

            // When
            val nextSegment = state.getNextSegmentToRender()

            // Then - 应该优先返回current段
            assertNotNull(nextSegment)
            assertEquals("current", nextSegment.id)
        }
    }

    @Nested
    @DisplayName("纯函数验证")
    inner class PureFunctionVerification {

        @Test
        @DisplayName("相同输入应该产生相同输出")
        fun `相同输入应该产生相同输出`() {
            // Given - 相同的状态和事件
            val state = SegmentQueueReducer.TBState(messageId = testMessageId)
            val event = ThinkingEvent.SegmentStarted("test", SegmentKind.PHASE, "Test")

            // When - 多次调用
            val result1 = reducer.reduce(state, event)
            val result2 = reducer.reduce(state, event)

            // Then - 结果应该相同
            assertEquals(result1.state.current?.id, result2.state.current?.id)
            assertEquals(result1.state.queue.size, result2.state.queue.size)
            assertEquals(result1.effects.size, result2.effects.size)
        }

        @Test
        @DisplayName("原始状态不应该被修改")
        fun `原始状态不应该被修改`() {
            // Given
            val originalQueue = ArrayDeque<Segment>()
            val originalState = SegmentQueueReducer.TBState(
                queue = originalQueue,
                messageId = testMessageId
            )
            val event = ThinkingEvent.SegmentStarted("test", SegmentKind.PHASE, "Test")

            // When
            val result = reducer.reduce(originalState, event)

            // Then - 原始状态应该保持不变
            assertTrue(originalState.queue.isEmpty())
            assertNull(originalState.current)
            
        }
    }

    @Nested
    @DisplayName("边界条件处理")
    inner class BoundaryConditionHandling {

        @Test
        @DisplayName("处理未知事件类型应该不抛异常")
        fun `处理未知事件类型应该不抛异常`() {
            // Given
            val state = SegmentQueueReducer.TBState(messageId = testMessageId)
            
            // 使用所有已知的ThinkingEvent类型
            val knownEvents = listOf(
                ThinkingEvent.SegmentStarted("test", SegmentKind.PHASE, "Test"),
                ThinkingEvent.SegmentText("text"),
                ThinkingEvent.SegmentClosed("test"),
                ThinkingEvent.ThinkingClosed,
                ThinkingEvent.FinalStart,
                ThinkingEvent.FinalContent("content"),
                ThinkingEvent.FinalClosed,
                ThinkingEvent.UiSegmentRendered("test")
            )

            // When & Then - 所有事件都应该能被处理
            knownEvents.forEach { event ->
                kotlin.runCatching {
                    reducer.reduce(state, event)
                }.onFailure { error ->
                    throw AssertionError("事件 ${event::class.simpleName} 处理失败: ${error.message}")
                }
            }
        }

        @Test
        @DisplayName("空messageId应该正常处理")
        fun `空messageId应该正常处理`() {
            // Given
            val state = SegmentQueueReducer.TBState(messageId = "")
            val event = ThinkingEvent.SegmentStarted("test", SegmentKind.PHASE, "Test")

            // When & Then - 不应该抛异常
            val result = reducer.reduce(state, event)
            assertNotNull(result.state)
        }

        @Test
        @DisplayName("大量segments不应该影响性能")
        fun `大量segments不应该影响性能`() {
            // Given - 创建大量段的状态
            val largeQueue = ArrayDeque<Segment>()
            repeat(100) { i ->
                val segment = Segment.create("seg-$i", SegmentKind.PHASE, "Segment $i")
                segment.appendText("Content $i")
                segment.close()
                largeQueue.add(segment)
            }
            
            val state = SegmentQueueReducer.TBState(
                queue = largeQueue,
                messageId = testMessageId
            )

            val event = ThinkingEvent.UiSegmentRendered("seg-50")

            // When - 处理大量数据
            val startTime = System.currentTimeMillis()
            val result = reducer.reduce(state, event)
            val endTime = System.currentTimeMillis()

            // Then - 应该在合理时间内完成（<100ms）
            val processingTime = endTime - startTime
            assertTrue(processingTime < 100, "处理时间 ${processingTime}ms 超过预期")
            assertNotNull(result.state)
        }
    }
}
