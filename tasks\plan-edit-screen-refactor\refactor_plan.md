# 重构计划: PlanEditScreen.kt

## 1. 诊断报告

### 主要问题
- **文件过大**: 2044行，远超 MVI 黄金标准的 200-300 行限制
- **职责过多**: 一个 Screen 文件包含了22个 @Composable 组件
- **架构违规**: Screen 文件应该只负责组装，不应包含大量业务组件逻辑
- **语法错误**: 第1878行存在多行字符串文档注释格式错误

### 违反的原则
- **单一职责原则**: Screen 文件包含了过多组件定义
- **组件分离原则**: 大量可复用组件混杂在 Screen 文件中
- **模块边界原则**: Screen 应该轻量级，专注于组装

### 具体"坏味道"定位
- **语法错误**: 第1878行 `/**\n * Plan Edit顶部栏组件` 多行字符串格式错误
- **组件过多**: 22个 @Composable 函数全部定义在同一文件中
- **逻辑混杂**: 包含计算逻辑、UI状态管理、组件定义等多种职责
- **Token扩展滥用**: 自定义 LocalTokens 扩展，违反设计系统规范

## 2. 重构策略

### 立即修复
1. **语法错误修复**: 修复第1878行文档注释格式
2. **文件备份**: 创建 .bak 备份文件

### 架构重构
1. **组件提取**: 将 22 个 @Composable 组件提取到独立文件
2. **Screen 瘦身**: PlanEditScreen.kt 只保留主屏幕组装逻辑
3. **模块结构优化**: 按功能和职责重新组织文件结构

### 目标文件结构
```
features/workout/plan/edit/
├── PlanEditScreen.kt                    (< 150行，仅主屏幕组装)
├── components/
│   ├── PlanEditTopBar.kt               (顶部栏组件)
│   ├── WeekSelector.kt                 (周选择器)
│   ├── DayPlanRow.kt                   (日期计划行)
│   ├── DayPlanItemChip.kt              (计划项芯片)
│   ├── PlanSummaryCard.kt              (计划汇总卡片)
│   ├── FloatingTemplatePanel.kt        (浮动模板面板)
│   └── canvas/
│       ├── PlanCanvas.kt               (画布主组件)
│       ├── DropZoneCanvas.kt           (拖拽区域)
│       └── CanvasGrid.kt               (网格布局)
└── utils/
    ├── PlanCalculations.kt             (计算工具函数)
    └── TokenExtensions.kt              (Token扩展，如需要)
```

## 3. 文件变更清单 (Checklist)

### Phase 1: 紧急修复 (立即执行)
- [ ] **备份**: 创建 `PlanEditScreen.kt.bak`
- [ ] **修复**: 第1878行语法错误
- [ ] **验证**: 编译通过

### Phase 2: 组件提取 (主要重构)
- [ ] **创建**: `components/PlanEditTopBar.kt`
- [ ] **创建**: `components/DayPlanRow.kt`
- [ ] **创建**: `components/DayPlanItemChip.kt`
- [ ] **创建**: `components/WeekSelector.kt`
- [ ] **创建**: `components/PlanSummaryCard.kt`
- [ ] **创建**: `components/FloatingTemplatePanel.kt`
- [ ] **创建**: `components/canvas/PlanCanvas.kt`
- [ ] **创建**: `components/canvas/DropZoneCanvas.kt`
- [ ] **创建**: `components/canvas/CanvasGrid.kt`
- [ ] **创建**: `utils/PlanCalculations.kt`

### Phase 3: Screen 重构 (核心整理)
- [ ] **重写**: `PlanEditScreen.kt` (仅保留主组装逻辑)
- [ ] **移除**: 所有内部组件定义
- [ ] **更新**: 导入语句和依赖
- [ ] **清理**: LocalTokens 等临时扩展

### Phase 4: 质量验证
- [ ] **编译**: 确保零编译错误
- [ ] **架构**: 验证 MVI 2.0 合规性
- [ ] **设计**: 验证 Tokens 使用规范
- [ ] **测试**: 功能回归测试

## 4. 技术实施细节

### 语法错误修复
```kotlin
// 错误的格式 (第1878行)
/**\n * Plan Edit顶部栏组件 - 符合架构规范的TopBar\n */

// 正确的格式
/**
 * Plan Edit顶部栏组件 - 符合架构规范的TopBar
 */
```

### 组件提取原则
1. **单一职责**: 每个组件文件只包含一个主要 @Composable
2. **依赖最小化**: 减少跨组件依赖
3. **可复用性**: 组件设计考虑复用性
4. **性能优化**: 使用 remember() 和 derivedStateOf 优化

### 重构后的 PlanEditScreen.kt 结构
```kotlin
@Composable
fun PlanEditScreen(
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
    planViewModel: PlanViewModel = hiltViewModel(),
    templateViewModel: TemplateViewModel = hiltViewModel(),
) {
    val state by planViewModel.state.collectAsStateWithLifecycle()
    
    Box(modifier = modifier.fillMaxSize()) {
        PlanCanvas(
            state = state,
            onIntent = planViewModel::dispatch,
            // ... 其他参数
        )
        
        FloatingTemplatePanel(
            // ... 参数
        )
    }
}
```

## 5. 风险评估与缓解

### 高风险
- **编译中断**: 大规模文件移动可能导致编译错误
- **功能回归**: 组件拆分可能影响现有功能

### 缓解措施
- **渐进式重构**: 先修复语法错误，再逐步提取组件
- **完整备份**: 重构前创建完整备份
- **分阶段验证**: 每个阶段都进行编译和功能验证

## 6. 成功标准

### 量化指标
- **文件行数**: PlanEditScreen.kt < 150行
- **组件数量**: 主文件中 @Composable < 3个
- **编译状态**: 零编译错误
- **架构合规**: 100% 符合 MVI 2.0 标准

### 质量指标
- **可读性**: 代码结构清晰，职责明确
- **可维护性**: 组件独立，易于修改
- **可测试性**: 组件可单独测试
- **复用性**: 组件可在其他场景复用