package com.example.gymbro.data.coach.repository

import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.data.coach.service.AiRequestSender
import com.example.gymbro.data.coach.service.AiResponseReceiver
import com.example.gymbro.domain.coach.model.AiTaskType
import com.example.gymbro.domain.coach.model.StreamEvent
import com.example.gymbro.domain.coach.repository.AiStreamRepository
import com.example.gymbro.domain.coach.repository.TaskCapabilities
import com.example.gymbro.shared.models.ai.ChatRequest
import kotlinx.coroutines.flow.Flow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI流式仓库实现 - 重构后的协调器
 *
 * 职责：
 * - 协调AiRequestSender和AiResponseReceiver
 * - 提供统一的Repository接口
 * - 保持向后兼容性
 */
@Singleton
class AiStreamRepositoryImpl @Inject constructor(
    private val aiRequestSender: AiRequestSender,
    private val aiResponseReceiver: AiResponseReceiver,
) : AiStreamRepository {

    override suspend fun insertThinking(
        sessionId: String,
        prompt: String,
    ): ModernResult<String> = aiRequestSender.insertThinking(sessionId, prompt)

    /**
     * 接收已构建的消息列表，避免重复prompt构建
     */
    override suspend fun streamAiResponse(
        sessionId: String,
        userMessageId: String,
        aiResponseId: String,
        messages: List<CoreChatMessage>,
        taskType: AiTaskType,
    ): Flow<StreamEvent> {
        Timber.d("🔄 协调器：开始流式AI响应 - sessionId=$sessionId, aiResponseId=$aiResponseId")

        return aiResponseReceiver.receiveStreamResponse(
            sessionId = sessionId,
            userMessageId = userMessageId,
            aiResponseId = aiResponseId,
            messages = messages,
            taskType = taskType,
        )
    }

    /**
     * 兼容性方法：使用prompt字符串（会导致重复prompt构建）
     */
    @Deprecated(
        message = "使用streamAiResponse(messages)避免重复prompt构建",
        replaceWith = ReplaceWith(
            "streamAiResponse(sessionId, userMessageId, aiResponseId, messages, taskType)",
        ),
        level = DeprecationLevel.WARNING,
    )
    override suspend fun streamAiResponseLegacy(
        sessionId: String,
        userMessageId: String,
        aiResponseId: String,
        prompt: String,
        taskType: AiTaskType,
    ): Flow<StreamEvent> {
        Timber.d("🔄 协调器：兼容性方法 - sessionId=$sessionId, aiResponseId=$aiResponseId")
        Timber.w("⚠️ 警告：使用兼容性方法会导致重复prompt构建，请迁移到streamAiResponse(messages)")

        // 使用AiRequestSender构建消息列表
        val messages = aiRequestSender.buildRequestMessages(
            sessionId = sessionId,
            prompt = prompt,
            taskType = taskType,
        )

        // 委托给主要方法
        return streamAiResponse(
            sessionId = sessionId,
            userMessageId = userMessageId,
            aiResponseId = aiResponseId,
            messages = messages,
            taskType = taskType,
        )
    }

    /**
     * 兼容性方法：保持向后兼容
     */
    @Deprecated("使用带完整上下文的streamAiResponse方法替代")
    override fun streamAi(
        prompt: String,
        sessionId: String,
        userMessageId: String,
        aiResponseId: String,
    ): Flow<StreamEvent> {
        Timber.w("⚠️ 使用已废弃的streamAi方法")

        // 由于是suspend函数，这里需要在协程中调用
        return kotlinx.coroutines.flow.flow {
            val messages = aiRequestSender.buildRequestMessages(
                sessionId = sessionId,
                prompt = prompt,
                taskType = AiTaskType.CHAT,
            )

            streamAiResponse(
                sessionId = sessionId,
                userMessageId = userMessageId,
                aiResponseId = aiResponseId,
                messages = messages,
                taskType = AiTaskType.CHAT,
            ).collect { event ->
                emit(event)
            }
        }
    }

    /**
     * 基于任务类型的流式聊天
     */
    override suspend fun streamChatWithTaskType(
        request: ChatRequest,
        taskType: AiTaskType,
    ): Flow<String> {
        Timber.d("🔄 协调器：任务类型流式聊天 - taskType=$taskType")

        // 优化请求参数
        val optimizedRequest = aiRequestSender.optimizeRequestForTask(request, taskType)

        return aiResponseReceiver.streamChatWithTaskType(optimizedRequest, taskType)
    }

    /**
     * 事件总线方法：将token流发布到全局TokenBus
     */
    override suspend fun streamChatWithMessageId(
        request: ChatRequest,
        messageId: String,
        taskType: AiTaskType,
    ): Flow<com.example.gymbro.core.network.output.OutputToken> {
        Timber.d("🔄 协调器：事件总线流式请求 - messageId=$messageId, taskType=$taskType")

        // 优化请求参数
        val optimizedRequest = aiRequestSender.optimizeRequestForTask(request, taskType)

        return aiResponseReceiver.streamChatWithMessageId(optimizedRequest, messageId, taskType)
    }

    /**
     * 获取任务类型支持的功能
     */
    override suspend fun getTaskCapabilities(taskType: AiTaskType): TaskCapabilities {
        return aiRequestSender.getTaskCapabilities(taskType)
    }

    /**
     * 获取监控指标
     */
    fun getMetrics(): String {
        return aiResponseReceiver.getMetrics()
    }
}
