package com.example.gymbro.features.thinkingbox.api

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.gymbro.features.thinkingbox.api.ThinkingBoxCompletionListener
import com.example.gymbro.features.thinkingbox.ThinkingBoxInternal
import com.example.gymbro.features.thinkingbox.internal.presentation.viewmodel.ThinkingBoxViewModel
import kotlinx.coroutines.flow.Flow
import timber.log.Timber

/**
 * ThinkingBoxWithCallback - 支持完成回调的ThinkingBox组件
 *
 * 🔥 【职责分离重构】新的公共API：
 * - 支持设置完成回调监听器
 * - 自动管理ViewModel的回调设置
 * - 保持与现有ThinkingBox的兼容性
 * - 用于Coach-ThinkingBox架构重构
 *
 * 使用场景：
 * ```kotlin
 * ThinkingBoxWithCallback(
 *     messageId = "ai-response-123",
 *     sessionId = "session-456",
 *     completionListener = coachCompletionListener,
 *     modifier = Modifier.fillMaxWidth()
 * )
 * ```
 */
@Composable
fun ThinkingBoxWithCallback(
    messageId: String,
    sessionId: String,
    completionListener: ThinkingBoxCompletionListener? = null,
    modifier: Modifier = Modifier,
    tokenFlow: Flow<String>? = null,
    viewModel: ThinkingBoxViewModel = hiltViewModel()
) {
    // 🔥 【职责分离】设置完成回调监听器
    LaunchedEffect(sessionId, completionListener) {
        if (completionListener != null) {
            Timber.i("TB-API: 🔗 设置ThinkingBox完成回调: sessionId=$sessionId, messageId=$messageId")
            // TODO: 实现setCompletionListener方法
            // viewModel.setCompletionListener(sessionId, completionListener)
        } else {
            Timber.d("TB-API: ⚠️ 未设置完成回调监听器: messageId=$messageId")
        }
    }

    // 🔥 【兼容性】使用现有的ThinkingBoxInternal组件
    ThinkingBoxInternal(
        messageId = messageId,
        modifier = modifier,
        tokenFlow = tokenFlow,
        viewModel = viewModel
    )
}

/**
 * ThinkingBoxWithCallback - 简化版本（无tokenFlow）
 *
 * 最常用的API，适用于大多数场景
 */
@Composable
fun ThinkingBoxWithCallback(
    messageId: String,
    sessionId: String,
    completionListener: ThinkingBoxCompletionListener? = null,
    modifier: Modifier = Modifier
) {
    ThinkingBoxWithCallback(
        messageId = messageId,
        sessionId = sessionId,
        completionListener = completionListener,
        modifier = modifier,
        tokenFlow = null
    )
}

/**
 * 🔥 【向后兼容】ThinkingBox别名
 *
 * 保持与现有代码的兼容性，逐步迁移到新API
 */
@Composable
fun ThinkingBox(
    messageId: String,
    modifier: Modifier = Modifier,
    tokenFlow: Flow<String>? = null
) {
    Timber.d("TB-API: 📢 使用兼容模式ThinkingBox: messageId=$messageId")

    ThinkingBoxInternal(
        messageId = messageId,
        modifier = modifier,
        tokenFlow = tokenFlow
    )
}
