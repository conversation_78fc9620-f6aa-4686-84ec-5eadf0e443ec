package com.example.gymbro.core.logging

import android.util.Log
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 【重构】统一日志配置管理器
 *
 * 功能：
 * - 模块级别的日志控制
 * - 运行时动态调整日志级别
 * - 生产环境和开发环境的不同策略
 * - 性能优化的日志过滤
 */
@Singleton
class LoggingConfig
@Inject
constructor() {
    /**
     * 模块日志配置
     */
    data class ModuleLogConfig(
        val enabled: Boolean = true,
        val minLevel: Int = Log.DEBUG,
        val tags: Set<String> = emptySet(),
        val sampleRate: Int = 1, // 采样率：1=全量，10=每10条记录1条
    )

    /**
     * 环境类型
     */
    enum class Environment {
        DEVELOPMENT,
        STAGING,
        PRODUCTION,
    }

    // 当前环境
    @Volatile
    private var currentEnvironment = Environment.DEVELOPMENT

    // 模块配置映射
    private val moduleConfigs = ConcurrentHashMap<String, ModuleLogConfig>()

    // 全局日志开关
    @Volatile
    private var globalEnabled = true

    init {
        setupDefaultConfigs()
    }

    /**
     * 设置默认配置
     */
    private fun setupDefaultConfigs() {
        // ThinkingBox 模块配置 - 🔥 大幅减少日志噪音
        moduleConfigs[MODULE_THINKING_BOX] = ModuleLogConfig(
            enabled = true,
            minLevel = Log.WARN, // 🔥 只记录WARN级别以上，减少噪音
            tags = setOf("TB-ERROR"), // 🔥 只保留错误日志
            sampleRate = 1,
        )

        // Coach 模块配置
        moduleConfigs[MODULE_COACH] = ModuleLogConfig(
            enabled = true,
            minLevel = Log.DEBUG, // 🔥 【消息流调试】降低到DEBUG级别
            tags = setOf(
                "COACH-ERROR",
                // 🔥 【消息流调试】添加关键调试标签，使用正确的模块前缀
                "COACH-MESSAGE-FLOW", "TOKEN-FLOW", "REDUCER-DEBUG", "EFFECT-FLOW",
                "MVI-DEBUG",
            ),
            sampleRate = 1,
        )

        // Workout 模块配置 - 使用WK前缀，优化调试噪音
        moduleConfigs[MODULE_WORKOUT] = ModuleLogConfig(
            enabled = true,
            minLevel = Log.DEBUG,
            tags = setOf(
                "WK-CORE", "WK-DATA", "WK-EXERCISE", "WK-DEBUG", "WK-ERROR", "WK-PERF",
                // 🔥 新增：WK保存链路跟踪标签
                "WK-SAVE-START", "WK-SAVE-PROCESS", "WK-SAVE-VALIDATE", "WK-SAVE-CANCEL",
                "WK-SAVE-DETERMINE", "WK-SAVE-CREATE-MODE", "WK-SAVE-UPDATE-MODE",
                "WK-SAVE-NAME-FINAL", "WK-SAVE-BUILD", "WK-SAVE-EXECUTE",
                "WK-SAVE-TRANSACTION", "WK-SAVE-COMPLETE", "WK-SAVE-NOTIFY",
                "WK-SAVE-SUCCESS", "WK-SAVE-FAILED", "WK-SAVE-TX-START",
                "WK-SAVE-TX-VALIDATE", "WK-SAVE-TX-VALIDATE-OK", "WK-SAVE-TX-VALIDATE-FAILED",
                "WK-SAVE-TX-PREPARE", "WK-SAVE-TX-JSON-CHECK", "WK-SAVE-TX-FC-CHECK",
                "WK-SAVE-TX-FC-OK", "WK-SAVE-TX-ATOMIC", "WK-SAVE-TX-SUCCESS", "WK-SAVE-TX-FAILED",
                // 旧标签保持兼容
                "WK-TEMPLATE", "WK-JSON", "WK-DB", "WK-MAPPER", "WK-STATE", "WK-CRITICAL", "WK-VALIDATION",
                "CRITICAL-SAVE", "CRITICAL-LOAD", "CRITICAL-DB", "CRITICAL-EFFECT",
                "CRITICAL-PUBLISH", "CRITICAL-SAVE-START", "CRITICAL-SAVE-SUCCESS",
                "CRITICAL-SAVE-ERROR", "CRITICAL-DEBUG", "CRITICAL-EFFECT-COLLECT",
            ),
            sampleRate = 1,
        )

        // Core 模块配置
        moduleConfigs[MODULE_CORE] = ModuleLogConfig(
            enabled = true,
            minLevel = Log.DEBUG,
            tags = setOf("CORE-ERROR", "NETWORK", "DATABASE"),
            sampleRate = 1,
        )

        // 🔥 【新增】Core-Network 模块配置
        moduleConfigs[MODULE_CORE_NETWORK] = ModuleLogConfig(
            enabled = true,
            minLevel = Log.DEBUG,
            tags = setOf(
                "CNET-ERROR", // 🔥 【关键】网络错误日志标签
                "CNET-STREAM", "CNET-CHECK", "CNET-SSE", "CNET-MONITOR",
                "CNET-RETRY", "CNET-LIFECYCLE", "CNET-SECURITY", "CNET-PERF",
                "CNET-GENERAL",
                // 🔥 【Token流修复】使用正确的CNT模块前缀
                // 旧架构组件已删除：CNT-TOKEN-ROUTER, ConversationScope
                // 🔥 【消息流调试】使用正确的CNT模块前缀
                "CNT-MESSAGE-FLOW",
                // 🔥 【AI响应管道调试】添加新的调试标签
                "HTTP-RAW-RESPONSE", "HTTP-RAW-XML", "JSON-PARSE-INPUT",
                "JSON-PARSE-CLEANED", "JSON-PARSE-CRITICAL", "JSON-PARSE-TEST",
                "JSON-FIELD-EXTRACT", "JSON-FIELD-ALL", "JSON-DEBUG",
                "XML-REASSEMBLY-RESULT", "FINAL-CONTENT",
                "ASC-RAW-TOKENS", "CNT-RAW-TOKENS",
            ),
            sampleRate = 1,
        )

        // DesignSystem 模块配置
        moduleConfigs[MODULE_DESIGN_SYSTEM] = ModuleLogConfig(
            enabled = true,
            minLevel = Log.WARN,
            tags = setOf("UI-ERROR"),
            sampleRate = 1,
        )
    }

    /**
     * 设置环境
     */
    fun setEnvironment(environment: Environment) {
        currentEnvironment = environment
        applyEnvironmentSettings()
    }

    /**
     * 应用环境设置
     */
    private fun applyEnvironmentSettings() {
        when (currentEnvironment) {
            Environment.DEVELOPMENT -> {
                // 开发环境：适度的日志输出，减少TB噪音
                updateModuleConfig(
                    MODULE_THINKING_BOX,
                    ModuleLogConfig(
                        enabled = true,
                        minLevel = Log.ERROR, // 🔥 开发环境也只记录错误
                        tags = setOf("TB-ERROR"), // 🔥 大幅简化
                        sampleRate = 1,
                    ),
                )
            }

            Environment.STAGING -> {
                // 测试环境：更多日志用于调试
                updateModuleConfig(
                    MODULE_THINKING_BOX,
                    ModuleLogConfig(
                        enabled = true,
                        minLevel = Log.DEBUG,
                        tags = setOf("TB-ERROR", "TB-STATE", "TB-UI", "TB-MERMAID"),
                        sampleRate = 5, // 采样输出
                    ),
                )
            }

            Environment.PRODUCTION -> {
                // 生产环境：只记录错误和关键信息
                moduleConfigs.forEach { (module, _) ->
                    updateModuleConfig(
                        module,
                        ModuleLogConfig(
                            enabled = true,
                            minLevel = Log.WARN,
                            tags = setOf("ERROR", "CRASH"),
                            sampleRate = 1,
                        ),
                    )
                }
            }
        }
    }

    /**
     * 更新模块配置
     */
    fun updateModuleConfig(
        module: String,
        config: ModuleLogConfig,
    ) {
        moduleConfigs[module] = config
    }

    /**
     * 获取模块配置
     */
    fun getModuleConfig(module: String): ModuleLogConfig? = moduleConfigs[module]

    /**
     * 检查全局日志开关状态
     */
    fun isGlobalEnabled(): Boolean = globalEnabled

    /**
     * 设置全局日志开关
     */
    fun setGlobalEnabled(enabled: Boolean) {
        globalEnabled = enabled
    }

    /**
     * 检查是否应该记录日志
     */
    fun shouldLog(
        module: String,
        tag: String?,
        priority: Int,
    ): Boolean {
        if (!globalEnabled) return false

        val config = moduleConfigs[module] ?: return false
        if (!config.enabled) return false
        if (priority < config.minLevel) return false

        // 检查标签过滤
        if (config.tags.isNotEmpty() && tag != null) {
            val shouldLogByTag =
                config.tags.any { allowedTag ->
                    tag.contains(allowedTag, ignoreCase = true)
                }
            if (!shouldLogByTag) return false
        }

        return true
    }

    /**
     * 获取当前环境
     */
    fun getCurrentEnvironment(): Environment = currentEnvironment

    companion object {
        // 模块常量
        const val MODULE_THINKING_BOX = "thinkingbox"
        const val MODULE_COACH = "coach"
        const val MODULE_WORKOUT = "workout"
        const val MODULE_CORE = "core"
        const val MODULE_CORE_NETWORK = "core-network" // 🔥 【新增】网络模块
        const val MODULE_DESIGN_SYSTEM = "designsystem"
    }
}

/**
 * 🔥 【重构】模块感知的 Timber Tree
 *
 * 根据模块配置自动过滤日志
 */
class ModuleAwareTree(
    private val loggingConfig: LoggingConfig,
) : Timber.DebugTree() {
    private val sampleCounters = ConcurrentHashMap<String, Int>()

    override fun log(
        priority: Int,
        tag: String?,
        message: String,
        t: Throwable?,
    ) {
        // 🔥 【重构清理】移除WorkoutLogTree跳过逻辑，统一由ModuleAwareTree处理
        // workout模块日志现在统一由ModuleAwareTree处理，消除重复Tree问题

        // 🔥 避免与NetworkLogTree重复处理
        if (shouldSkipForNetworkLogTree(tag)) {
            return
        }

        // 🔥 新增：噪音日志过滤
        if (shouldFilterNoiseLog(tag, message)) {
            return
        }

        val module = determineModule(tag)

        // 检查是否应该记录
        if (!loggingConfig.shouldLog(module, tag, priority)) {
            return
        }

        // 采样控制
        val config = loggingConfig.getModuleConfig(module)
        if (config != null && config.sampleRate > 1) {
            val key = "$module-$tag"
            val count = sampleCounters.merge(key, 1) { old, _ -> old + 1 } ?: 1
            if (count % config.sampleRate != 0) {
                return
            }
        }

        super.log(priority, tag, message, t)
    }

    /**
     * 🔥 【保留】检查是否应该跳过给NetworkLogTree处理
     * 避免ModuleAwareTree和NetworkLogTree重复处理同一条日志
     */
    private fun shouldSkipForNetworkLogTree(tag: String?): Boolean {
        if (tag == null) return false

        // 跳过所有CNET-前缀标签，交给NetworkLogTree处理
        return tag.startsWith("CNET-")
    }

    /**
     * 噪音日志过滤逻辑
     */
    private fun shouldFilterNoiseLog(tag: String?, message: String): Boolean {
        // 🔥 【System.out日志控制】只允许ERROR级别的System.out日志
        if (tag == "System.out") {
            // 过滤所有DEBUG级别的System.out日志
            if (message.contains("[DEBUG]", ignoreCase = true) ||
                message.contains("🔧", ignoreCase = true) ||
                message.contains("Processing:", ignoreCase = true) ||
                message.contains("Reducing intent:", ignoreCase = true)
            ) {
                return true
            }
        }

        // 过滤 MVI 架构噪音日志
        if (tag == "System.out" && (
                message.contains("[DEBUG] BaseMviViewModel") ||
                    message.contains("HomeViewModel") ||
                    message.contains("TemplateViewModel")
                )
        ) {
            return true
        }

        return false
    }

    /**
     * 根据标签确定模块 - 专门针对perthink phase调试优化
     */
    private fun determineModule(tag: String?): String =
        when {
            // 🔥 【perthink调试】ThinkingBox相关标签
            tag?.startsWith("TB-") == true -> LoggingConfig.MODULE_THINKING_BOX
            tag?.contains("ThinkingBox") == true -> LoggingConfig.MODULE_THINKING_BOX
            tag?.contains("ThinkingHeader") == true -> LoggingConfig.MODULE_THINKING_BOX
            tag?.contains("FINAL-DEBUG") == true -> LoggingConfig.MODULE_THINKING_BOX
            tag?.contains("SimpleSummaryText") == true -> LoggingConfig.MODULE_THINKING_BOX
            tag?.contains("状态映射完成") == true -> LoggingConfig.MODULE_THINKING_BOX
            tag?.startsWith("PHASE-DEBUG") == true -> LoggingConfig.MODULE_THINKING_BOX
            tag?.contains("RAW-TOKEN-COLLECTOR") == true -> LoggingConfig.MODULE_THINKING_BOX

            // 🔥 【perthink调试】TOKEN-FLOW归类到Core模块
            tag?.contains("TOKEN-FLOW") == true -> LoggingConfig.MODULE_CORE

            // 🔥 【消息流调试】使用正确的模块前缀路由
            tag?.contains("COACH-MESSAGE-FLOW") == true -> LoggingConfig.MODULE_COACH
            tag?.contains("CNT-MESSAGE-FLOW") == true -> LoggingConfig.MODULE_CORE_NETWORK
            tag?.contains("CNT-TOKEN-ROUTER") == true -> LoggingConfig.MODULE_CORE_NETWORK

            // 🔥 【减少噪音】其他模块标签
            tag?.startsWith("COACH-") == true -> LoggingConfig.MODULE_COACH
            tag?.startsWith("WORKOUT-") == true -> LoggingConfig.MODULE_WORKOUT

            // 🔥 【新增】Workout模块WK前缀标签
            tag?.startsWith("WK-") == true -> LoggingConfig.MODULE_WORKOUT

            // 🔥 【新增】Core-Network模块CNET前缀标签
            tag?.startsWith("CNET-") == true -> LoggingConfig.MODULE_CORE_NETWORK

            // 🔥 【保留】Template 调试标签归类 - 归属到WORKOUT模块
            tag?.startsWith("TEMPLATE-WEIGHT-DEBUG") == true -> LoggingConfig.MODULE_WORKOUT
            tag?.startsWith("CRITICAL-") == true -> LoggingConfig.MODULE_WORKOUT
            tag?.startsWith("JSON-PARSE") == true -> LoggingConfig.MODULE_WORKOUT
            tag?.startsWith("EXERCISE-ADD") == true -> LoggingConfig.MODULE_WORKOUT
            tag?.startsWith("SAVE-HANDLER") == true -> LoggingConfig.MODULE_WORKOUT
            tag?.startsWith("P0-") == true -> LoggingConfig.MODULE_WORKOUT
            tag?.startsWith("DATA-") == true -> LoggingConfig.MODULE_WORKOUT
            tag?.startsWith("ADD-") == true -> LoggingConfig.MODULE_WORKOUT

            // 🔥 【默认】其他标签归类到Core
            else -> LoggingConfig.MODULE_CORE
        }
}
