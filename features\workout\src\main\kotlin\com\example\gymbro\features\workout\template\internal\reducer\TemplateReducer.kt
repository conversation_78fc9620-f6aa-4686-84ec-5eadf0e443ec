package com.example.gymbro.features.workout.template.internal.reducer

import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.features.workout.template.TemplateContract
import javax.inject.Inject
import javax.inject.Singleton

/**
 * =========================================================================================
 * 🔥 GymBro Template Reducer - 遵循黄金标准 🔥
 * =========================================================================================
 *
 * 本Reducer遵循ProfileBio黄金标准，专注于纯状态转换。
 *
 * 🎯 核心职责：
 * 1. 纯状态转换：(Intent, State) -> (NewState, Effect?)
 * 2. 不执行副作用：所有副作用通过Effect传递给EffectHandler
 * 3. 简洁高效：移除不必要的抽象层和复杂逻辑
 * 4. 职责明确：主Screen只负责模板列表管理，动作管理在EditScreen
 *
 * ✅ 已优化：
 * - 实现标准Reducer接口
 * - 使用标准ReduceResult
 * - 纯函数状态转换
 */
@Singleton
internal class TemplateReducer @Inject constructor() :
    Reducer<TemplateContract.Intent, TemplateContract.State, TemplateContract.Effect> {

    /**
     * 主要的状态缩减函数 - 纯状态转换
     */
    override fun reduce(
        intent: TemplateContract.Intent,
        currentState: TemplateContract.State,
    ): ReduceResult<TemplateContract.State, TemplateContract.Effect> {
        return when (intent) {
            // === 数据加载 ===
            is TemplateContract.Intent.LoadTemplates -> ReduceResult.withEffect(
                newState = currentState.copy(isLoading = true, error = null),
                effect = TemplateContract.Effect.LoadTemplatesData
            )

            is TemplateContract.Intent.RefreshTemplates -> ReduceResult.withEffect(
                newState = currentState.copy(isLoading = true, error = null),
                effect = TemplateContract.Effect.RefreshTemplatesData
            )

            is TemplateContract.Intent.LoadDrafts -> ReduceResult.withEffect(
                newState = currentState.copy(isLoadingDrafts = true, error = null),
                effect = TemplateContract.Effect.LoadDraftsData
            )

            is TemplateContract.Intent.RefreshDrafts -> ReduceResult.withEffect(
                newState = currentState.copy(isLoadingDrafts = true, error = null),
                effect = TemplateContract.Effect.RefreshDraftsData
            )

            // === 模板操作 ===
            is TemplateContract.Intent.NavigateToCreateTemplate -> ReduceResult.withEffect(
                newState = currentState,
                effect = TemplateContract.Effect.NavigateToCreateTemplate
            )

            is TemplateContract.Intent.NavigateToEditTemplate -> ReduceResult.withEffect(
                newState = currentState,
                effect = TemplateContract.Effect.NavigateToEditTemplate(intent.templateId)
            )

            is TemplateContract.Intent.DeleteTemplate -> ReduceResult.withEffect(
                newState = currentState.copy(isDeleting = true),
                effect = TemplateContract.Effect.DeleteTemplate(intent.templateId)
            )

            // === 搜索和筛选 ===
            is TemplateContract.Intent.UpdateSearchQuery -> ReduceResult.stateOnly(
                newState = currentState.copy(
                    searchQuery = intent.query,
                    filteredTemplates = filterTemplates(currentState.templates, intent.query),
                    filteredDrafts = filterDrafts(currentState.drafts, intent.query)
                )
            )

            is TemplateContract.Intent.ToggleSearch -> ReduceResult.stateOnly(
                newState = currentState.copy(
                    showSearchField = !currentState.showSearchField,
                    searchQuery = if (!currentState.showSearchField) currentState.searchQuery else "",
                    filteredTemplates = if (!currentState.showSearchField) currentState.filteredTemplates else currentState.templates,
                    filteredDrafts = if (!currentState.showSearchField) currentState.filteredDrafts else currentState.drafts
                )
            )

            is TemplateContract.Intent.ClearSearch -> ReduceResult.stateOnly(
                newState = currentState.copy(
                    searchQuery = "",
                    showSearchField = false,
                    filteredTemplates = currentState.templates,
                    filteredDrafts = currentState.drafts
                )
            )

            // === UI状态管理 ===
            is TemplateContract.Intent.SwitchTab -> ReduceResult.stateOnly(
                newState = currentState.copy(currentTab = intent.tab)
            )

            is TemplateContract.Intent.ClearError -> ReduceResult.stateOnly(
                newState = currentState.copy(error = null)
            )

            // === 结果处理 ===
            is TemplateContract.Intent.TemplatesLoaded -> ReduceResult.stateOnly(
                newState = currentState.copy(
                    templates = intent.templates,
                    filteredTemplates = if (currentState.searchQuery.isBlank()) {
                        intent.templates
                    } else {
                        filterTemplates(intent.templates, currentState.searchQuery)
                    },
                    isLoading = false,
                    error = null
                )
            )

            is TemplateContract.Intent.DraftsLoaded -> ReduceResult.stateOnly(
                newState = currentState.copy(
                    drafts = intent.drafts,
                    filteredDrafts = if (currentState.searchQuery.isBlank()) {
                        intent.drafts
                    } else {
                        filterDrafts(intent.drafts, currentState.searchQuery)
                    },
                    isLoadingDrafts = false,
                    error = null
                )
            )

            is TemplateContract.Intent.TemplateDeleted -> ReduceResult.stateOnly(
                newState = currentState.copy(
                    templates = currentState.templates.filter { it.id != intent.templateId },
                    filteredTemplates = currentState.filteredTemplates.filter { it.id != intent.templateId },
                    isDeleting = false
                )
            )

            is TemplateContract.Intent.LoadError -> ReduceResult.stateOnly(
                newState = currentState.copy(
                    error = intent.error,
                    isLoading = false,
                    isLoadingDrafts = false,
                    isDeleting = false
                )
            )
        }
    }

    // === 辅助函数 ===

    private fun filterTemplates(
        templates: List<com.example.gymbro.domain.workout.model.template.WorkoutTemplate>,
        query: String
    ): List<com.example.gymbro.domain.workout.model.template.WorkoutTemplate> {
        if (query.isBlank()) return templates
        return templates.filter { template ->
            template.name.contains(query, ignoreCase = true) ||
            template.description.contains(query, ignoreCase = true)
        }
    }

    private fun filterDrafts(
        drafts: List<com.example.gymbro.domain.workout.model.template.WorkoutTemplate>,
        query: String
    ): List<com.example.gymbro.domain.workout.model.template.WorkoutTemplate> {
        if (query.isBlank()) return drafts
        return drafts.filter { draft ->
            draft.name.contains(query, ignoreCase = true) ||
            draft.description.contains(query, ignoreCase = true)
        }
    }
}
