package com.example.gymbro.features.workout.json.processor

import com.example.gymbro.features.workout.shared.utils.PlanTestDataFactory
import com.example.gymbro.shared.models.workout.DayPlan
import com.example.gymbro.shared.models.workout.PlanProgressStatus
import io.mockk.MockKAnnotations
import io.mockk.clearAllMocks
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * Plan动态修改和批量更新测试
 * 
 * 🎯 测试目标：
 * - PlanJsonProcessor的动态修改功能
 * - 批量更新操作的原子性
 * - 字段更新的准确性验证
 * - 版本控制和时间戳更新
 * - 数据一致性保证
 * 
 * 🔍 测试覆盖范围：
 * - Plan名称/描述更新
 * - 总天数调整和DayPlan同步
 * - DayPlan添加/移除/修改
 * - Template引用管理
 * - 批量更新事务处理
 * - 更新失败回滚机制
 * 
 * <AUTHOR> AI Assistant
 * @since 1.0.0
 */
@DisplayName("Plan动态修改和批量更新测试")
class PlanJsonDynamicUpdateTest {
    
    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }
    
    @AfterEach
    fun tearDown() {
        clearAllMocks()
    }
    
    // ==================== Plan基础字段更新测试 ====================
    
    @Nested
    @DisplayName("Plan基础字段更新测试")
    inner class BasicFieldUpdateTests {
        
        @Test
        @DisplayName("更新Plan名称应该成功并更新时间戳")
        fun `updating plan name should succeed and update timestamp`() {
            // Given
            val originalPlan = PlanTestDataFactory.createSimplePlan()
            val originalJsonString = originalPlan.toJson()
            val newName = "更新后的计划名称"
            val originalUpdatedAt = originalPlan.updatedAt
            
            // When
            val updatedJsonString = PlanJsonProcessor.updatePlanName(originalJsonString, newName)
            val updatedPlan = PlanJsonProcessor.fromJson(updatedJsonString)
            
            // Then
            assertNotNull(updatedPlan, "更新后的Plan不应为null")
            assertEquals(newName, updatedPlan.name, "计划名称应已更新")
            assertEquals(originalPlan.id, updatedPlan.id, "计划ID应保持不变")
            assertEquals(originalPlan.userId, updatedPlan.userId, "用户ID应保持不变")
            assertTrue(updatedPlan.updatedAt > originalUpdatedAt, "更新时间戳应该增加")
            
            // 验证其他字段未被修改
            assertEquals(originalPlan.description, updatedPlan.description, "描述应保持不变")
            assertEquals(originalPlan.totalDays, updatedPlan.totalDays, "总天数应保持不变")
            assertEquals(originalPlan.dailySchedule.size, updatedPlan.dailySchedule.size, "日程数量应保持不变")
        }
        
        @Test
        @DisplayName("更新Plan描述应该成功")
        fun `updating plan description should succeed`() {
            // Given
            val originalPlan = PlanTestDataFactory.createSimplePlan()
            val originalJsonString = originalPlan.toJson()
            val newDescription = "这是更新后的计划描述，包含更详细的信息"
            
            // When
            val updatedJsonString = PlanJsonProcessor.updatePlanDescription(originalJsonString, newDescription)
            val updatedPlan = PlanJsonProcessor.fromJson(updatedJsonString)
            
            // Then
            assertNotNull(updatedPlan, "更新后的Plan不应为null")
            assertEquals(newDescription, updatedPlan.description, "计划描述应已更新")
            assertEquals(originalPlan.name, updatedPlan.name, "计划名称应保持不变")
            assertEquals(originalPlan.id, updatedPlan.id, "计划ID应保持不变")
        }
        
        @Test
        @DisplayName("清空Plan描述应该成功")
        fun `clearing plan description should succeed`() {
            // Given
            val originalPlan = PlanTestDataFactory.createComplexPlan() // 有描述的计划
            val originalJsonString = originalPlan.toJson()
            
            // When
            val updatedJsonString = PlanJsonProcessor.updatePlanDescription(originalJsonString, null)
            val updatedPlan = PlanJsonProcessor.fromJson(updatedJsonString)
            
            // Then
            assertNotNull(updatedPlan, "更新后的Plan不应为null")
            assertEquals(null, updatedPlan.description, "计划描述应被清空")
        }
        
        @Test
        @DisplayName("更新Plan总天数应该同步调整DayPlan")
        fun `updating plan total days should synchronize day plans`() {
            // Given
            val originalPlan = PlanTestDataFactory.createSimplePlan() // 3天计划
            val originalJsonString = originalPlan.toJson()
            val newTotalDays = 5
            
            // When
            val updatedJsonString = PlanJsonProcessor.updatePlanTotalDays(originalJsonString, newTotalDays)
            val updatedPlan = PlanJsonProcessor.fromJson(updatedJsonString)
            
            // Then
            assertNotNull(updatedPlan, "更新后的Plan不应为null")
            assertEquals(newTotalDays, updatedPlan.totalDays, "总天数应已更新")
            assertEquals(newTotalDays, updatedPlan.dailySchedule.size, "DayPlan数量应与总天数匹配")
            
            // 验证原有的DayPlan保持不变
            (1..originalPlan.totalDays).forEach { dayNumber ->
                val originalDayPlan = originalPlan.dailySchedule[dayNumber]
                val updatedDayPlan = updatedPlan.dailySchedule[dayNumber]
                if (originalDayPlan != null && updatedDayPlan != null) {
                    assertEquals(originalDayPlan.isRestDay, updatedDayPlan.isRestDay, "第${dayNumber}天的休息日状态应保持不变")
                    assertEquals(originalDayPlan.templateIds, updatedDayPlan.templateIds, "第${dayNumber}天的Template引用应保持不变")
                }
            }
            
            // 验证新增的DayPlan是休息日
            (originalPlan.totalDays + 1..newTotalDays).forEach { dayNumber ->
                val newDayPlan = updatedPlan.dailySchedule[dayNumber]
                assertNotNull(newDayPlan, "第${dayNumber}天应存在")
                assertTrue(newDayPlan.isRestDay, "新增的第${dayNumber}天应为休息日")
                assertTrue(newDayPlan.templateIds.isEmpty(), "新增的第${dayNumber}天不应有Template引用")
            }
        }
        
        @Test
        @DisplayName("减少Plan总天数应该移除多余的DayPlan")
        fun `reducing plan total days should remove excess day plans`() {
            // Given
            val originalPlan = PlanTestDataFactory.createComplexPlan() // 7天计划
            val originalJsonString = originalPlan.toJson()
            val newTotalDays = 4
            
            // When
            val updatedJsonString = PlanJsonProcessor.updatePlanTotalDays(originalJsonString, newTotalDays)
            val updatedPlan = PlanJsonProcessor.fromJson(updatedJsonString)
            
            // Then
            assertNotNull(updatedPlan, "更新后的Plan不应为null")
            assertEquals(newTotalDays, updatedPlan.totalDays, "总天数应已更新")
            assertEquals(newTotalDays, updatedPlan.dailySchedule.size, "DayPlan数量应与总天数匹配")
            
            // 验证保留的DayPlan数据完整性
            (1..newTotalDays).forEach { dayNumber ->
                val originalDayPlan = originalPlan.dailySchedule[dayNumber]
                val updatedDayPlan = updatedPlan.dailySchedule[dayNumber]
                assertNotNull(updatedDayPlan, "第${dayNumber}天应存在")
                if (originalDayPlan != null) {
                    assertEquals(originalDayPlan.isRestDay, updatedDayPlan.isRestDay, "第${dayNumber}天的状态应保持不变")
                    assertEquals(originalDayPlan.templateIds, updatedDayPlan.templateIds, "第${dayNumber}天的Template引用应保持不变")
                }
            }
            
            // 验证多余的DayPlan已被移除
            (newTotalDays + 1..originalPlan.totalDays).forEach { dayNumber ->
                val removedDayPlan = updatedPlan.dailySchedule[dayNumber]
                assertEquals(null, removedDayPlan, "第${dayNumber}天应被移除")
            }
        }
    }
    
    // ==================== DayPlan操作测试 ====================
    
    @Nested
    @DisplayName("DayPlan操作测试")
    inner class DayPlanOperationTests {
        
        @Test
        @DisplayName("更新特定天的DayPlan应该成功")
        fun `updating specific day plan should succeed`() {
            // Given
            val originalPlan = PlanTestDataFactory.createSimplePlan()
            val originalJsonString = originalPlan.toJson()
            val targetDay = 2
            val newDayPlan = DayPlan(
                dayNumber = targetDay,
                templateIds = listOf(PlanTestDataFactory.TEST_TEMPLATE_ID_3),
                templateVersionIds = listOf(PlanTestDataFactory.TEST_TEMPLATE_VERSION_ID_3),
                isRestDay = false,
                dayNotes = "更新后的第2天训练",
                progress = PlanProgressStatus.IN_PROGRESS
            )
            
            // When
            val updatedJsonString = PlanJsonProcessor.updateDayPlan(originalJsonString, targetDay, newDayPlan)
            val updatedPlan = PlanJsonProcessor.fromJson(updatedJsonString)
            
            // Then
            assertNotNull(updatedPlan, "更新后的Plan不应为null")
            val updatedDayPlan = updatedPlan.dailySchedule[targetDay]
            assertNotNull(updatedDayPlan, "第${targetDay}天计划不应为null")
            
            assertEquals(newDayPlan.dayNumber, updatedDayPlan.dayNumber, "天数应匹配")
            assertEquals(newDayPlan.isRestDay, updatedDayPlan.isRestDay, "休息日状态应匹配")
            assertEquals(newDayPlan.templateIds, updatedDayPlan.templateIds, "Template引用应匹配")
            assertEquals(newDayPlan.templateVersionIds, updatedDayPlan.templateVersionIds, "TemplateVersion引用应匹配")
            assertEquals(newDayPlan.dayNotes, updatedDayPlan.dayNotes, "备注应匹配")
            assertEquals(newDayPlan.progress, updatedDayPlan.progress, "进度状态应匹配")
            
            // 验证其他天的计划未被修改
            originalPlan.dailySchedule.forEach { (dayNumber, originalDayPlan) ->
                if (dayNumber != targetDay) {
                    val unchangedDayPlan = updatedPlan.dailySchedule[dayNumber]
                    assertEquals(originalDayPlan, unchangedDayPlan, "第${dayNumber}天计划应保持不变")
                }
            }
        }
        
        @Test
        @DisplayName("向训练日添加Template应该成功")
        fun `adding template to workout day should succeed`() {
            // Given
            val originalPlan = PlanTestDataFactory.createSimplePlan()
            val originalJsonString = originalPlan.toJson()
            val targetDay = 1
            val newTemplateId = "new_template_001"
            
            // When
            val updatedJsonString = PlanJsonProcessor.addTemplateToDay(originalJsonString, targetDay, newTemplateId)
            val updatedPlan = PlanJsonProcessor.fromJson(updatedJsonString)
            
            // Then
            assertNotNull(updatedPlan, "更新后的Plan不应为null")
            val updatedDayPlan = updatedPlan.dailySchedule[targetDay]
            assertNotNull(updatedDayPlan, "第${targetDay}天计划不应为null")
            
            assertTrue(updatedDayPlan.templateIds.contains(newTemplateId), "应包含新添加的Template")
            assertNotEquals(
                originalPlan.dailySchedule[targetDay]?.templateIds?.size,
                updatedDayPlan.templateIds.size,
                "Template数量应增加"
            )
            assertEquals(false, updatedDayPlan.isRestDay, "添加Template后不应为休息日")
        }
        
        @Test
        @DisplayName("向休息日添加Template应该转换为训练日")
        fun `adding template to rest day should convert to workout day`() {
            // Given
            val originalPlan = PlanTestDataFactory.createSimplePlan()
            val originalJsonString = originalPlan.toJson()
            val restDay = 2 // 第2天是休息日
            val newTemplateId = "template_for_rest_day"
            
            // When
            val updatedJsonString = PlanJsonProcessor.addTemplateToDay(originalJsonString, restDay, newTemplateId)
            val updatedPlan = PlanJsonProcessor.fromJson(updatedJsonString)
            
            // Then
            assertNotNull(updatedPlan, "更新后的Plan不应为null")
            val updatedDayPlan = updatedPlan.dailySchedule[restDay]
            assertNotNull(updatedDayPlan, "第${restDay}天计划不应为null")
            
            assertEquals(false, updatedDayPlan.isRestDay, "添加Template后应转换为训练日")
            assertTrue(updatedDayPlan.templateIds.contains(newTemplateId), "应包含新添加的Template")
            assertTrue(updatedDayPlan.templateIds.size == 1, "休息日转换后应只有一个Template")
        }
        
        @Test
        @DisplayName("从训练日移除Template应该成功")
        fun `removing template from workout day should succeed`() {
            // Given
            val originalPlan = PlanTestDataFactory.createComplexPlan() // 包含多个Template
            val originalJsonString = originalPlan.toJson()
            val targetDay = 1
            val originalDayPlan = originalPlan.dailySchedule[targetDay]
            val templateToRemove = originalDayPlan?.templateIds?.first()
            
            // When
            val updatedJsonString = PlanJsonProcessor.removeTemplateFromDay(originalJsonString, targetDay, templateToRemove!!)
            val updatedPlan = PlanJsonProcessor.fromJson(updatedJsonString)
            
            // Then
            assertNotNull(updatedPlan, "更新后的Plan不应为null")
            val updatedDayPlan = updatedPlan.dailySchedule[targetDay]
            assertNotNull(updatedDayPlan, "第${targetDay}天计划不应为null")
            
            assertTrue(!updatedDayPlan.templateIds.contains(templateToRemove), "被移除的Template不应存在")
            assertTrue(updatedDayPlan.templateIds.size < originalDayPlan.templateIds.size, "Template数量应减少")
        }
        
        @Test
        @DisplayName("移除最后一个Template应该转换为休息日")
        fun `removing last template should convert to rest day`() {
            // Given
            val simplePlan = PlanTestDataFactory.createSimplePlan()
            val originalJsonString = simplePlan.toJson()
            val targetDay = 1
            val originalDayPlan = simplePlan.dailySchedule[targetDay]
            val onlyTemplate = originalDayPlan?.templateIds?.first()
            
            // When
            val updatedJsonString = PlanJsonProcessor.removeTemplateFromDay(originalJsonString, targetDay, onlyTemplate!!)
            val updatedPlan = PlanJsonProcessor.fromJson(updatedJsonString)
            
            // Then
            assertNotNull(updatedPlan, "更新后的Plan不应为null")
            val updatedDayPlan = updatedPlan.dailySchedule[targetDay]
            assertNotNull(updatedDayPlan, "第${targetDay}天计划不应为null")
            
            assertEquals(true, updatedDayPlan.isRestDay, "移除最后一个Template后应转换为休息日")
            assertTrue(updatedDayPlan.templateIds.isEmpty(), "应没有Template引用")
        }
    }
    
    // ==================== 批量更新测试 ====================
    
    @Nested
    @DisplayName("批量更新测试")
    inner class BatchUpdateTests {
        
        @Test
        @DisplayName("批量更新Plan基础字段应该成功")
        fun `batch updating plan basic fields should succeed`() {
            // Given
            val originalPlan = PlanTestDataFactory.createSimplePlan()
            val originalJsonString = originalPlan.toJson()
            val updates = listOf(
                PlanUpdateData(PlanUpdateType.NAME, nameValue = "批量更新后的名称"),
                PlanUpdateData(PlanUpdateType.DESCRIPTION, descriptionValue = "批量更新后的描述"),
                PlanUpdateData(PlanUpdateType.TOTAL_DAYS, totalDaysValue = 5)
            )
            
            // When
            val updatedJsonString = PlanJsonProcessor.batchUpdatePlan(originalJsonString, updates)
            val updatedPlan = PlanJsonProcessor.fromJson(updatedJsonString)
            
            // Then
            assertNotNull(updatedPlan, "批量更新后的Plan不应为null")
            assertEquals("批量更新后的名称", updatedPlan.name, "名称应已更新")
            assertEquals("批量更新后的描述", updatedPlan.description, "描述应已更新")
            assertEquals(5, updatedPlan.totalDays, "总天数应已更新")
            assertEquals(5, updatedPlan.dailySchedule.size, "DayPlan数量应与总天数匹配")
        }
        
        @Test
        @DisplayName("批量更新DayPlan和Template应该成功")
        fun `batch updating day plans and templates should succeed`() {
            // Given
            val originalPlan = PlanTestDataFactory.createComplexPlan()
            val originalJsonString = originalPlan.toJson()
            val newDayPlan = DayPlan(
                dayNumber = 3,
                templateIds = listOf("batch_template_001"),
                templateVersionIds = listOf("batch_version_001"),
                isRestDay = false,
                dayNotes = "批量更新的第3天",
                progress = PlanProgressStatus.COMPLETED
            )
            val updates = listOf(
                PlanUpdateData(PlanUpdateType.DAY_PLAN, dayNumber = 3, dayPlanValue = newDayPlan),
                PlanUpdateData(PlanUpdateType.ADD_TEMPLATE, dayNumber = 1, templateIdValue = "additional_template"),
                PlanUpdateData(PlanUpdateType.REMOVE_TEMPLATE, dayNumber = 2, templateIdValue = PlanTestDataFactory.TEST_TEMPLATE_ID_3)
            )
            
            // When
            val updatedJsonString = PlanJsonProcessor.batchUpdatePlan(originalJsonString, updates)
            val updatedPlan = PlanJsonProcessor.fromJson(updatedJsonString)
            
            // Then
            assertNotNull(updatedPlan, "批量更新后的Plan不应为null")
            
            // 验证DayPlan更新
            val updatedDay3 = updatedPlan.dailySchedule[3]
            assertEquals(newDayPlan.dayNotes, updatedDay3?.dayNotes, "第3天备注应已更新")
            assertEquals(newDayPlan.progress, updatedDay3?.progress, "第3天进度应已更新")
            
            // 验证Template添加
            val updatedDay1 = updatedPlan.dailySchedule[1]
            assertTrue(updatedDay1?.templateIds?.contains("additional_template") == true, "第1天应包含新添加的Template")
            
            // 验证Template移除
            val updatedDay2 = updatedPlan.dailySchedule[2]
            assertTrue(!updatedDay2?.templateIds?.contains(PlanTestDataFactory.TEST_TEMPLATE_ID_3) == true, "第2天不应包含被移除的Template")
        }
        
        @Test
        @DisplayName("空批量更新列表应该返回原始数据")
        fun `empty batch update list should return original data`() {
            // Given
            val originalPlan = PlanTestDataFactory.createSimplePlan()
            val originalJsonString = originalPlan.toJson()
            val emptyUpdates = emptyList<PlanUpdateData>()
            
            // When
            val resultJsonString = PlanJsonProcessor.batchUpdatePlan(originalJsonString, emptyUpdates)
            
            // Then
            assertEquals(originalJsonString, resultJsonString, "空更新列表应返回原始JSON")
        }
        
        @Test
        @DisplayName("批量更新中的单个失败不应影响其他更新")
        fun `single failure in batch update should not affect other updates`() {
            // Given
            val originalPlan = PlanTestDataFactory.createSimplePlan()
            val originalJsonString = originalPlan.toJson()
            val updates = listOf(
                PlanUpdateData(PlanUpdateType.NAME, nameValue = "有效的名称更新"),
                PlanUpdateData(PlanUpdateType.DAY_PLAN, dayNumber = 999, dayPlanValue = null), // 无效更新
                PlanUpdateData(PlanUpdateType.DESCRIPTION, descriptionValue = "有效的描述更新")
            )
            
            // When
            val updatedJsonString = PlanJsonProcessor.batchUpdatePlan(originalJsonString, updates)
            val updatedPlan = PlanJsonProcessor.fromJson(updatedJsonString)
            
            // Then
            assertNotNull(updatedPlan, "批量更新应成功")
            assertEquals("有效的名称更新", updatedPlan.name, "有效的名称更新应成功")
            assertEquals("有效的描述更新", updatedPlan.description, "有效的描述更新应成功")
            // 无效的DayPlan更新应被忽略，不影响其他更新
        }
    }
    
    // ==================== 数据一致性验证测试 ====================
    
    @Nested
    @DisplayName("数据一致性验证测试")
    inner class DataConsistencyTests {
        
        @Test
        @DisplayName("多次连续更新应保持数据一致性")
        fun `multiple consecutive updates should maintain data consistency`() {
            // Given
            val originalPlan = PlanTestDataFactory.createSimplePlan()
            var jsonString = originalPlan.toJson()
            
            // When: 执行多次连续更新
            jsonString = PlanJsonProcessor.updatePlanName(jsonString, "第一次更新")
            jsonString = PlanJsonProcessor.updatePlanDescription(jsonString, "第一次描述更新")
            jsonString = PlanJsonProcessor.updatePlanTotalDays(jsonString, 4)
            jsonString = PlanJsonProcessor.addTemplateToDay(jsonString, 4, "new_template")
            jsonString = PlanJsonProcessor.updatePlanName(jsonString, "最终名称")
            
            val finalPlan = PlanJsonProcessor.fromJson(jsonString)
            
            // Then: 验证最终数据一致性
            assertNotNull(finalPlan, "最终Plan不应为null")
            assertEquals("最终名称", finalPlan.name, "最终名称应正确")
            assertEquals("第一次描述更新", finalPlan.description, "描述应正确")
            assertEquals(4, finalPlan.totalDays, "总天数应正确")
            assertEquals(4, finalPlan.dailySchedule.size, "DayPlan数量应匹配")
            
            val day4 = finalPlan.dailySchedule[4]
            assertNotNull(day4, "第4天应存在")
            assertTrue(day4.templateIds.contains("new_template"), "第4天应包含新添加的Template")
        }
        
        @Test
        @DisplayName("更新时间戳应随每次修改递增")
        fun `update timestamps should increment with each modification`() {
            // Given
            val originalPlan = PlanTestDataFactory.createSimplePlan()
            var jsonString = originalPlan.toJson()
            var previousUpdatedAt = originalPlan.updatedAt
            
            // When & Then: 每次更新都应增加时间戳
            jsonString = PlanJsonProcessor.updatePlanName(jsonString, "第一次更新")
            var updatedPlan = PlanJsonProcessor.fromJson(jsonString)
            assertNotNull(updatedPlan)
            assertTrue(updatedPlan.updatedAt > previousUpdatedAt, "第一次更新后时间戳应增加")
            previousUpdatedAt = updatedPlan.updatedAt
            
            Thread.sleep(1) // 确保时间戳差异
            jsonString = PlanJsonProcessor.updatePlanDescription(jsonString, "第二次更新")
            updatedPlan = PlanJsonProcessor.fromJson(jsonString)
            assertNotNull(updatedPlan)
            assertTrue(updatedPlan.updatedAt > previousUpdatedAt, "第二次更新后时间戳应继续增加")
            previousUpdatedAt = updatedPlan.updatedAt
            
            Thread.sleep(1) // 确保时间戳差异
            jsonString = PlanJsonProcessor.updatePlanTotalDays(jsonString, 5)
            updatedPlan = PlanJsonProcessor.fromJson(jsonString)
            assertNotNull(updatedPlan)
            assertTrue(updatedPlan.updatedAt > previousUpdatedAt, "第三次更新后时间戳应继续增加")
        }
        
        @Test
        @DisplayName("Template引用完整性应在更新后保持")
        fun `template reference integrity should be maintained after updates`() {
            // Given
            val originalPlan = PlanTestDataFactory.createComplexPlan()
            val originalJsonString = originalPlan.toJson()
            
            // When: 执行可能影响Template引用的更新
            var jsonString = PlanJsonProcessor.updatePlanTotalDays(originalJsonString, 10) // 增加天数
            jsonString = PlanJsonProcessor.addTemplateToDay(jsonString, 8, "new_template_008")
            jsonString = PlanJsonProcessor.removeTemplateFromDay(jsonString, 1, PlanTestDataFactory.TEST_TEMPLATE_ID_1)
            
            val updatedPlan = PlanJsonProcessor.fromJson(jsonString)
            
            // Then: 验证Template引用完整性
            assertNotNull(updatedPlan, "更新后的Plan不应为null")
            
            // 验证原有Template引用保持完整
            originalPlan.dailySchedule.forEach { (dayNumber, originalDayPlan) ->
                if (dayNumber != 1) { // 第1天移除了一个Template
                    val updatedDayPlan = updatedPlan.dailySchedule[dayNumber]
                    if (updatedDayPlan != null && !originalDayPlan.isRestDay) {
                        // 验证Template引用的基本完整性（除了被明确移除的）
                        originalDayPlan.templateVersionIds.forEach { templateVersionId ->
                            if (templateVersionId != PlanTestDataFactory.TEST_TEMPLATE_VERSION_ID_1 || dayNumber != 1) {
                                assertTrue(
                                    updatedDayPlan.templateVersionIds.contains(templateVersionId),
                                    "第${dayNumber}天应保持TemplateVersion引用: $templateVersionId"
                                )
                            }
                        }
                    }
                }
            }
            
            // 验证新添加的Template引用
            val day8 = updatedPlan.dailySchedule[8]
            assertNotNull(day8, "第8天应存在")
            assertTrue(day8.templateIds.contains("new_template_008"), "第8天应包含新添加的Template")
        }
    }
}