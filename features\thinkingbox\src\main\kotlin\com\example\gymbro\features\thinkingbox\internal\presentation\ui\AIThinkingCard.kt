package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.dp
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import timber.log.Timber

/**
 * 🔥 【四条铁律】shouldShowAIThinkingCard - 判断ThinkingBox是否应该显示
 * P0核心架构重构版本 - 基于segmentsQueue非空直接判断
 */
fun shouldShowAIThinkingCard(
    state: ThinkingBoxContract.State,
): Boolean {
    return state.shouldShowAIThinkingCard
}

/**
 * 🔥 【P0核心架构重构】AIThinkingCard - 单一画布架构（四条铁律）
 *
 * 🔥 【四条铁律实现】:
 * 1. UI绝对不重组刷新 - 废弃AnimatedContent，采用LazyColumn增量绘制
 * 2. 优雅的1秒30字符显示 - 由ThinkingStageCard内部UnifiedTextRenderer实现
 * 3. 思考框硬上限1/3屏高 - Modifier.heightIn(max = screenHeight / 3)
 * 4. 文本内容8行超限省略 - 由UnifiedTextRenderer实现
 *
 * 🎯 【单一画布架构】:
 * - LazyColumn作为单一画布，直接绑定到state.segmentsQueue
 * - 每个新Segment在底部增量添加ThinkingStageCard
 * - 自动滚动：新阶段出现时平滑滚动到底部
 * - 高度硬上限：根Composable应用heightIn(max = screenHeight / 3)
 *
 * @param state ThinkingBox Contract State
 * @param messageId 消息ID
 * @param modifier 修饰符
 * @param onSegmentRendered 段渲染完成回调
 */
@Composable
fun AIThinkingCard(
    state: ThinkingBoxContract.State,
    messageId: String,
    modifier: Modifier = Modifier,
    onSegmentRendered: ((String) -> Unit)? = null,
) {
    // 🔥 【四条铁律第3条】思考框硬上限1/3屏高
    val screenHeight = LocalConfiguration.current.screenHeightDp.dp
    val maxHeight = screenHeight * 0.33f
    
    // 🔥 【自动滚动管理】新阶段出现时平滑滚动到底部
    val listState = rememberLazyListState()
    
    // 🔥 【MVI规范】状态监听 - 监听关键状态变化
    LaunchedEffect(
        state.segmentsQueue.size,
        state.thinkingClosed,
        state.isStreaming(),
    ) {
        Timber.tag("TB-UI").d(
            "📊 [状态监听] 队列大小=${state.segmentsQueue.size}, " +
                "思考关闭=${state.thinkingClosed}, 流式=${state.isStreaming()}",
        )
        
        // 自动滚动到底部
        if (state.segmentsQueue.isNotEmpty()) {
            listState.animateScrollToItem(state.segmentsQueue.size - 1)
        }
    }
    
    // 段渲染完成回调增强
    val enhancedSegmentRendered: (String) -> Unit = remember(onSegmentRendered) {
        { segmentId ->
            Timber.tag("TB-RENDER").d("✅ [段渲染完成] segmentId=$segmentId")
            onSegmentRendered?.invoke(segmentId)
        }
    }
    
    when {
        // 🔥 【单一画布架构】有segments数据 -> 显示LazyColumn
        state.segmentsQueue.isNotEmpty() -> {
            Timber.tag("TB-UI").d("🎯 [显示LazyColumn] ${state.segmentsQueue.size}个段")
            
            LazyColumn(
                state = listState,
                modifier = modifier
                    .fillMaxWidth()
                    .heightIn(max = maxHeight) // 🔥 【四条铁律第3条】硬上限
            ) {
                // 🔥 【增量绘制】items直接绑定到segmentsQueue
                items(
                    items = state.segmentsQueue,
                    key = { segment -> segment.id } // 使用segment.id作为key保证稳定性
                ) { segment ->
                    ThinkingStageCard(
                        segment = segment,
                        modifier = Modifier.fillMaxWidth(),
                        onSegmentRendered = enhancedSegmentRendered,
                    )
                }
            }
        }
        // 🔥 【等待状态】未收到segments数据但正在流式传输 -> 显示等待状态
        state.isStreaming() && !state.thinkingClosed -> {
            Timber.d("TB-UI: 📱 显示等待状态: ThinkingHeader")
            com.example.gymbro.features.thinkingbox.internal.presentation.ui.ThinkingHeader(
                title = "Bro is thinking...",
                isStreaming = true,
                hasContent = false,
                modifier = modifier
                    .fillMaxWidth()
                    .heightIn(max = maxHeight) // 🔥 【四条铁律第3条】硬上限
            )
        }
        // 🔥 【隐藏状态】其他情况不显示
        else -> {
            Timber.d("TB-UI: ❌ 无显示内容: 流式=${state.isStreaming()}, 思考关闭=${state.thinkingClosed}")
        }
    }
}
