package com.example.gymbro.features.workout.plan.edit.internal.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.domain.workout.model.plan.DayPlan
import com.example.gymbro.features.workout.plan.edit.PlanEditContract
import javax.inject.Inject
import kotlin.collections.plus

/**
 * Plan Edit Reducer - 训练计划编辑状态转换器
 *
 * 🎯 核心功能：
 * - 纯函数状态转换
 * - Intent到State的映射
 * - Effect的生成和分发
 * - 数据一致性保证
 *
 * 🏗️ 架构特点：
 * - 严格的纯函数实现
 * - 无副作用的状态计算
 * - 完整的Intent处理覆盖
 * - 清晰的Effect生成逻辑
 */
class PlanEditReducer @Inject constructor() :
    Reducer<PlanEditContract.Intent, PlanEditContract.State, PlanEditContract.Effect> {

    /**
     * 状态转换主函数
     *
     * @param intent 用户意图
     * @param state 当前状态
     * @return 新状态和Effect列表
     */
    override fun reduce(
        intent: PlanEditContract.Intent,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {

        return when (intent) {
            // === 计划管理 ===
            is PlanEditContract.Intent.LoadPlan -> handleLoadPlan(intent, state)
            is PlanEditContract.Intent.SavePlan -> handleSavePlan(state)
            is PlanEditContract.Intent.CreateNewPlan -> handleCreateNewPlan(state)
            is PlanEditContract.Intent.UpdatePlanName -> handleUpdatePlanName(intent, state)

            // === 视图控制 ===
            is PlanEditContract.Intent.ToggleCalendarView -> handleToggleCalendarView(state)
            is PlanEditContract.Intent.SelectWeek -> handleSelectWeek(intent, state)
            is PlanEditContract.Intent.SelectDay -> handleSelectDay(intent, state)

            // === 日计划编辑 ===
            is PlanEditContract.Intent.UpdateDayPlan -> handleUpdateDayPlan(intent, state)
            is PlanEditContract.Intent.ToggleRestDay -> handleToggleRestDay(intent, state)
            is PlanEditContract.Intent.AddTemplateToDay -> handleAddTemplateToDay(intent, state)
            is PlanEditContract.Intent.RemoveTemplateFromDay -> handleRemoveTemplateFromDay(intent, state)

            // === 模板选择 ===
            is PlanEditContract.Intent.ShowTemplateSelector -> handleShowTemplateSelector(intent, state)
            is PlanEditContract.Intent.HideTemplateSelector -> handleHideTemplateSelector(state)
            is PlanEditContract.Intent.SelectTemplate -> handleSelectTemplate(intent, state)
            is PlanEditContract.Intent.LoadTemplates -> handleLoadTemplates(state)

            // === 错误处理 ===
            is PlanEditContract.Intent.RetryLastAction -> handleRetryLastAction(state)
            is PlanEditContract.Intent.ClearError -> handleClearError(state)

            // === 内部结果Intent ===
            is PlanEditContract.Intent.LoadPlanResult -> handleLoadPlanResult(intent, state)
            is PlanEditContract.Intent.LoadTemplatesResult -> handleLoadTemplatesResult(intent, state)
            is PlanEditContract.Intent.SavePlanResult -> handleSavePlanResult(intent, state)
            is PlanEditContract.Intent.ErrorResult -> handleErrorResult(intent, state)
        }
    }

    // === Intent处理函数 ===

    private fun handleLoadPlan(
        intent: PlanEditContract.Intent.LoadPlan,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(
                planId = intent.planId,
                isLoading = true,
                error = null
            ),
            effects = emptyList() // Effect由EffectHandler处理
        )
    }

    private fun handleSavePlan(
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return if (state.canSave) {
            ReduceResult(
                newState = state.copy(
                    isSaving = true,
                    error = null
                ),
                effects = emptyList() // Effect由EffectHandler处理
            )
        } else {
            ReduceResult(newState = state, effects = emptyList())
        }
    }

    private fun handleCreateNewPlan(
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(
                planId = "",
                planName = "新建计划",
                weekPlans = emptyMap(),
                hasUnsavedChanges = false,
                error = null
            ),
            effects = emptyList()
        )
    }

    private fun handleUpdatePlanName(
        intent: PlanEditContract.Intent.UpdatePlanName,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(
                planName = intent.name,
                hasUnsavedChanges = true
            ),
            effects = emptyList()
        )
    }

    private fun handleToggleCalendarView(
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(
                showCalendarView = !state.showCalendarView
            ),
            effects = listOf(PlanEditContract.Effect.HapticFeedback)
        )
    }

    private fun handleSelectWeek(
        intent: PlanEditContract.Intent.SelectWeek,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(
                selectedWeek = intent.week
            ),
            effects = listOf(PlanEditContract.Effect.ScrollToWeek(intent.week))
        )
    }

    private fun handleSelectDay(
        intent: PlanEditContract.Intent.SelectDay,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(
                selectedDay = intent.day
            ),
            effects = listOf(PlanEditContract.Effect.ScrollToDay(state.selectedWeek, intent.day))
        )
    }

    private fun handleUpdateDayPlan(
        intent: PlanEditContract.Intent.UpdateDayPlan,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        val updatedWeekPlans = state.weekPlans.toMutableMap()
        val weekPlans = updatedWeekPlans[intent.week]?.toMutableList()
            ?: MutableList(7) { dayIndex -> DayPlan.Companion.createWorkoutDay(dayIndex + 1, emptyList()) }

        if (intent.day in 1..7) {
            weekPlans[intent.day - 1] = intent.dayPlan
            updatedWeekPlans[intent.week] = weekPlans
        }

        return ReduceResult(
            newState = state.copy(
                weekPlans = updatedWeekPlans,
                hasUnsavedChanges = true
            ),
            effects = emptyList()
        )
    }

    private fun handleToggleRestDay(
        intent: PlanEditContract.Intent.ToggleRestDay,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        val currentDayPlan = state.getDayPlan(intent.week, intent.day)
        val updatedDayPlan = if (currentDayPlan.isRestDay) {
            DayPlan.Companion.createWorkoutDay(intent.day, emptyList())
        } else {
            DayPlan.Companion.createRestDay(intent.day)
        }

        return handleUpdateDayPlan(
            PlanEditContract.Intent.UpdateDayPlan(intent.week, intent.day, updatedDayPlan),
            state
        )
    }

    private fun handleAddTemplateToDay(
        intent: PlanEditContract.Intent.AddTemplateToDay,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        val currentDayPlan = state.getDayPlan(intent.week, intent.day)
        val updatedTemplates = currentDayPlan.templateVersionIds + intent.templateId
        val updatedDayPlan = currentDayPlan.copy(templateVersionIds = updatedTemplates)

        return handleUpdateDayPlan(
            PlanEditContract.Intent.UpdateDayPlan(intent.week, intent.day, updatedDayPlan),
            state
        )
    }

    private fun handleRemoveTemplateFromDay(
        intent: PlanEditContract.Intent.RemoveTemplateFromDay,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        val currentDayPlan = state.getDayPlan(intent.week, intent.day)
        val updatedTemplates = currentDayPlan.templateVersionIds.toMutableList()

        if (intent.templateIndex in updatedTemplates.indices) {
            updatedTemplates.removeAt(intent.templateIndex)
        }

        val updatedDayPlan = currentDayPlan.copy(templateVersionIds = updatedTemplates)

        return handleUpdateDayPlan(
            PlanEditContract.Intent.UpdateDayPlan(intent.week, intent.day, updatedDayPlan),
            state
        )
    }

    private fun handleShowTemplateSelector(
        intent: PlanEditContract.Intent.ShowTemplateSelector,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(
                showTemplateSelector = true,
                selectedWeek = intent.week,
                selectedDay = intent.day
            ),
            effects = emptyList()
        )
    }

    private fun handleHideTemplateSelector(
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(
                showTemplateSelector = false
            ),
            effects = emptyList()
        )
    }

    private fun handleSelectTemplate(
        intent: PlanEditContract.Intent.SelectTemplate,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        // 添加模板到当前选中的日期
        val addTemplateResult = handleAddTemplateToDay(
            PlanEditContract.Intent.AddTemplateToDay(
                state.selectedWeek,
                state.selectedDay,
                intent.template.id
            ),
            state
        )

        // 隐藏模板选择器
        return ReduceResult(
            newState = addTemplateResult.newState.copy(
                showTemplateSelector = false
            ),
            effects = addTemplateResult.effects + PlanEditContract.Effect.HapticFeedback
        )
    }

    private fun handleLoadTemplates(
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(isLoading = true),
            effects = emptyList() // Effect由EffectHandler处理
        )
    }

    private fun handleRetryLastAction(
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(error = null),
            effects = emptyList()
        )
    }

    private fun handleClearError(
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(error = null),
            effects = emptyList()
        )
    }

    // === 结果处理函数 ===

    private fun handleLoadPlanResult(
        intent: PlanEditContract.Intent.LoadPlanResult,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(
                planId = intent.planId,
                weekPlans = intent.weekPlans,
                isLoading = false,
                error = null
            ),
            effects = emptyList()
        )
    }

    private fun handleLoadTemplatesResult(
        intent: PlanEditContract.Intent.LoadTemplatesResult,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(
                availableTemplates = intent.templates,
                isLoading = false,
                error = null
            ),
            effects = emptyList()
        )
    }

    private fun handleSavePlanResult(
        intent: PlanEditContract.Intent.SavePlanResult,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return if (intent.success) {
            ReduceResult(
                newState = state.copy(
                    isSaving = false,
                    hasUnsavedChanges = false,
                    lastSavedTime = System.currentTimeMillis(),
                    error = null
                ),
                effects = listOf(PlanEditContract.Effect.ShowSaveSuccess)
            )
        } else {
            ReduceResult(
                newState = state.copy(
                    isSaving = false,
                    error = intent.error
                ),
                effects = intent.error?.let { listOf(PlanEditContract.Effect.ShowError(it)) } ?: emptyList()
            )
        }
    }

    private fun handleErrorResult(
        intent: PlanEditContract.Intent.ErrorResult,
        state: PlanEditContract.State
    ): ReduceResult<PlanEditContract.State, PlanEditContract.Effect> {
        return ReduceResult(
            newState = state.copy(
                isLoading = false,
                isSaving = false,
                error = intent.error
            ),
            effects = listOf(PlanEditContract.Effect.ShowError(intent.error))
        )
    }
}
