package com.example.gymbro.core.network.protocol

import com.example.gymbro.core.network.processor.ContentExtractor
import kotlinx.serialization.json.Json
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 JsonContentExtractor兼容性实现
 *
 * 临时兼容层，用于支持现有的AdaptiveStreamClient
 * 将逐步迁移到新的ContentExtractor架构
 */
@Singleton
class JsonContentExtractorCompat @Inject constructor(
    private val json: Json,
    private val contentExtractor: ContentExtractor
) {
    companion object {
        private const val TAG = "JsonContentExtractorCompat"
    }

    /**
     * 兼容旧的parseJsonContentWithContext方法
     */
    fun parseJsonContentWithContext(
        messageId: String,
        jsonContent: String
    ): ParsedContent? {
        return try {
            // 使用新的ContentExtractor提取内容
            val extractedContent = contentExtractor.extractJsonSseContent(jsonContent)

            if (extractedContent.isNotEmpty()) {
                ParsedContent(
                    content = extractedContent,
                    reasoningContent = null, // 简化实现
                    isComplete = false
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "JSON内容解析失败: messageId=$messageId")
            null
        }
    }

    /**
     * 兼容旧的extractContentStreamingFromJson方法
     */
    fun extractContentStreamingFromJson(
        sseData: String,
        messageId: String,
        onChunk: (String) -> Unit
    ): Boolean {
        return try {
            // 使用新的ContentExtractor提取内容
            val extractedContent = contentExtractor.extractJsonSseContent(sseData)

            if (extractedContent.isNotEmpty()) {
                onChunk(extractedContent)
            }

            // 简化：检查是否包含完成标记
            sseData.contains("[DONE]") || sseData.contains("\"finish_reason\"")
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "流式JSON内容提取失败: messageId=$messageId")
            false
        }
    }

    /**
     * 兼容旧的解析结果数据结构
     */
    data class ParsedContent(
        val content: String?,
        val reasoningContent: String?,
        val isComplete: Boolean
    )
}

/**
 * 🚀 XmlCharacterReassembler兼容性实现
 *
 * 临时兼容层，新架构中移除XML重组，让ThinkingBox直接处理
 */
@Singleton
class XmlCharacterReassemblerCompat @Inject constructor() {
    companion object {
        private const val TAG = "XmlCharacterReassemblerCompat"
    }

    /**
     * 兼容旧的addFragment方法
     * 新架构中直接返回原始内容，不进行重组
     */
    fun addFragment(messageId: String, fragment: String): String? {
        // 新架构：直接返回fragment，让ThinkingBox处理
        return if (fragment.isNotEmpty()) fragment else null
    }

    /**
     * 兼容旧的reassembleContent方法
     * 新架构中直接返回原始内容
     */
    fun reassembleContent(messageId: String, content: String): String {
        // 新架构：直接返回原始内容，不进行XML重组
        Timber.tag(TAG).v("🔄 兼容模式：直接返回原始内容，messageId=$messageId")
        return content
    }

    /**
     * 兼容旧的清理方法（带messageId参数）
     */
    fun cleanup(messageId: String) {
        Timber.tag(TAG).d("🧹 兼容模式：清理操作（无实际操作），messageId=$messageId")
    }

    /**
     * 兼容旧的清理方法（无参数）
     */
    fun cleanup() {
        Timber.tag(TAG).d("🧹 兼容模式：清理操作（无实际操作）")
    }
}
