
package com.example.gymbro.features.thinkingbox.domain.model.events

import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind

/**
 * ThinkingEvent - Segment队列架构事件系统
 *
 * 🎯 核心设计目标（729方案3.md）:
 * - 基于Segment队列模型的事件系统
 * - 删除双握手布尔逻辑，简化状态管理
 * - 支持三断点规则：<thinking>、</phase>、</thinking>
 * - 实现双时序架构：Token数据流 + UI渲染队列
 * - 由 DomainMapper 从 SemanticEvent 映射而来
 * - 供 ThinkingReducer 处理，更新队列状态
 */
sealed interface ThinkingEvent {

    // ===== Segment队列核心事件 =====

    /**
     * 段开始事件
     *
     * 🔥 创建新的内容段并设为当前写入段
     * 对应：<think>、<phase id="X">、</thinking>触发的final-phase
     *
     * @param id 段的唯一标识符
     * @param kind 段的类型（PERTHINK、PHASE、FINAL）
     * @param title 段的标题（可选）
     */
    data class SegmentStarted(
        val id: String,
        val kind: SegmentKind,
        val title: String? = null,
    ) : ThinkingEvent

    /**
     * 段文本事件
     *
     * 🔥 向当前段追加文本内容
     * 对应：标签内的流式文本内容
     *
     * @param text 增量文本内容
     */
    data class SegmentText(val text: String) : ThinkingEvent

    /**
     * 段闭合事件
     *
     * 🔥 标记段内容完整，将段加入渲染队列
     * 对应：</phase>、<thinking>（闭合perthink）、</thinking>（闭合final-phase）
     *
     * @param id 段的标识符
     */
    data class SegmentClosed(val id: String) : ThinkingEvent

    /**
     * UI段渲染完成事件
     *
     * 🔥 UI组件报告段渲染完成，可从队列中移除
     * 由UI层在动画完成后发送，实现队列管理
     *
     * @param id 完成渲染的段标识符
     */
    data class UiSegmentRendered(val id: String) : ThinkingEvent

    /**
     * 思考阶段结束事件
     *
     * 🔥 对应</thinking>标签，标记思考阶段完成
     * 触发思考内容的History写入和思考框关闭
     */
    object ThinkingClosed : ThinkingEvent

    // ===== Final内容处理事件 =====

    /**
     * 最终内容开始事件
     *
     * 🔥 对应<final>标签开始
     * 激活finalBuffer，开始后台累积最终富文本
     */
    object FinalStart : ThinkingEvent

    /**
     * 最终内容块事件
     *
     * 🔥 对应<final>标签内的流式文本
     * 直接追加到finalBuffer，不进入UI队列
     *
     * @param text 最终内容文本块
     */
    data class FinalContent(val text: String) : ThinkingEvent

    /**
     * 最终内容结束事件
     *
     * 🔥 对应</final>标签结束
     * 触发最终内容的History写入和对话结束
     */
    object FinalComplete : ThinkingEvent

    // ===== 错误处理事件 =====

    /**
     * 解析错误事件
     *
     * 🔥 【729方案9强化】用于上报解析错误和规范违反
     * 例如：<phase>在PRE_THINK状态下出现
     *
     * @param message 错误描述信息
     */
    data class ParseError(val message: String) : ThinkingEvent

    // ===== 已删除的双握手事件（729方案3.md要求删除）=====
    // 以下事件已被删除，不再支持：
    // - FinalRenderingReady: 双握手机制，已被队列模型替代
    // - FinalAnimationComplete: 双握手机制，已被队列模型替代
    // - PhaseAnimFinished: 双握手机制，已被UiSegmentRendered替代
    // - ShowSummaryPanel: UI交互事件，移至UI层处理
    // - SummaryAnimationComplete: 动画事件，移至UI层处理
    // - SummaryCardCollapsed: 时序协调事件，已被简化
    // - ThinkingEnd: 已被ThinkingClosed替代
    // - ThinkingBoxClosed: 双握手事件，已被Effect处理
    // - FinalRenderingComplete: 双握手事件，已被Effect处理
    // - PreThinkChunk: 已被SegmentText统一处理
    // - PreThinkStarted: 已被SegmentStarted替代
    // - ThinkingStarted: 状态事件，已简化
    // - PreThinkEnd: 已被SegmentClosed替代
    // - PhaseStart: 已被SegmentStarted替代
    // - PhaseContent: 已被SegmentText替代
    // - PhaseComplete: 已被SegmentClosed替代
}
