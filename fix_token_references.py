#!/usr/bin/env python3
"""
修复 ThinkingBox UI 文件中的 Token 引用问题
将 Tokens.Color.Dark 替换为 ColorTokens.Dark
"""

import os
import re

def fix_token_references(file_path):
    """修复单个文件中的 token 引用"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 添加 ColorTokens 导入（如果不存在）
        if 'import com.example.gymbro.designSystem.theme.tokens.ColorTokens' not in content:
            # 找到 tokens.Tokens 导入行
            tokens_import_pattern = r'import com\.example\.gymbro\.designSystem\.theme\.tokens\.Tokens'
            if re.search(tokens_import_pattern, content):
                content = re.sub(
                    tokens_import_pattern,
                    'import com.example.gymbro.designSystem.theme.tokens.Tokens\nimport com.example.gymbro.designSystem.theme.tokens.ColorTokens',
                    content
                )
        
        # 替换所有 Tokens.Color.Dark 引用为 ColorTokens.Dark
        content = re.sub(r'Tokens\.Color\.Dark\.', 'ColorTokens.Dark.', content)
        
        # 替换 Tokens.Color.Component 引用
        content = re.sub(r'Tokens\.Color\.Component\.', 'ColorTokens.Component.', content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"已修复: {file_path}")
            return True
        else:
            print(f"无需修复: {file_path}")
            return False
            
    except Exception as e:
        print(f"修复失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    # ThinkingBox UI 目录
    ui_dir = r"D:\GymBro\GymBro\features\thinkingbox\src\main\kotlin\com\example\gymbro\features\thinkingbox\internal\presentation\ui"
    
    if not os.path.exists(ui_dir):
        print(f"目录不存在: {ui_dir}")
        return
    
    fixed_count = 0
    total_count = 0
    
    # 遍历所有 .kt 文件
    for filename in os.listdir(ui_dir):
        if filename.endswith('.kt'):
            file_path = os.path.join(ui_dir, filename)
            total_count += 1
            if fix_token_references(file_path):
                fixed_count += 1
    
    print(f"\n修复完成: {fixed_count}/{total_count} 个文件已修复")

if __name__ == "__main__":
    main()