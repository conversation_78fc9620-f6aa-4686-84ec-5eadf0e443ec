package com.example.gymbro.features.thinkingbox.internal.history

import com.example.gymbro.data.coach.dao.ChatRawDao
import com.example.gymbro.data.local.entity.ChatRaw
import com.example.gymbro.domain.thinkingbox.repository.HistoryRepository
import com.example.gymbro.domain.thinkingbox.model.ThinkingFinalEntity
import com.example.gymbro.domain.thinkingbox.model.ThinkingHistoryComplete
import com.example.gymbro.domain.thinkingbox.model.ThinkingMessageEntity
import com.example.gymbro.domain.thinkingbox.model.ThinkingPhaseEntity
import com.example.gymbro.features.thinkingbox.di.ThinkingBoxScope
import com.example.gymbro.features.thinkingbox.domain.model.events.ThinkingEvent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * HistorySaver - ThinkingBox历史记录保存器
 *
 * 🔥 【架构修复】实现HistoryRepository接口，符合Clean Architecture
 * 🔥 根据大纲要求实现基于新接口定义的方法
 */
@Singleton
class HistorySaver
@Inject
constructor(
    private val chatRawDao: ChatRawDao,
    @ThinkingBoxScope
    private val coroutineScope: CoroutineScope,
) : HistoryRepository {
    private val TAG = "TB-HISTORY"

    // 🔥 当前消息状态跟踪
    private val activeMessages = mutableMapOf<String, ThinkingMessageState>()

    // 🔥 【多轮对话修复】全局事件流，用于收集来自所有实例的事件
    private val globalEventFlow = kotlinx.coroutines.flow.MutableSharedFlow<ThinkingEvent>()

    // 🔥 【多轮对话修复】确保只启动一次全局监听
    private val isGlobalListenerStarted =
        java.util.concurrent.atomic
            .AtomicBoolean(false)

    init {
        // 🔥 【多轮对话修复】在初始化时启动全局事件监听
        startGlobalEventListener()
    }

    // 🔥 【接口实现】HistoryRepository接口方法实现
    override suspend fun insertThinkingMessage(
        messageId: String,
        status: String,
        startedAt: Long,
        finishedAt: Long?,
        durationMs: Long?,
        tokenCount: Int?,
    ) {
        try {
            // 基于ChatRaw实现，这里简化实现
            Timber.tag(TAG).d("💾 插入思考消息记录: messageId=$messageId, status=$status")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "💥 插入思考消息记录异常: messageId=$messageId")
        }
    }

    override suspend fun updateThinkingMessage(
        messageId: String,
        status: String,
        finishedAt: Long,
        durationMs: Long,
        tokenCount: Int,
    ) {
        try {
            // 基于ChatRaw实现，这里简化实现
            Timber.tag(TAG).d("💾 更新思考消息记录: messageId=$messageId, status=$status")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "💥 更新思考消息记录异常: messageId=$messageId")
        }
    }

    override suspend fun insertThinkingPhase(
        messageId: String,
        phaseId: String,
        title: String,
        content: String,
        complete: Boolean,
    ) {
        try {
            // 基于ChatRaw实现，这里简化实现
            Timber.tag(TAG).d("💾 插入思考阶段记录: messageId=$messageId, phaseId=$phaseId")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "💥 插入思考阶段记录异常: messageId=$messageId")
        }
    }

    override suspend fun insertThinkingFinal(
        messageId: String,
        markdown: String,
    ) {
        try {
            val aiMessage = chatRawDao.getMessageById(messageId)
            
            if (aiMessage != null && aiMessage.role == ChatRaw.ROLE_ASSISTANT) {
                // 更新ChatRaw表的finalMarkdown字段
                val updatedMessage = aiMessage.copy(
                    finalMarkdown = markdown
                )
                chatRawDao.updateChatMessage(updatedMessage)
                Timber.tag(TAG).d("💾 保存最终内容成功: messageId=$messageId (${markdown.length} chars)")
            } else {
                Timber.tag(TAG).w("⚠️ AI消息未找到: messageId=$messageId")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "💥 保存最终内容异常: messageId=$messageId")
        }
    }

    override suspend fun getThinkingMessage(messageId: String): ThinkingMessageEntity? {
        return try {
            // 基于ChatRaw实现，这里简化实现，返回null
            null
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "💥 查询思考消息记录异常: messageId=$messageId")
            null
        }
    }

    override suspend fun getThinkingPhases(messageId: String): List<ThinkingPhaseEntity> {
        return try {
            // 基于ChatRaw实现，这里简化实现，返回空列表
            emptyList()
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "💥 查询思考阶段记录异常: messageId=$messageId")
            emptyList()
        }
    }

    override suspend fun getThinkingFinal(messageId: String): ThinkingFinalEntity? {
        return try {
            // 基于ChatRaw实现，这里简化实现，返回null
            null
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "💥 查询最终内容记录异常: messageId=$messageId")
            null
        }
    }

    override suspend fun getCompleteThinkingHistory(messageId: String): ThinkingHistoryComplete? {
        return try {
            // 基于ChatRaw实现，这里简化实现，返回null
            null
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "💥 查询完整思考历史异常: messageId=$messageId")
            null
        }
    }

    // 🔥 【兼容性】保留原有的便捷方法
    suspend fun saveThinkingProcess(
        messageId: String,
        thinkingMarkdown: String,
    ): Result<Unit> {
        return try {
            val aiMessage = chatRawDao.getMessageById(messageId)
            
            if (aiMessage != null && aiMessage.role == ChatRaw.ROLE_ASSISTANT) {
                // 更新ChatRaw表的thinkingNodes字段
                val updatedMessage = aiMessage.copy(
                    thinkingNodes = thinkingMarkdown
                )
                chatRawDao.updateChatMessage(updatedMessage)
                Timber.tag(TAG).d("💾 保存思考过程成功: messageId=$messageId (${thinkingMarkdown.length} chars)")
                Result.success(Unit)
            } else {
                val error = IllegalArgumentException("AI消息未找到: messageId=$messageId")
                Timber.tag(TAG).w("⚠️ 保存思考过程失败: ${error.message}")
                Result.failure(error)
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "💥 保存思考过程异常: messageId=$messageId")
            Result.failure(e)
        }
    }

    suspend fun saveFinalContent(
        messageId: String,
        finalMarkdown: String,
    ): Result<Unit> {
        return try {
            insertThinkingFinal(messageId, finalMarkdown)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 🔥 【多轮对话修复】启动全局事件监听器（只启动一次）
     */
    private fun startGlobalEventListener() {
        if (!isGlobalListenerStarted.compareAndSet(false, true)) {
            return // 已经启动，避免重复
        }

        coroutineScope.launch {
            globalEventFlow.collect { event ->
                try {
                    handleThinkingEvent(event)
                } catch (e: Exception) {
                    Timber.tag(TAG).e(e, "❌ 处理ThinkingEvent失败: ${event::class.simpleName}")
                }
            }
        }

        Timber.tag(TAG).i("🔥 全局ThinkingEvent监听器已启动")
    }

    /**
     * 🔥 【V2系统提升】注册V2实例事件流到全局监听器
     */
    fun registerInstanceEventFlowV2(
        instanceEventFlow: Flow<ThinkingEvent>,
        messageId: String,
    ) {
        coroutineScope.launch {
            instanceEventFlow.collect { event ->
                handleThinkingEventV2(event, messageId)
            }
        }

        Timber.tag(TAG).d("🔥 注册V2实例事件流: messageId=$messageId")
    }

    /**
     * 🔥 【V2系统提升】处理V2思考事件
     */
    private suspend fun handleThinkingEventV2(event: ThinkingEvent, messageId: String) {
        Timber.tag(TAG).d("🔥 [V2系统] 处理事件: ${event::class.simpleName}, messageId=$messageId")

        when (event) {
            is ThinkingEvent.FinalContent -> {
                // 🔥 【时序修复】禁用自动保存，等待Coach模块手动触发
                Timber.tag(TAG).d("🔥 [时序修复] 跳过V2 FinalContent自动保存，等待Coach模块手动触发")
            }
            else -> {
                // 其他V2事件暂时不需要特殊处理
                Timber.tag(TAG).v("🔥 [V2系统] 忽略事件: ${event::class.simpleName}")
            }
        }
    }

    /**
     * 🔥 【时序修复】完全禁用HistorySaver的自动保存功能
     */
    private suspend fun handleThinkingEvent(event: ThinkingEvent) {
        when (event) {
            is ThinkingEvent.FinalContent -> {
                Timber.tag(TAG).d("🔥 [V2系统] 跳过FinalContent自动保存，等待Coach模块手动触发")
            }
            else -> {
                // 其他事件暂时不需要特殊处理
                Timber.tag(TAG).v("🔥 忽略事件: ${event::class.simpleName}")
            }
        }
    }

    /**
     * 消息状态跟踪数据类
     */
    private data class ThinkingMessageState(
        val messageId: String,
        val startedAt: Long = System.currentTimeMillis(),
        var finishedAt: Long? = null,
        var status: String = "started",
        val phases: MutableList<String> = mutableListOf(),
        var finalContent: String? = null,
    )
}
