package com.example.gymbro.features.workout.template.edit.reducer

import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.features.workout.template.TemplateContract
import com.example.gymbro.features.workout.template.edit.contract.TemplateEditContract
import com.example.gymbro.features.workout.template.edit.utils.DragDropHandler
import timber.log.Timber
import javax.inject.Inject

/**
 * UI 交互处理器
 *
 * 🎯 职责：
 * - 处理 UI 交互相关的 Intent
 * - 管理对话框状态
 * - 处理导航和错误状态
 * - 处理拖拽和快速操作
 *
 * 📋 遵循标准：
 * - 单一职责原则
 * - 纯函数式编程
 * - 不可变状态管理
 */
class UIInteractionHandlers @Inject constructor() {

    // === 导航处理 ===

    fun handleNavigateBack(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.withEffect(
            state,
            TemplateEditContract.Effect.PrepareToExit,
        )
    }

    fun handleShowExerciseSelector(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.withEffect(
            state,
            TemplateEditContract.Effect.NavigateToExerciseLibrary,
        )

    fun handleResetNavigationState(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                isSaving = false,
                isCreatingVersion = false,
            ),
        )
    }

    // === 错误处理 ===

    fun handleError(
        intent: TemplateEditContract.Intent.HandleError,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                error = intent.error,
                isLoading = false,
                isSaving = false,
                isCreatingVersion = false,
                isRestoringVersion = false,
            ),
        )
    }

    fun handleClearError(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(error = null),
        )
    }

    // === 对话框管理 ===

    fun handleShowTemplateNameDialog(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        // 🔥 修复：使用状态中的 templateName 而不是 template?.name
        val currentName = state.templateName.takeIf { it.isNotBlank() } ?: "训练模版"
        Timber.d("🔧 [DEBUG-DIALOG] handleShowTemplateNameDialog 被调用")
        Timber.d("🔧 [DEBUG-DIALOG] 当前模板名称: '$currentName'")

        return ReduceResult.stateOnly(
            state.copy(
                showTemplateNameDialog = true,
                tempTemplateName = currentName,
            ),
        )
    }

    fun handleShowTemplateDescriptionDialog(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                showTemplateDescriptionDialog = true,
                tempTemplateDescription = state.template?.description ?: "",
            ),
        )
    }

    fun handleDismissDialog(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(
                showTemplateNameDialog = false,
                showTemplateDescriptionDialog = false,
                tempTemplateName = "",
                tempTemplateDescription = "",
            ),
        )
    }

    fun handleUpdateTempTemplateName(
        intent: TemplateEditContract.Intent.UpdateTempTemplateName,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(tempTemplateName = intent.name),
        )
    }

    fun handleUpdateTempTemplateDescription(
        intent: TemplateEditContract.Intent.UpdateTempTemplateDescription,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        return ReduceResult.stateOnly(
            state.copy(tempTemplateDescription = intent.description),
        )
    }

    fun handleConfirmTemplateName(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        val newName = state.tempTemplateName ?: state.template?.name ?: ""
        val newDescription = state.template?.description ?: ""

        println("🔧 [DEBUG] handleConfirmTemplateName: newName='$newName', newDescription='$newDescription'")

        return ReduceResult.withEffect(
            state.copy(
                templateName = newName,
                hasUnsavedChanges = true,
                showTemplateNameDialog = false,
                tempTemplateName = "",
            ),
            TemplateEditContract.Effect.SaveTemplateBasicInfo(newName, newDescription),
        )
    }

    fun handleConfirmTemplateDescription(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        val newName = state.template?.name ?: ""
        val newDescription = state.tempTemplateDescription.takeIf { it.isNotEmpty() } ?: state.template?.description ?: ""

        println(
            "🔧 [DEBUG] handleConfirmTemplateDescription: newName='$newName', newDescription='$newDescription'",
        )

        return ReduceResult.withEffect(
            state.copy(
                templateDescription = newDescription,
                hasUnsavedChanges = true,
                showTemplateDescriptionDialog = false,
                tempTemplateDescription = "",
            ),
            TemplateEditContract.Effect.SaveTemplateBasicInfo(newName, newDescription),
        )
    }

    // === 拖拽排序处理 ===

    fun handleStartDrag(
        intent: TemplateEditContract.Intent.StartDrag,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> {
        if (!DragDropHandler.canStartDrag(state, intent.exerciseId)) {
            return ReduceResult.noChange(state)
        }

        return ReduceResult.stateOnly(
            DragDropHandler.handleDragStart(state, intent.exerciseId, intent.startIndex),
        )
    }

    fun handleUpdateDragPosition(
        intent: TemplateEditContract.Intent.UpdateDragPosition,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.stateOnly(
            DragDropHandler.handleDragUpdate(state, intent.targetIndex, intent.offset),
        )

    fun handleCompleteDrag(
        intent: TemplateEditContract.Intent.CompleteDrag,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.stateOnly(
            DragDropHandler.handleDragComplete(state, intent.fromIndex, intent.toIndex),
        )

    fun handleCancelDrag(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.stateOnly(
            DragDropHandler.handleDragCancel(state),
        )

    // === 快速操作 ===

    fun handleShowQuickActions(
        intent: TemplateEditContract.Intent.ShowQuickActions,
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.stateOnly(
            state.copy(
                showQuickActions = true,
                quickActionTargetId = intent.exerciseId,
            ),
        )

    fun handleHideQuickActions(
        state: TemplateEditContract.State,
    ): ReduceResult<TemplateEditContract.State, TemplateEditContract.Effect> =
        ReduceResult.stateOnly(
            state.copy(
                showQuickActions = false,
                quickActionTargetId = null,
            ),
        )
}
