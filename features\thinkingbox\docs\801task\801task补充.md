


**最终行动纲领**:

1.  **执行 `801执行task1.md` 中的所有架构和职责解耦任务。** 这是我们坚实的基础。
2.  **在实现 `AIThinkingCard.kt` 时，严格遵循本规划中P0部分的“状态锁定”模式。** 这是实现“优雅展示”和避免UI刷新风暴的关键技术细节。
3.  **在Code Review阶段，我们将把“是否遵循优雅展示原则”作为最高优先级的审查项。** 任何可能导致动画中断或信息过载的实现都将被标记和修正。

感谢您的耐心指导，现在我们对最终要交付的“菜品”有了完全一致的理解。这份结合了正确架构和卓越用户体验的规划，将确保我们打造出业界顶尖的AI思考可视化模块。
好的，收到最终指示。**“UI绝对不能重组刷新”**、**“优雅的1秒30字符显示”**、**“思考框硬上限1/3屏高”** 和 **“文本内容8行超限省略”** —— 这四条是铁律，是本次实现的最终验收标准。

我完全理解并接受这些明确、具体的UI/UX约束。这意味着我们追求的不是简单的流式渲染，而是一种**高度可控、具有舞台剧般节奏感**的优雅呈现。之前方案中的`AnimatedContent`切换阶段的模式将被废弃，因为它本质上是一种内容替换，属于您定义的“重组刷新”。

以下是根据这四条铁律制定的最终、不可更改的执行计划。

---

### **最终权威执行计划 (v-Final)**

**核心设计理念：单一画布，增量绘制，严格约束。**

我们将把 `AIThinkingCard` 想象成一个永不重置的画布。新的思考阶段不是“替换”旧的，而是在旧的完成之后，“追加”到画布上，同时画布会自动向上滚动，将焦点引导到新的内容上。

---

#### **P0: 核心UI架构与逻辑重构**

**目标**: 彻底消除任何形式的“重组刷新”，实现一个平滑、连续、增量式的UI呈现流程。

1.  **废弃 `AnimatedContent`，采用 `LazyColumn` 作为单一画布**
    *   **文件**: `features/thinkingbox/internal/presentation/ui/AIThinkingCard.kt`
    *   **操作**:
        *   `AIThinkingCard` 的核心将是一个 `LazyColumn`。
        *   `LazyColumn` 的 `items` 将直接绑定到 `state.segmentsQueue`。
        *   当新的 `Segment` 到达 `segmentsQueue` 时，`LazyColumn` 会**增量地**在列表底部添加一个新的 `ThinkingStageCard` Composable，而不是刷新整个 `AIThinkingCard`。

2.  **实现 `ThinkingStageCard` 的独立生命周期与优雅渲染**
    *   **文件**: `features/thinkingbox/internal/presentation/ui/ThinkingStageCard.kt`
    *   **操作**:
        *   **独立渲染**: 每个 `ThinkingStageCard` 将管理自己的渲染生命周期。它在被添加到 `LazyColumn` 时开始渲染。
        *   **精确的打字机**: 内部使用 `UnifiedTextRenderer` (或类似实现)，严格遵守 **每秒30字符** 的速率（大约每字符 `33ms` 的延迟）。
        *   **8行截断**: `ThinkingStageCard` 在渲染文本时，需要实时计算行数。一旦超过8行，立即停止打字机效果，并在末尾追加“...”。
        *   **UI握手**: 在打字机效果 **完成**（无论是正常结束还是因8行限制而截断）后，调用 `onSegmentRendered(segment.id)`。

3.  **实现思考框高度硬上限与滚动**
    *   **文件**: `features/thinkingbox/internal/presentation/ui/AIThinkingCard.kt`
    *   **操作**:
        *   `AIThinkingCard` 的根布局 (通常是 `Card` 或 `Surface`) 必须使用 `Modifier.heightIn(max = screenHeight / 3)` 来强制执行高度上限。
        *   内部的 `LazyColumn` 将自然地处理滚动。
        *   **自动滚动**: 实现一个 `AutoScrollManager`，在新的 `ThinkingStageCard` 被添加并开始渲染时，自动将 `LazyColumn` 平滑地滚动到底部，确保用户焦点始终在最新的内容上。

#### **P1: `Reducer` 与 `State` 的适配**

**目标**: 确保 `State` 和 `Reducer` 的设计能够支撑这种增量式的UI架构。

1.  **`ThinkingBoxContract.State` - 简化为数据容器**
    *   **文件**: `features/thinkingbox/internal/contract/ThinkingBoxContract.kt`
    *   **操作**: `State` 变得更简单。它的核心就是 `segmentsQueue: List<SegmentUi>`。不再需要复杂的 `shouldShow...` 计算属性，因为UI的逻辑变得更直接。
        *   `AIThinkingCard` 的可见性可以直接判断 `state.segmentsQueue.isNotEmpty()`。
        *   最终内容的显示则是在 `ThinkingBox` 的主UI布局中，当 `thinkingClosed` 标志位为 `true` 之后进行协调。

2.  **`SegmentQueueReducer.kt` - 队列的忠实管理者**
    *   **文件**: `features/thinkingbox/internal/reducer/SegmentQueueReducer.kt`
    *   **操作**:
        *   当 `SegmentClosed` 事件到达，将完整的 `Segment` 添加到 `segmentsQueue` 列表的末尾。
        *   当 `UiSegmentRendered` 事件到达，**不再是移除**，而是**更新** `segmentsQueue` 中对应`Segment`的状态。例如，可以添加一个 `isRendered: Boolean` 标志位。
            ```kotlin
            // In SegmentUi
            data class SegmentUi(..., val isRendered: Boolean = false)

            // In Reducer
            val updatedQueue = state.segmentsQueue.map {
                if (it.id == event.segmentId) it.copy(isRendered = true) else it
            }
            ```
        *   **为什么是更新而不是移除？** 因为 `LazyColumn` 需要这个列表来保持所有已显示的阶段。移除会导致UI元素消失。

#### **P2: 流程图与最终实施清单**

**目标**: 锁定最终的实现细节，确保团队理解一致。

1.  **最终UI呈现流程图 (增量绘制版)**

    ```mermaid
    sequenceDiagram
        participant Reducer as [Reducer]
        participant State as [State (segmentsQueue)]
        participant LazyColumn as [LazyColumn in AIThinkingCard]
        participant StageCard1 as [ThinkingStageCard (Seg1)]
        participant StageCard2 as [ThinkingStageCard (Seg2)]

        Reducer->>State: Add Segment1 to queue
        State-->>LazyColumn: New item detected
        LazyColumn->>+StageCard1: Create & Compose
        StageCard1->>StageCard1: Start 33ms/char typewriter animation
        LazyColumn->>LazyColumn: Auto-scroll to StageCard1

        Note right of StageCard1: Animation in progress...

        Reducer->>State: Add Segment2 to queue
        State-->>LazyColumn: New item detected
        LazyColumn->>+StageCard2: Create & Compose (below StageCard1)
        StageCard2->>StageCard2: Start 33ms/char typewriter animation
        LazyColumn->>LazyColumn: Auto-scroll to StageCard2

        StageCard1-->>Reducer: onSegmentRendered(Seg1) (Animation done)
        Reducer->>State: Mark Segment1 as isRendered=true

        Note right of StageCard2: Animation in progress...

        StageCard2-->>Reducer: onSegmentRendered(Seg2)
        Reducer->>State: Mark Segment2 as isRendered=true
    ```

2.  **最终权威实施清单 (v-Final)**

    *   **[Contract]**: `ThinkingBoxContract.State` 以 `segmentsQueue: List<SegmentUi>` 为核心。`SegmentUi` 中添加 `isRendered: Boolean` 标志。
    *   **[UI-Core]**: **废弃 `AnimatedContent`**。`AIThinkingCard` 内部**必须**使用 `LazyColumn`，并将其 `items` 绑定到 `state.segmentsQueue`。
    *   **[UI-Height]**: `AIThinkingCard` 的根 Composable **必须** 应用 `Modifier.heightIn(max = screenHeight / 3)`。
    *   **[UI-Render]**: `ThinkingStageCard` **必须** 实现独立的渲染生命周期，严格遵守**每秒30字符**的渲染速度和**8行截断**规则。
    *   **[UI-Handshake]**: `ThinkingStageCard` 在其动画（或截断）**完成时**，**必须** 调用 `onSegmentRendered(segmentId)`。
    *   **[UI-Scroll]**: `AIThinkingCard` **必须** 实现自动滚动，在新阶段出现时，将视图滚动至列表底部。
    *   **[Reducer]**: `UiSegmentRendered` 事件**必须**将 `segmentsQueue` 中对应项的 `isRendered` 标志更新为 `true`，**而不是移除**。
    *   **[Cleanup]**: 所有与“替换式”UI（如`AnimatedContent`切换`Segment`）相关的逻辑和状态都将被**彻底删除**。
    *   **[Testing]**: **必须** 编写UI测试 (`ComposeTestRule`) 来验证：
        *   `LazyColumn` 中 `itemCount` 与 `segmentsQueue.size` 一致。
        *   新 `Segment` 到达时，`LazyColumn` 会滚动到底部。
        *   `ThinkingStageCard` 中的文本超过8行时，会显示省略号。

这套规划将确保我们交付的`ThinkingBox`不仅在架构上清晰、健壮，更在用户体验上达到您所要求的“优雅呈现”的极高标准。这是一个明确、无歧义的最终执行方案。
