package com.example.gymbro.features.workout.plan

import androidx.compose.runtime.Immutable
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.domain.workout.model.WorkoutPlan
import com.example.gymbro.domain.workout.model.calendar.WeeklyStats
import com.example.gymbro.domain.workout.model.plan.DayPlan
import com.example.gymbro.domain.workout.model.stats.DailyStats
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.features.workout.calendar.internal.CalendarContract
import com.example.gymbro.features.workout.plan.edit.internal.components.canvas.model.PlanCanvasData
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.datetime.LocalDate

/**
 * Plan模块MVI契约
 *
 * 🎯 核心功能：支持Plan层的三Tab设计和calendar.json输出
 * 基于08_Plan层改造设计.md的要求：
 * - 三Tab设计：全部、收藏、AI生成
 * - 去掉搜索功能，只保留分类
 * - 支持calendar.json输出功能
 * - 遵循Function Call实现模式
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (Plan层改造设计)
 */
object PlanContract {

    /**
     * Plan模块状态
     */
    @Immutable
    data class State(
        // === 基础数据状态 ===
        val isLoading: Boolean = false,
        val isRefreshing: Boolean = false,
        val error: UiText? = null,
        val isEmpty: Boolean = false,

        // === 当前计划数据 ===
        val plan: WorkoutPlan? = null,

        // === 模板预览状态（新增）===
        val previewingTemplate: WorkoutTemplate? = null,

        // === 三Tab数据状态 ===
        val currentTab: PlanTab = PlanTab.ALL,
        val allPlans: ImmutableList<WorkoutPlan> = persistentListOf(),
        val favoritePlans: ImmutableList<WorkoutPlan> = persistentListOf(),
        val aiGeneratedPlans: ImmutableList<WorkoutPlan> = persistentListOf(),

        // === 当前显示的计划列表 ===
        val currentPlans: ImmutableList<WorkoutPlan> = persistentListOf(),
        val filteredPlans: ImmutableList<WorkoutPlan> = persistentListOf(),

        // === 搜索和筛选状态 ===
        val isSearching: Boolean = false,
        val isSearchVisible: Boolean = false,
        val searchQuery: String = "",
        val selectedFilter: PlanFilter = PlanFilter.ALL,
        val selectedFilters: Set<PlanFilter> = emptySet(),

        // === 交互状态 ===
        val selectedPlanId: String? = null,
        val selectedPlan: WorkoutPlan? = null,
        val isDragging: Boolean = false,
        val draggedItemId: String? = null,
        val selectedDayForTemplate: Int? = null, // 新增：当前选择的天数（用于模板选择器）

        // === 4周导航状态 ===
        val currentWeek: Int = 1, // 当前显示的周数 (1-4)
        val weeklySchedules: Map<Int, Map<Int, DayPlan>> = (1..4).associateWith { emptyMap() }, // 4周的日程安排
        // === 拖拽重排序状态 ===
        val isDraggingTemplate: Boolean = false,
        val draggedTemplateId: String? = null,
        val dropTargetDay: Int? = null, // 拖拽目标日期 (1-7)
        val dropTargetWeek: Int? = null, // 拖拽目标周数 (1-4)
        val isDraggingDayPlan: Boolean = false,
        val draggedDayPlan: Pair<Int, Int>? = null, // (week, day) 被拖拽的日程

        // === TemplateDraft拖拽状态（新增）===
        val isDraggingTemplateDraft: Boolean = false,
        val draggedTemplateDraftId: String? = null,
        // === 操作状态 ===
        val isDeleting: Boolean = false,
        val isSaving: Boolean = false,
        val isApplyingPlan: Boolean = false,
        val isGeneratingCalendarJson: Boolean = false,
        val isTogglingFavorite: Boolean = false,

        // === 对话框状态 ===
        val showDeleteConfirmDialog: Boolean = false,
        val showPlanDetailDialog: Boolean = false,
        val showApplyPlanDialog: Boolean = false,
        val showGeneratePlanDialog: Boolean = false,
        // 🔥 showTemplatePickerDialog已移除 - 使用内置FloatingTemplateSelector
        // === 模板相关状态 ===
        val availableTemplates: ImmutableList<WorkoutTemplate> = persistentListOf(),

        // === TemplateDraft草稿数据状态（新增）===
        val availableTemplateDrafts: ImmutableList<TemplateDraft> = persistentListOf(),
        val isLoadingDrafts: Boolean = false,
        val draftsError: UiText? = null,

        // === 浮动模板选择器状态 ===
        val isTemplatePickerExpanded: Boolean = true,
        val templatePickerPage: Int = 0,
        val draftTemplatePickerExpanded: Boolean = false, // 草稿选择器展开状态

        // === 日历视图状态 ===
        val showCalendarView: Boolean = false,
        val calendarEntries: Map<LocalDate, CalendarContract.CalendarEntry> = emptyMap(),
        val selectedCalendarDate: LocalDate? = null,
        val totalCompletedWeight: Double = 0.0,
        val completedTrainingDays: Int = 0,
        val totalTrainingDays: Int = 0,
        val templatePickerMaxPages: Int = 5,
        val templatesPerPage: Int = 6,
        // === AI生成相关状态 ===
        val isGeneratingPlan: Boolean = false,
        val generationPrompt: String = "",
        val lastGeneratedPlan: WorkoutPlan? = null,

        // === Calendar JSON输出状态 ===
        val selectedDate: LocalDate? = null,
        val lastGeneratedCalendarJson: com.example.gymbro.shared.models.workout.PlanCalendarData? = null,

        // === Stats数据状态（新增）===
        val dailyStats: ImmutableList<DailyStats> = persistentListOf(),
        val weeklyStats: WeeklyStats = WeeklyStats(),
        val currentWeekStats: WeeklyStats = WeeklyStats(),
        val isLoadingStats: Boolean = false,
        val statsError: UiText? = null,

        // === 进度追踪状态（基于stats数据）===
        val progressByDate: Map<LocalDate, DailyStats> = emptyMap(),
        val weekProgressSummary: Map<Int, WeeklyStats> = emptyMap(), // 按周索引的进度汇总

        // === 画布模式状态（新增）===
        val isCanvasMode: Boolean = false, // 是否启用画布模式
        val canvasData: PlanCanvasData? = null, // 画布数据
        val canvasInitialized: Boolean = false, // 画布是否已初始化
        val canvasSyncInProgress: Boolean = false, // 画布同步进行中
    ) : UiState

    /**
     * Plan模块意图
     */
    sealed class Intent : AppIntent {
        // === 基础数据操作 ===
        object LoadPlans : Intent()
        object RefreshPlans : Intent()

        // === 搜索和筛选操作 ===
        data class SearchPlans(val query: String) : Intent()
        data class FilterPlans(val filter: PlanFilter) : Intent()
        object ClearSearch : Intent()
        object ToggleSearch : Intent()
        data class UpdateSearchQuery(val query: String) : Intent()
        data class RemoveFilter(val filter: PlanFilter) : Intent()
        object ClearAllFilters : Intent()
        object ShowFilterDialog : Intent() // 🆕 显示筛选对话框

        // === 三Tab切换操作 ===
        data class SwitchTab(val tab: PlanTab) : Intent()
        object LoadAllPlans : Intent()
        object LoadFavoritePlans : Intent()
        object LoadAIGeneratedPlans : Intent()

        // === 计划分类操作 ===
        data class TogglePlanFavorite(val planId: String) : Intent()
        data class MarkPlanAsAIGenerated(val planId: String) : Intent()

        // === 计划基础操作 ===
        data class SelectPlan(val planId: String) : Intent()
        data class ShowPlanDetail(val plan: WorkoutPlan) : Intent()
        data class DeletePlan(val planId: String) : Intent()
        data class ConfirmDeletePlan(val planId: String) : Intent()
        data class DuplicatePlan(val planId: String) : Intent()

        // === Stats数据操作Intent（新增）===
        object LoadDailyStats : Intent()
        object LoadWeeklyStats : Intent()
        data class LoadStatsForPlan(val planId: String) : Intent()
        data class LoadStatsForDateRange(val startDate: LocalDate, val endDate: LocalDate) : Intent()

        // === Stats数据结果Intent ===
        data class DailyStatsLoaded(val stats: ImmutableList<DailyStats>) : Intent()
        data class WeeklyStatsLoaded(val stats: WeeklyStats) : Intent()
        data class StatsLoadFailed(val error: UiText) : Intent()

        // === 进度查询Intent（基于stats数据）===
        data class GetProgressForDate(val date: LocalDate) : Intent()
        data class GetWeekProgress(val weekNumber: Int) : Intent()

        // === 进度操作Intent（新增）===
        data class ToggleDayCompleted(val date: LocalDate, val planId: String) : Intent()
        data class ProgressUpdatedResult(
            val date: LocalDate,
            val isCompleted: Boolean,
            val updatedStats: DailyStats?,
        ) : Intent()

        // === 复制周功能Intent（新增）===
        data class CopyWeek(val fromWeek: Int, val toWeek: Int) : Intent()
        data class CopyWeekCompleted(val fromWeek: Int, val toWeek: Int) : Intent() // 🆕 复制周完成回调

        // === 模板预览Intent（新增）===
        data class ShowTemplatePreview(val template: WorkoutTemplate) : Intent()
        object HideTemplatePreview : Intent()

        // === 计划保存操作 ===
        object SavePlan : Intent()

        data class PlanSaved(
            val planId: String,
        ) : Intent()

        data class PlanSaveFailed(
            val error: UiText,
        ) : Intent()

        // === 周和日程管理操作 ===
        data class SelectWeek(val week: Int) : Intent()
        data class UpdateDayPlan(
            val week: Int,
            val day: Int,
            val dayPlan: com.example.gymbro.domain.workout.model.plan.DayPlan?
        ) : Intent()

        // === 模板拖拽操作 ===
        data class StartTemplateDrag(val templateId: String) : Intent()
        object EndTemplateDrag : Intent()

        // === 拖拽排序操作 ===
        data class StartDragging(val planId: String) : Intent()
        object StopDragging : Intent()
        data class ReorderPlans(val fromIndex: Int, val toIndex: Int) : Intent()

        // === 计划应用和Calendar JSON输出 ===
        data class ShowApplyPlanDialog(val plan: WorkoutPlan) : Intent()
        data class ApplyPlanToCalendar(val planId: String, val startDate: LocalDate) : Intent()
        data class GenerateCalendarJson(val planId: String, val startDate: LocalDate) : Intent()
        data class StartPlanExecution(val planId: String) : Intent()

        // === 日历集成增强功能 ===
        data class ExportPlanToCalendar(
            val planId: String,
            val startDate: LocalDate,
        ) : Intent()

        data class GetPlanCalendarSummary(val planId: String) : Intent()

        data class ImportMultiplePlansToCalendar(
            val planIds: List<String>,
            val startDates: List<LocalDate>,
        ) : Intent()

        // === 日历集成结果 Intent ===
        data class CalendarEntriesGenerated(
            val entries: List<com.example.gymbro.domain.workout.usecase.plan.PlanCalendarEntry>,
        ) : Intent()

        data class CalendarSummaryGenerated(
            val summary: com.example.gymbro.domain.workout.usecase.plan.PlanCalendarSummary,
        ) : Intent()

        data class MultipleCalendarEntriesGenerated(
            val entries: List<com.example.gymbro.domain.workout.usecase.plan.PlanCalendarEntry>,
            val successCount: Int,
            val totalCount: Int,
        ) : Intent()

        // === 模板相关 ===
        // 🔥 ShowTemplatePicker已移除 - 使用内置FloatingTemplateSelector
        data class CreatePlanFromTemplate(val templateId: String) : Intent()

        // === TemplateDraft草稿相关Intent（新增）===
        object LoadTemplateDrafts : Intent()
        object RefreshTemplateDrafts : Intent()
        data class CreatePlanFromTemplateDraft(val draftId: String) : Intent()
        data class ShowTemplateDraftPreview(val draft: TemplateDraft) : Intent()
        object HideTemplateDraftPreview : Intent()
        object ToggleDraftTemplatePicker : Intent() // 切换草稿/模板选择器

        // === 浮动模板选择器操作 ===
        object ToggleTemplatePicker : Intent()

        data class ChangeTemplatePage(
            val page: Int,
        ) : Intent()

        object StopTemplateDrag : Intent()

        data class DropTemplateOnDay(
            val templateId: String,
            val dayNumber: Int,
        ) : Intent()

        // === TemplateDraft拖拽操作Intent（新增）===
        data class StartTemplateDraftDrag(
            val draftId: String,
        ) : Intent()

        object StopTemplateDraftDrag : Intent()

        data class DropTemplateDraftOnDay(
            val draftId: String,
            val dayNumber: Int,
        ) : Intent()

        // === 4周导航操作 ===
        data class SwitchToWeek(
            val weekNumber: Int,
        ) : Intent() // 切换到指定周 (1-4)

        // === 拖拽重排序操作 ===
        data class StartTemplateDragToWeek(
            val templateId: String,
            val week: Int,
            val day: Int,
        ) : Intent()

        data class UpdateTemplateDragPosition(
            val week: Int,
            val day: Int,
        ) : Intent()

        data class DropTemplateOnWeekDay(
            val templateId: String,
            val week: Int,
            val day: Int,
        ) : Intent()

        // === TemplateDraft跨周拖拽操作Intent（新增）===
        data class StartTemplateDraftDragToWeek(
            val draftId: String,
            val week: Int,
            val day: Int,
        ) : Intent()

        data class UpdateTemplateDraftDragPosition(
            val week: Int,
            val day: Int,
        ) : Intent()

        data class DropTemplateDraftOnWeekDay(
            val draftId: String,
            val week: Int,
            val day: Int,
        ) : Intent()

        data class StartDayPlanDrag(
            val week: Int,
            val day: Int,
        ) : Intent()

        data class UpdateDayPlanDragPosition(
            val targetWeek: Int,
            val targetDay: Int,
        ) : Intent()

        data class DropDayPlan(
            val fromWeek: Int,
            val fromDay: Int,
            val toWeek: Int,
            val toDay: Int,
        ) : Intent()

        object CancelDrag : Intent()

        // === 日历视图操作 ===
        object ToggleCalendarView : Intent()
        data class SelectCalendarDate(val date: LocalDate) : Intent()
        data class ShowCalendarEntryDetail(val entry: CalendarContract.CalendarEntry) : Intent()
        object LoadCalendarData : Intent()
        object RefreshCalendarData : Intent()

        // === AI快速生成计划 ===
        object ShowGeneratePlanDialog : Intent()
        data class GenerateQuickPlan(val prompt: String) : Intent()
        data class PlanGenerationCompleted(val plan: WorkoutPlan) : Intent()
        data class PlanGenerationFailed(val error: UiText) : Intent()

        // === 数据操作结果Intent（EffectHandler -> Reducer） ===

        // === 计划加载结果 ===
        data class PlansLoaded(val plans: List<WorkoutPlan>) : Intent()
        data class AllPlansLoaded(val plans: List<WorkoutPlan>) : Intent()
        data class FavoritePlansLoaded(val plans: List<WorkoutPlan>) : Intent()
        data class AIGeneratedPlansLoaded(val plans: List<WorkoutPlan>) : Intent()

        data class PlansLoadingFailed(
            val error: UiText,
        ) : Intent()

        // === 计划选择结果 ===
        data class PlanSelected(
            val plan: WorkoutPlan,
        ) : Intent()

        data class PlanSelectionFailed(
            val planId: String,
            val error: UiText,
        ) : Intent()

        // === 新增的Intent ===
        object AnimateReorder : Intent()
        data class ExportCalendarJson(val planId: String) : Intent()
        data class ShareCalendarJson(val json: String) : Intent()
        data class ShowCalendarJsonGenerated(val json: String) : Intent()
        object StartDragMode : Intent()
        object StopDragMode : Intent()

        // === 计划分类操作结果 ===
        data class PlanFavoriteToggled(val planId: String, val isFavorite: Boolean) : Intent()
        data class PlanFavoriteToggleFailed(val planId: String, val error: UiText) : Intent()
        data class PlanMarkedAsAIGenerated(val planId: String) : Intent()
        data class PlanAIGeneratedMarkFailed(val planId: String, val error: UiText) : Intent()

        // === 计划删除结果 ===
        data class PlanDeleted(val planId: String) : Intent()
        data class PlanDeletionFailed(val planId: String, val error: UiText) : Intent()

        // === 计划应用和Calendar JSON结果 ===
        data class PlanAppliedToCalendar(val planId: String, val startDate: LocalDate) : Intent()
        data class PlanApplicationFailed(val planId: String, val error: UiText) : Intent()
        data class CalendarJsonGenerated(
            val planId: String,
            val calendarData: com.example.gymbro.shared.models.workout.PlanCalendarData,
        ) : Intent()
        data class CalendarJsonGenerationFailed(val planId: String, val error: UiText) : Intent()

        // === 模板相关结果 ===
        data class TemplatesLoaded(val templates: List<WorkoutTemplate>) : Intent()
        data class TemplatesLoadingFailed(val error: UiText) : Intent()
        data class PlanCreatedFromTemplate(val newPlan: WorkoutPlan) : Intent()
        data class PlanCreationFromTemplateFailed(val templateId: String, val error: UiText) : Intent()

        // === TemplateDraft草稿数据结果Intent（新增）===
        data class TemplateDraftsLoaded(val drafts: List<TemplateDraft>) : Intent()
        data class TemplateDraftsLoadingFailed(val error: UiText) : Intent()
        data class PlanCreatedFromTemplateDraft(val newPlan: WorkoutPlan) : Intent()
        data class PlanCreationFromTemplateDraftFailed(val draftId: String, val error: UiText) : Intent()

        // === 计划复制结果 ===
        data class PlanDuplicated(
            val originalPlanId: String,
            val newPlan: WorkoutPlan,
        ) : Intent()

        data class PlanDuplicationFailed(
            val planId: String,
            val error: UiText,
        ) : Intent()

        // === 计划编辑相关 ===
        data class UpdatePlanName(
            val name: String,
        ) : Intent()

        data class UpdatePlanDescription(
            val description: String,
        ) : Intent()

        data class UpdateTotalDays(
            val totalDays: Int,
        ) : Intent()

        data class SelectDayForTemplate(
            val dayNumber: Int,
        ) : Intent()

        data class AddTemplateToDay(
            val dayNumber: Int,
            val templateId: String,
        ) : Intent()

        data class RemoveTemplateFromDay(
            val dayNumber: Int,
            val templateIndex: Int,
        ) : Intent()

        data class ToggleRestDay(
            val dayNumber: Int,
        ) : Intent()

        data class UpdateDayNotes(
            val dayNumber: Int,
            val notes: String,
        ) : Intent()

        data class ReorderTemplatesInDay(
            val dayNumber: Int,
            val fromIndex: Int,
            val toIndex: Int,
        ) : Intent()

        // UI操作
        object ClearError : Intent()
        object DismissDialogs : Intent()
        object DismissDeleteConfirmDialog : Intent()

        object DismissPlanDetailDialog : Intent()

        // 🔥 DismissTemplatePickerDialog已移除 - 使用内置FloatingTemplateSelector
        object DismissApplyPlanDialog : Intent()
        object DismissGeneratePlanDialog : Intent()

        // === 画布模式相关Intent（新增）===
        object EnableCanvasMode : Intent() // 启用画布模式
        object DisableCanvasMode : Intent() // 禁用画布模式
        object InitializeCanvas : Intent() // 初始化画布
        data class UpdateCanvasData(val canvasData: PlanCanvasData) : Intent() // 更新画布数据
        data class SyncCanvasToTraditional(val canvasData: PlanCanvasData) : Intent() // 将画布数据同步到传统Plan格式
        data class SyncTraditionalToCanvas(val plan: WorkoutPlan) : Intent() // 将传统Plan同步到画布格式

        // === 画布操作结果Intent ===
        data class CanvasInitialized(val canvasData: PlanCanvasData) : Intent()
        data class CanvasUpdated(val canvasData: PlanCanvasData) : Intent()
        data class CanvasSyncCompleted(val success: Boolean) : Intent()
        data class CanvasSyncFailed(val error: UiText) : Intent()
    }

    /**
     * Plan模块副作用
     */
    sealed class Effect : UiEffect {
        // === 导航效果 ===
        object NavigateBack : Effect()
        data class NavigateToPlanEditor(val planId: String? = null) : Effect()
        data class NavigateToPlanDetail(val planId: String) : Effect()
        data class NavigateToTemplateDetail(val templateId: String) : Effect()
        data class NavigateToWorkoutSession(val planId: String) : Effect()
        data class NavigateToCalendar(val planId: String, val startDate: LocalDate) : Effect()

        // === 通知效果 ===
        data class ShowToast(val message: UiText) : Effect()
        data class ShowSnackbar(val message: UiText) : Effect()
        data class ShowSuccess(val message: UiText) : Effect()
        data class ShowError(val error: UiText) : Effect()

        // === AI生成相关效果 ===
        data class ShowGenerationProgress(val message: UiText) : Effect()
        data class ShowGenerationSuccess(val planName: String) : Effect()
        object TriggerAiGeneration : Effect()

        // === Calendar JSON输出效果 ===
        data class ShowCalendarJsonGenerated(val planName: String, val entriesCount: Int) : Effect()
        data class ExportCalendarJson(
            val calendarData: com.example.gymbro.shared.models.workout.PlanCalendarData,
        ) : Effect()
        data class ShareCalendarJson(
            val planId: String,
            val calendarData: com.example.gymbro.shared.models.workout.PlanCalendarData,
        ) : Effect()

        // === 日历集成增强效果 ===
        data class CalendarEntriesGenerated(
            val entries: List<com.example.gymbro.domain.workout.usecase.plan.PlanCalendarEntry>,
        ) : Effect()

        data class CalendarSummaryGenerated(
            val summary: com.example.gymbro.domain.workout.usecase.plan.PlanCalendarSummary,
        ) : Effect()

        data class MultipleCalendarEntriesGenerated(
            val entries: List<com.example.gymbro.domain.workout.usecase.plan.PlanCalendarEntry>,
            val successCount: Int,
            val totalCount: Int,
        ) : Effect()

        // === 系统级效果 ===
        object HapticFeedback : Effect()
        data class SharePlan(val planId: String) : Effect()
        data class ExportPlan(val planId: String) : Effect()

        // === 拖拽相关效果 ===
        object StartDragMode : Effect()
        object StopDragMode : Effect()
        data class AnimateReorder(
            val fromIndex: Int,
            val toIndex: Int,
        ) : Effect()

        // === 4周导航和拖拽增强效果 ===
        object DragHapticFeedback : Effect() // 拖拽触觉反馈

        object DropHapticFeedback : Effect() // 放置触觉反馈

        data class ShowDropZoneHighlight(
            val week: Int,
            val day: Int,
        ) : Effect() // 显示放置区域高亮

        object HideDropZoneHighlight : Effect() // 隐藏放置区域高亮

        data class SaveWeeklyPlanToDatabase(
            val weeklySchedules: Map<Int, Map<Int, DayPlan>>,
        ) : Effect() // 保存4周计划到数据库

        data class WeekSwitched(val weekNumber: Int) : Effect() // 周切换完成反馈

        // === 新增的Effect ===
        data class AIGeneratedPlansLoaded(val plans: List<WorkoutPlan>) : Effect()
        data class AllPlansLoaded(val plans: List<WorkoutPlan>) : Effect()
        data class CalendarJsonGenerated(val json: String) : Effect()
        data class CalendarJsonGenerationFailed(val error: UiText) : Effect()
        data class FavoritePlansLoaded(val plans: List<WorkoutPlan>) : Effect()
        data class GenerateCalendarJson(val planId: String) : Effect()
        data class LoadAIGeneratedPlans(val dummy: Unit = Unit) : Effect()
        data class LoadPlans(val dummy: Unit = Unit) : Effect()
        data class LoadFavoritePlans(val dummy: Unit = Unit) : Effect()
        data class CreatePlan(val plan: WorkoutPlan) : Effect()
        data class UpdatePlan(val plan: WorkoutPlan) : Effect()
        data class DeletePlan(val planId: String) : Effect()
        data class DuplicatePlan(val planId: String) : Effect()
        data class ToggleFavorite(val planId: String) : Effect()
        data class GenerateAIPlan(val prompt: String) : Effect()
        data class ImportPlan(val planData: String) : Effect()
        object NavigateToAIGenerator : Effect()

        // === Stats数据加载Effects ===
        object LoadDailyStatsEffect : Effect()
        object LoadWeeklyStatsEffect : Effect()
        data class LoadStatsForPlanEffect(val planId: String) : Effect()
        data class LoadStatsForDateRangeEffect(val startDate: LocalDate, val endDate: LocalDate) : Effect()

        // === TemplateDraft草稿数据加载Effects（新增）===
        object LoadTemplateDraftsEffect : Effect()
        object RefreshTemplateDraftsEffect : Effect()
        data class CreatePlanFromTemplateDraftEffect(val draftId: String) : Effect()

        // === Stats数据更新Effects ===
        data class RefreshStatsEffect(val planId: String? = null) : Effect()
        data class UpdateProgressDisplayEffect(val dailyStats: List<DailyStats>) : Effect()

        // === 画布模式相关Effects（新增）===
        object InitializeCanvasEffect : Effect() // 初始化画布数据
        data class ConvertPlanToCanvasEffect(val plan: WorkoutPlan) : Effect() // 将Plan转换为画布数据
        data class ConvertCanvasToPlanEffect(val canvasData: PlanCanvasData) : Effect() // 将画布数据转换为Plan
        data class SyncCanvasDataEffect(val canvasData: PlanCanvasData) : Effect() // 同步画布数据到后端
        data class ValidateCanvasDataEffect(val canvasData: PlanCanvasData) : Effect() // 验证画布数据完整性
    }

    /**
     * Plan Tab类型
     *
     * 🎯 核心功能：支持三Tab设计
     * 基于用户反馈，去掉搜索功能，只保留分类
     */
    enum class PlanTab(val displayName: UiText) {
        ALL(UiText.DynamicString("全部")),
        FAVORITES(UiText.DynamicString("收藏")),
        AI_GENERATED(UiText.DynamicString("AI生成")),
        ;

        companion object {
            fun fromDisplayName(name: String): PlanTab {
                return entries.find { it.displayName.toString() == name } ?: ALL
            }
        }
    }

    /**
     * Plan筛选器类型
     */
    enum class PlanFilter(val displayName: UiText) {
        ALL(UiText.DynamicString("全部")),
        FAVORITES(UiText.DynamicString("收藏")),
        ACTIVE(UiText.DynamicString("活跃")),
        AI_GENERATED(UiText.DynamicString("AI生成")),
        COMPLETED(UiText.DynamicString("已完成")),
        DRAFT(UiText.DynamicString("草稿")),
        RECENT(UiText.DynamicString("最近使用")),
        CUSTOM(UiText.DynamicString("自定义")),
        ;

        companion object {
            fun fromDisplayName(name: String): PlanFilter {
                return entries.find { it.displayName.toString() == name } ?: ALL
            }
        }
    }

    /**
     * 拖拽状态
     */
    data class DragState(
        val isDragging: Boolean = false,
        val draggedItemId: String? = null,
        val draggedFromIndex: Int? = null,
        val draggedToIndex: Int? = null,
    )

    /**
     * Calendar JSON输出状态
     */
    data class CalendarJsonState(
        val isGenerating: Boolean = false,
        val lastGenerated: com.example.gymbro.shared.models.workout.PlanCalendarData? = null,
        val generatedAt: Long? = null,
    )
}
