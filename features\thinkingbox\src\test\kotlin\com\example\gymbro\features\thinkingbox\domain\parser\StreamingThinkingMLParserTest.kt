package com.example.gymbro.features.thinkingbox.domain.parser

import com.example.gymbro.features.thinkingbox.domain.model.events.SemanticEvent
import com.example.gymbro.features.thinkingbox.domain.model.ErrorType
import com.example.gymbro.features.thinkingbox.domain.model.ParseError
import io.mockk.*
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.test.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * StreamingThinkingMLParser 全面测试
 *
 * 🎯 覆盖内容：
 * - 合法标签解析验证
 * - 非法标签清理测试  
 * - 流式解析机制验证
 * - 错误处理和恢复测试
 */
@DisplayName("StreamingThinkingMLParser 解析器测试")
class StreamingThinkingMLParserTest {

    private val mockXmlScanner = mockk<XmlStreamScanner>(relaxed = true)
    private lateinit var parser: StreamingThinkingMLParser

    @BeforeEach
    fun setUp() {
        clearAllMocks()
        
        // 设置默认的XmlScanner mock行为
        every { mockXmlScanner.feed(any()) } returns emptyList()
        every { mockXmlScanner.hasIncompleteTag() } returns false
        every { mockXmlScanner.isBufferNearFull() } returns false
        every { mockXmlScanner.getBufferContent() } returns ""
        
        parser = StreamingThinkingMLParser(mockXmlScanner)
    }

    @Nested
    @DisplayName("合法标签解析测试")
    inner class ValidTagParsingTests {

        @Test
        @DisplayName("应该正确解析think标签流")
        fun `should parse think tags correctly in stream`() = runTest {
            // Given
            val messageId = "test-message"
            val tokens = flowOf("<think>", "预思考内容", "</think>")
            val events = mutableListOf<SemanticEvent>()
            
            // Mock XML tokens
            every { mockXmlScanner.feed("<think>") } returns listOf(
                XmlStreamScanner.TagOpen("think", emptyMap())
            )
            every { mockXmlScanner.feed("预思考内容") } returns listOf(
                XmlStreamScanner.Text("预思考内容")
            )
            every { mockXmlScanner.feed("</think>") } returns listOf(
                XmlStreamScanner.TagClose("think")
            )

            // When
            parser.parseTokenStream(messageId, tokens) { event ->
                events.add(event)
            }

            // Then
            assertTrue(events.any { it is SemanticEvent.TagOpened && it.name == "think" })
            assertTrue(events.any { it is SemanticEvent.TextChunk && it.text == "预思考内容" })
            assertTrue(events.any { it is SemanticEvent.TagClosed && it.name == "think" })
            assertTrue(events.any { it is SemanticEvent.StreamFinished })
        }

        @Test
        @DisplayName("应该正确解析thinking标签流")
        fun `should parse thinking tags correctly in stream`() = runTest {
            // Given
            val messageId = "thinking-test"
            val tokens = flowOf("<thinking>", "正式思考开始", "</thinking>")
            val events = mutableListOf<SemanticEvent>()
            
            // Mock XML tokens
            every { mockXmlScanner.feed("<thinking>") } returns listOf(
                XmlStreamScanner.TagOpen("thinking", emptyMap())
            )
            every { mockXmlScanner.feed("正式思考开始") } returns listOf(
                XmlStreamScanner.Text("正式思考开始")
            )
            every { mockXmlScanner.feed("</thinking>") } returns listOf(
                XmlStreamScanner.TagClose("thinking")
            )

            // When
            parser.parseTokenStream(messageId, tokens) { event ->
                events.add(event)
            }

            // Then
            assertTrue(events.any { it is SemanticEvent.TagOpened && it.name == "thinking" })
            assertTrue(events.any { it is SemanticEvent.TextChunk && it.text == "正式思考开始" })
            assertTrue(events.any { it is SemanticEvent.TagClosed && it.name == "thinking" })
        }

        @Test
        @DisplayName("应该正确解析带属性的phase标签")
        fun `should parse phase tags with attributes correctly`() = runTest {
            // Given
            val messageId = "phase-test"
            val tokens = flowOf("""<phase id="1">""", "分析内容", "</phase>")
            val events = mutableListOf<SemanticEvent>()
            
            // Mock XML tokens
            every { mockXmlScanner.feed("""<phase id="1">""") } returns listOf(
                XmlStreamScanner.TagOpen("phase", mapOf("id" to "1"))
            )
            every { mockXmlScanner.feed("分析内容") } returns listOf(
                XmlStreamScanner.Text("分析内容")
            )
            every { mockXmlScanner.feed("</phase>") } returns listOf(
                XmlStreamScanner.TagClose("phase")
            )

            // When
            parser.parseTokenStream(messageId, tokens) { event ->
                events.add(event)
            }

            // Then
            val phaseOpen = events.filterIsInstance<SemanticEvent.TagOpened>()
                .find { it.name == "phase" }
            assertEquals("1", phaseOpen?.attrs?.get("id"))
            assertTrue(events.any { it is SemanticEvent.TextChunk && it.text == "分析内容" })
        }
        @Test
        @DisplayName("应该正确解析final标签")
        fun `should parse final tags correctly`() = runTest {
            // Given
            val messageId = "final-test"
            val tokens = flowOf("<final>", "最终答案内容", "</final>")
            val events = mutableListOf<SemanticEvent>()
            
            // Mock XML tokens
            every { mockXmlScanner.feed("<final>") } returns listOf(
                XmlStreamScanner.TagOpen("final", emptyMap())
            )
            every { mockXmlScanner.feed("最终答案内容") } returns listOf(
                XmlStreamScanner.Text("最终答案内容")
            )
            every { mockXmlScanner.feed("</final>") } returns listOf(
                XmlStreamScanner.TagClose("final")
            )

            // When
            parser.parseTokenStream(messageId, tokens) { event ->
                events.add(event)
            }

            // Then
            assertTrue(events.any { it is SemanticEvent.TagOpened && it.name == "final" })
            assertTrue(events.any { it is SemanticEvent.FinalStart })
            assertTrue(events.any { it is SemanticEvent.TextChunk && it.text == "最终答案内容" })
            assertTrue(events.any { it is SemanticEvent.TagClosed && it.name == "final" })
            assertTrue(events.any { it is SemanticEvent.FinalEnd })
        }

        @Test
        @DisplayName("应该正确处理复杂嵌套结构")
        fun `should handle complex nested structure correctly`() = runTest {
            // Given
            val messageId = "complex-test"
            val tokens = flowOf(
                "<think>", "预思考", "</think>",
                "<thinking>",
                "<phase>", "分析阶段", "</phase>",
                "<phase>", "解决阶段", "</phase>",
                "</thinking>",
                "<final>", "最终答案", "</final>"
            )
            val events = mutableListOf<SemanticEvent>()
            
            // Mock各种XML tokens
            every { mockXmlScanner.feed("<think>") } returns listOf(XmlStreamScanner.TagOpen("think", emptyMap()))
            every { mockXmlScanner.feed("预思考") } returns listOf(XmlStreamScanner.Text("预思考"))
            every { mockXmlScanner.feed("</think>") } returns listOf(XmlStreamScanner.TagClose("think"))
            every { mockXmlScanner.feed("<thinking>") } returns listOf(XmlStreamScanner.TagOpen("thinking", emptyMap()))
            every { mockXmlScanner.feed("<phase>") } returns listOf(XmlStreamScanner.TagOpen("phase", emptyMap()))
            every { mockXmlScanner.feed("分析阶段") } returns listOf(XmlStreamScanner.Text("分析阶段"))
            every { mockXmlScanner.feed("</phase>") } returns listOf(XmlStreamScanner.TagClose("phase"))
            every { mockXmlScanner.feed("解决阶段") } returns listOf(XmlStreamScanner.Text("解决阶段"))
            every { mockXmlScanner.feed("</thinking>") } returns listOf(XmlStreamScanner.TagClose("thinking"))
            every { mockXmlScanner.feed("<final>") } returns listOf(XmlStreamScanner.TagOpen("final", emptyMap()))
            every { mockXmlScanner.feed("最终答案") } returns listOf(XmlStreamScanner.Text("最终答案"))
            every { mockXmlScanner.feed("</final>") } returns listOf(XmlStreamScanner.TagClose("final"))

            // When
            parser.parseTokenStream(messageId, tokens) { event ->
                events.add(event)
            }

            // Then - 验证所有关键事件都存在
            assertTrue(events.any { it is SemanticEvent.TagOpened && it.name == "think" })
            assertTrue(events.any { it is SemanticEvent.TagOpened && it.name == "thinking" })
            assertTrue(events.any { it is SemanticEvent.TagOpened && it.name == "phase" })
            assertTrue(events.any { it is SemanticEvent.TagClosed && it.name == "thinking" })
            assertTrue(events.any { it is SemanticEvent.TagOpened && it.name == "final" })
            assertTrue(events.any { it is SemanticEvent.FinalStart })
            assertTrue(events.any { it is SemanticEvent.FinalEnd })
            assertTrue(events.any { it is SemanticEvent.StreamFinished })
        }
    }

    @Nested
    @DisplayName("状态机转换测试")
    inner class StateMachineTransitionTests {

        @Test
        @DisplayName("PRE_THINK到THINKING状态转换")
        fun `PRE_THINK to THINKING state transition`() = runTest {
            // Given
            val messageId = "state-test"
            val tokens = flowOf("<thinking>", "内容", "</thinking>")
            val events = mutableListOf<SemanticEvent>()
            
            every { mockXmlScanner.feed("<thinking>") } returns listOf(
                XmlStreamScanner.TagOpen("thinking", emptyMap())
            )
            every { mockXmlScanner.feed("内容") } returns listOf(
                XmlStreamScanner.Text("内容")
            )
            every { mockXmlScanner.feed("</thinking>") } returns listOf(
                XmlStreamScanner.TagClose("thinking")
            )

            // When
            parser.parseTokenStream(messageId, tokens) { event ->
                events.add(event)
            }

            // Then - 应该有正确的状态转换事件
            assertTrue(events.any { it is SemanticEvent.TagOpened && it.name == "thinking" })
            assertTrue(events.any { it is SemanticEvent.TagClosed && it.name == "thinking" })
        }

        @Test
        @DisplayName("THINKING到IN_FINAL状态转换")
        fun `THINKING to IN_FINAL state transition`() = runTest {
            // Given
            val messageId = "final-state-test"
            val tokens = flowOf("<thinking>", "<final>", "最终内容", "</final>", "</thinking>")
            val events = mutableListOf<SemanticEvent>()
            
            every { mockXmlScanner.feed("<thinking>") } returns listOf(XmlStreamScanner.TagOpen("thinking", emptyMap()))
            every { mockXmlScanner.feed("<final>") } returns listOf(XmlStreamScanner.TagOpen("final", emptyMap()))
            every { mockXmlScanner.feed("最终内容") } returns listOf(XmlStreamScanner.Text("最终内容"))
            every { mockXmlScanner.feed("</final>") } returns listOf(XmlStreamScanner.TagClose("final"))
            every { mockXmlScanner.feed("</thinking>") } returns listOf(XmlStreamScanner.TagClose("thinking"))

            // When
            parser.parseTokenStream(messageId, tokens) { event ->
                events.add(event)
            }

            // Then - 应该生成FinalStart和FinalEnd事件
            assertTrue(events.any { it is SemanticEvent.FinalStart })
            assertTrue(events.any { it is SemanticEvent.FinalEnd })
            assertTrue(events.any { it is SemanticEvent.TextChunk && it.text == "最终内容" })
        }

        @Test
        @DisplayName("错误状态下的final标签处理")
        fun `handle final tag in wrong state`() = runTest {
            // Given - 在POST_FINAL状态下遇到final标签
            val messageId = "wrong-state-test"
            val tokens = flowOf("<thinking>", "</thinking>", "<final>", "内容", "</final>")
            val events = mutableListOf<SemanticEvent>()
            
            every { mockXmlScanner.feed("<thinking>") } returns listOf(XmlStreamScanner.TagOpen("thinking", emptyMap()))
            every { mockXmlScanner.feed("</thinking>") } returns listOf(XmlStreamScanner.TagClose("thinking"))
            every { mockXmlScanner.feed("<final>") } returns listOf(XmlStreamScanner.TagOpen("final", emptyMap()))
            every { mockXmlScanner.feed("内容") } returns listOf(XmlStreamScanner.Text("内容"))
            every { mockXmlScanner.feed("</final>") } returns listOf(XmlStreamScanner.TagClose("final"))

            // When
            parser.parseTokenStream(messageId, tokens) { event ->
                events.add(event)
            }

            // Then - 应该仍然生成FinalStart和FinalEnd事件（解析器级别不验证业务逻辑）
            assertTrue(events.any { it is SemanticEvent.FinalStart })
            assertTrue(events.any { it is SemanticEvent.FinalEnd })
        }
    }

    @Nested
    @DisplayName("单token解析测试")
    inner class SingleTokenParsingTests {

        @Test
        @DisplayName("parseTokenChunk应该正确处理单个Token块")
        fun `parseTokenChunk should process single token chunk correctly`() = runTest {
            // Given
            val messageId = "single-token-test"
            val tokenChunk = "<think>内容</think>"
            val events = mutableListOf<SemanticEvent>()
            
            // Mock XML tokens
            every { mockXmlScanner.feed(tokenChunk) } returns listOf(
                XmlStreamScanner.TagOpen("think", emptyMap()),
                XmlStreamScanner.Text("内容"),
                XmlStreamScanner.TagClose("think")
            )

            // When
            parser.parseTokenChunk(tokenChunk, messageId) { event ->
                events.add(event)
            }

            // Then
            assertTrue(events.any { it is SemanticEvent.TagOpened && it.name == "think" })
            assertTrue(events.any { it is SemanticEvent.TextChunk && it.text == "内容" })
            assertTrue(events.any { it is SemanticEvent.TagClosed && it.name == "think" })
        }
    }

    @Nested
    @DisplayName("错误处理测试")
    inner class ErrorHandlingTests {

        @Test
        @DisplayName("Token流错误应该被捕获并生成错误事件")
        fun `token stream errors should be caught and generate error events`() = runTest {
            // Given
            val messageId = "error-test"
            val errorFlow = flow<String> { 
                emit("valid-token")
                throw RuntimeException("Token流错误") 
            }
            val events = mutableListOf<SemanticEvent>()
            
            every { mockXmlScanner.feed("valid-token") } returns listOf(
                XmlStreamScanner.Text("valid-token")
            )

            // When
            parser.parseTokenStream(messageId, errorFlow) { event ->
                events.add(event)
            }

            // Then
            assertTrue(events.any { it is SemanticEvent.ParseErrorEvent })
            val errorEvent = events.filterIsInstance<SemanticEvent.ParseErrorEvent>().first()
            assertEquals(ErrorType.PARSING_ERROR, errorEvent.error.type)
            assertTrue(errorEvent.error.message.contains("Token流错误"))
        }

        @Test
        @DisplayName("parseTokenChunk错误应该被捕获")
        fun `parseTokenChunk errors should be caught`() = runTest {
            // Given
            val messageId = "chunk-error-test"
            val tokenChunk = "problematic-chunk"
            val events = mutableListOf<SemanticEvent>()
            
            // Mock throwing exception
            every { mockXmlScanner.feed(tokenChunk) } throws RuntimeException("解析失败")

            // When
            parser.parseTokenChunk(tokenChunk, messageId) { event ->
                events.add(event)
            }

            // Then
            assertTrue(events.any { it is SemanticEvent.ParseErrorEvent })
        }
    }

    @Nested
    @DisplayName("流控制测试")
    inner class FlowControlTests {

        @Test
        @DisplayName("flush应该处理缓冲区内容")
        fun `flush should handle buffer content`() = runTest {
            // Given
            every { mockXmlScanner.hasIncompleteTag() } returns false
            every { mockXmlScanner.isBufferNearFull() } returns false
            every { mockXmlScanner.getBufferContent() } returns "缓冲内容"

            // When
            parser.flush()

            // Then
            verify { mockXmlScanner.hasIncompleteTag() }
            verify { mockXmlScanner.getBufferContent() }
        }

        @Test
        @DisplayName("finishParsing应该发送StreamFinished事件")
        fun `finishParsing should send StreamFinished event`() = runTest {
            // Given
            val messageId = "finish-test"
            val events = mutableListOf<SemanticEvent>()

            // When
            parser.finishParsing(messageId) { event ->
                events.add(event)
            }

            // Then
            assertTrue(events.any { it is SemanticEvent.StreamFinished })
            assertEquals(1, events.size)
        }
    }

    @Nested
    @DisplayName("边界情况测试")
    inner class EdgeCaseTests {

        @Test
        @DisplayName("空Token流应该正确处理")
        fun `should handle empty token stream correctly`() = runTest {
            // Given
            val messageId = "empty-stream-test"
            val emptyTokens = emptyFlow<String>()
            val events = mutableListOf<SemanticEvent>()

            // When
            parser.parseTokenStream(messageId, emptyTokens) { event ->
                events.add(event)
            }

            // Then
            // 应该只有StreamFinished事件
            assertEquals(1, events.size)
            assertTrue(events[0] is SemanticEvent.StreamFinished)
        }

        @Test
        @DisplayName("空白Token应该被过滤")
        fun `should filter blank tokens`() = runTest {
            // Given
            val messageId = "blank-test"
            val tokens = flowOf("   ", "\n", "\t", "")
            val events = mutableListOf<SemanticEvent>()
            
            // Mock返回空白文本
            every { mockXmlScanner.feed(any()) } returns listOf(
                XmlStreamScanner.Text("   ")
            ) andThen listOf(
                XmlStreamScanner.Text("\n")
            ) andThen listOf(
                XmlStreamScanner.Text("\t")
            ) andThen listOf(
                XmlStreamScanner.Text("")
            )

            // When
            parser.parseTokenStream(messageId, tokens) { event ->
                events.add(event)
            }

            // Then
            // 应该过滤掉空白内容，只有StreamFinished
            assertTrue(events.none { it is SemanticEvent.TextChunk })
            assertTrue(events.any { it is SemanticEvent.StreamFinished })
        }
    }

    @Nested
    @DisplayName("性能测试")
    inner class PerformanceTests {

        @Test
        @DisplayName("大量Token应该高效处理")
        fun `should handle large number of tokens efficiently`() = runTest {
            // Given
            val messageId = "performance-test"
            val tokenCount = 1000
            val tokens = (1..tokenCount).map { "token$it" }.asFlow()
            val events = mutableListOf<SemanticEvent>()
            
            // Mock每个token都返回文本
            every { mockXmlScanner.feed(any()) } returns listOf(
                XmlStreamScanner.Text("content")
            )

            // When
            val startTime = System.currentTimeMillis()
            parser.parseTokenStream(messageId, tokens) { event ->
                events.add(event)
            }
            val endTime = System.currentTimeMillis()

            // Then - 应该在合理时间内完成
            assertTrue(endTime - startTime < 5000) // 5秒内
            // 应该有大量的TextChunk事件加上一个StreamFinished
            assertTrue(events.size >= tokenCount)
            assertTrue(events.any { it is SemanticEvent.StreamFinished })
        }
    }

    @Nested
    @DisplayName("XML扫描器集成测试")
    inner class XmlScannerIntegrationTests {

        @Test
        @DisplayName("XML扫描器异常应该被处理")
        fun `XML scanner exceptions should be handled`() = runTest {
            // Given
            val messageId = "scanner-error-test"
            val tokens = flowOf("problematic-token")
            val events = mutableListOf<SemanticEvent>()
            
            every { mockXmlScanner.feed("problematic-token") } throws RuntimeException("Scanner error")

            // When
            parser.parseTokenStream(messageId, tokens) { event ->
                events.add(event)
            }

            // Then
            assertTrue(events.any { it is SemanticEvent.ParseErrorEvent })
        }

        @Test
        @DisplayName("空XML token列表应该正常处理")
        fun `empty XML token list should be handled`() = runTest {
            // Given
            val messageId = "empty-xml-test"
            val tokens = flowOf("some-content")
            val events = mutableListOf<SemanticEvent>()
            
            every { mockXmlScanner.feed("some-content") } returns emptyList()

            // When
            parser.parseTokenStream(messageId, tokens) { event ->
                events.add(event)
            }

            // Then - 应该只有StreamFinished事件
            assertEquals(1, events.size)
            assertTrue(events[0] is SemanticEvent.StreamFinished)
        }

        @Test
        @DisplayName("flush方法应该正确处理缓冲区状态")
        fun `flush method should handle buffer states correctly`() = runTest {
            // Given - 有未完成标签的情况
            every { mockXmlScanner.hasIncompleteTag() } returns true
            every { mockXmlScanner.isBufferNearFull() } returns false
            every { mockXmlScanner.getBufferContent() } returns ""

            // When
            parser.flush()

            // Then
            verify { mockXmlScanner.hasIncompleteTag() }
            verify(exactly = 0) { mockXmlScanner.getBufferContent() } // 不应该读取缓冲区
        }

        @Test
        @DisplayName("flush方法应该处理缓冲区满载警告")
        fun `flush method should handle buffer full warning`() = runTest {
            // Given - 缓冲区接近满载
            every { mockXmlScanner.hasIncompleteTag() } returns false
            every { mockXmlScanner.isBufferNearFull() } returns true
            every { mockXmlScanner.getBufferContent() } returns "buffer-content"

            // When
            parser.flush()

            // Then
            verify { mockXmlScanner.isBufferNearFull() }
            verify { mockXmlScanner.getBufferContent() }
        }

        @Test
        @DisplayName("flush异常应该被捕获")
        fun `flush exceptions should be caught`() = runTest {
            // Given
            every { mockXmlScanner.hasIncompleteTag() } throws RuntimeException("Flush error")

            // When & Then - 不应该抛出异常
            kotlin.runCatching {
                parser.flush()
            }.onFailure {
                throw AssertionError("Flush exception should be caught")
            }
        }
    }

    @Nested
    @DisplayName("日志和调试支持测试")
    inner class LoggingAndDebuggingTests {

        @Test
        @DisplayName("关键标签应该被正确检测")
        fun `key tags should be detected correctly`() = runTest {
            // Given
            val messageId = "key-tags-test"
            val tokensWithKeyTags = flowOf(
                "<thinking>content</thinking>",
                "<phase>analysis</phase>",
                "<final>answer</final>"
            )
            val events = mutableListOf<SemanticEvent>()
            
            // Mock返回相应的XML tokens
            every { mockXmlScanner.feed(match { it.contains("<thinking>") }) } returns listOf(
                XmlStreamScanner.TagOpen("thinking", emptyMap()),
                XmlStreamScanner.Text("content"),
                XmlStreamScanner.TagClose("thinking")
            )
            every { mockXmlScanner.feed(match { it.contains("<phase>") }) } returns listOf(
                XmlStreamScanner.TagOpen("phase", emptyMap()),
                XmlStreamScanner.Text("analysis"),
                XmlStreamScanner.TagClose("phase")
            )
            every { mockXmlScanner.feed(match { it.contains("<final>") }) } returns listOf(
                XmlStreamScanner.TagOpen("final", emptyMap()),
                XmlStreamScanner.Text("answer"),
                XmlStreamScanner.TagClose("final")
            )

            // When
            parser.parseTokenStream(messageId, tokensWithKeyTags) { event ->
                events.add(event)
            }

            // Then - 应该生成相应的语义事件
            assertTrue(events.any { it is SemanticEvent.TagOpened && it.name == "thinking" })
            assertTrue(events.any { it is SemanticEvent.TagOpened && it.name == "phase" })
            assertTrue(events.any { it is SemanticEvent.TagOpened && it.name == "final" })
        }

        @Test
        @DisplayName("token计数应该正确维护")
        fun `token count should be maintained correctly`() = runTest {
            // Given
            val messageId = "count-test"
            val tokens = (1..15).map { "token$it" }.asFlow() // 15个tokens触发计数日志
            val events = mutableListOf<SemanticEvent>()
            
            every { mockXmlScanner.feed(any()) } returns listOf(XmlStreamScanner.Text("content"))

            // When
            parser.parseTokenStream(messageId, tokens) { event ->
                events.add(event)
            }

            // Then - 应该处理所有tokens
            assertTrue(events.size >= 15) // 至少15个TextChunk + 1个StreamFinished
        }
    }
}