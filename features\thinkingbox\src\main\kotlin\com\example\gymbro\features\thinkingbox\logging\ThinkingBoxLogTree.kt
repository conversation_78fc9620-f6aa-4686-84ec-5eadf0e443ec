package com.example.gymbro.features.thinkingbox.logging

import android.util.Log
import kotlinx.coroutines.*
import timber.log.Timber
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * ThinkingBox 专用日志树
 *
 * 实现日志瘦身，过滤低优先级的 token 级别日志
 */
class ThinkingBoxLogTree : Timber.DebugTree() {

    companion object {
        // 🔥 【统一标签规范】ThinkingBox模块使用TB-*前缀
        private val THINKING_TAGS = setOf(
            // 🔥 【核心组件标签 - 实际使用】
            "TB-Parser", // StreamingThinkingMLParser
            "TB-Guardrail", // ThinkingMLGuardrail
            "TB-Init", // ThinkingBox初始化
            "TB-Callback", // 回调调试
            "TB-Raw-Collector", // RAW Token收集
            "TB-Raw-Tokens", // 原始Token记录

            // 🔥 【流处理标签 - 实际使用】
            "ThinkingBoxStreamAdapter", // ThinkingBox流适配器
            "StreamingJob", // 流式处理作业

            // 🔥 【XML处理标签 - 实际使用】
            "TB-XML-INPUT", // XML输入
            "TB-XML-OUTPUT", // XML输出
            "TB-XML-SCANNER", // XML扫描器

            // 🔥 【MVI架构标签 - 实际使用】
            "TB-REDUCER", // SegmentQueueReducer状态管理
            "TB-QUEUE", // 段队列管理
            "TB-MAPPER", // DomainMapper事件映射
            "TB-VIEWMODEL", // ThinkingBoxViewModel

            // 🔥 【UI组件标签 - 实际使用】
            "TB-SEGMENT-UI", // UI段组件显示

            // 🔥 【域服务标签 - 实际使用】
            "TB-MERMAID", // Mermaid图表渲染
            "MermaidPerf", // Mermaid性能管理
            "TB-METRICS", // ThinkingBox指标
            "TB-FUNCTION-CALL-DETECTOR", // 函数调用检测

            // 🔥 【历史记录标签 - 实际使用】
            "HISTORY-ACTOR", // 历史记录处理

            // 🔥 【Core-Network标签 - 保持兼容】
            // 旧架构组件已删除：TokenRouter, ConversationScope
            "CNET-STREAM", // Core网络流
            "CNET-SSE", // Server-Sent Events
            // 旧架构组件已删除：CNT-TOKEN-ROUTER
            "CNT-XML-DEBUG", // XML调试
            "CNT-MESSAGE-FLOW", // 消息流调试
        )

        // 🔥 【729方案9优化】高频日志标签 - 需要特殊处理的高频标签
        private val HIGH_FREQUENCY_TAGS = setOf(
            "TB-RAW-COLLECTOR",
            "TB-XML-INPUT",
            "TB-XML-OUTPUT",
            "TB-XML-SCANNER",
            "TB-Parser", // StreamingThinkingMLParser
            "TB-Raw-Tokens", // 原始Token记录
            "CNT-TOKEN-ROUTER", // TokenRouter调试
            "ConversationScope", // ConversationScope作用域
        )

        // 🔥 【729方案9优化】高频日志计数器和限流
        private val logCounters = ConcurrentHashMap<String, AtomicLong>()
        private const val HIGH_FREQ_LOG_INTERVAL = 100L // 每100条记录一次
    }

    override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
        // 🔥 【729方案9优化】条件日志记录和高频日志过滤
        when {
            // 1. 🔥 【ERROR级别强制显示】确保关键错误可见
            priority >= Log.ERROR -> {
                super.log(priority, tag, message, t)
                return
            }

            // 2. 🔥 【729方案9优化】高频日志限流 - 仅在DEBUG模式下启用详细日志
            isHighFrequencyTag(tag) && priority <= Log.DEBUG -> {
                if (isDebugBuild() && shouldLogHighFrequency(tag)) {
                    super.log(priority, tag, "[限流] $message", t)
                }
                return
            }

            // 3. 🔥 【ThinkingBox模块日志】启用所有TB-*标签（非高频部分）
            isThinkingBoxTag(tag) && !isHighFrequencyTag(tag) -> {
                super.log(priority, tag, message, t)
                return
            }

            // 4. 🔥 【Core-Network日志】TokenRouter和ConversationScope（非高频部分）
            (tag?.equals("TokenRouter", ignoreCase = true) == true ||
                tag?.startsWith("CNET-", ignoreCase = true) == true) &&
                !isHighFrequencyTag(tag) -> {
                super.log(priority, tag, message, t)
                return
            }

            // 5. 🔥 【INFO级别ThinkingBox相关】确保重要信息可见
            priority >= Log.INFO && (tag?.startsWith("TB-", ignoreCase = true) == true ||
                                     tag?.contains("ThinkingBox", ignoreCase = true) == true) -> {
                super.log(priority, tag, message, t)
                return
            }

            // 6. 非ThinkingBox日志，只显示INFO及以上
            !isThinkingBoxTag(tag) && priority < Log.INFO -> return
        }

        // 其他日志正常输出
        super.log(priority, tag, message, t)
    }

    /**
     * 🔥 【729方案9优化】判断高频日志是否应该记录
     */
    private fun shouldLogHighFrequency(tag: String?): Boolean {
        if (tag == null) return false

        val counter = logCounters.getOrPut(tag) { AtomicLong(0) }
        val count = counter.incrementAndGet()

        // 每HIGH_FREQ_LOG_INTERVAL条记录一次
        return count % HIGH_FREQ_LOG_INTERVAL == 0L
    }

    /**
     * 检查是否为 ThinkingBox 相关标签
     */
    private fun isThinkingBoxTag(tag: String?): Boolean {
        if (tag == null) return false
        return THINKING_TAGS.any { thinkingTag ->
            tag.startsWith(thinkingTag, ignoreCase = true)
        }
    }

    /**
     * 检查是否为高频日志标签
     */
    private fun isHighFrequencyTag(tag: String?): Boolean {
        if (tag == null) return false
        return HIGH_FREQUENCY_TAGS.any { highFreqTag ->
            tag.startsWith(highFreqTag, ignoreCase = true)
        }
    }

    /**
     * 检查是否为 Token 级别的日志（需要过滤的token flow日志）
     */
    private fun isTokenLevelLog(message: String): Boolean {
        return message.contains("token", ignoreCase = true) ||
            message.contains("收到语义事件", ignoreCase = true) ||
            message.contains("发送内容", ignoreCase = true) ||
            message.contains("解析事件", ignoreCase = true) ||
            message.contains("TOKEN-FLOW", ignoreCase = true) ||
            message.contains("parseTokenStream", ignoreCase = true) ||
            message.contains("tokensSnapshot", ignoreCase = true) ||
            message.contains("处理Token", ignoreCase = true) ||
            message.contains("发送TextChunk", ignoreCase = true) ||
            message.contains("累积原始token", ignoreCase = true) ||
            message.contains("SemanticEvent", ignoreCase = true) ||
            message.contains("ThinkingEvent", ignoreCase = true) ||
            message.contains("映射并发射", ignoreCase = true) ||
            message.contains("事件处理", ignoreCase = true) ||
            message.contains("XML解析", ignoreCase = true) ||
            message.contains("标签处理", ignoreCase = true) ||
            message.contains("内容更新", ignoreCase = true) ||
            message.contains("流式处理", ignoreCase = true) ||
            // 🔥 【新增Token流修复验证关键词】
            message.contains("Starting token stream listening", ignoreCase = true) ||
            message.contains("收到第", ignoreCase = true) ||
            message.contains("个token事件", ignoreCase = true) ||
            message.contains("生成ThinkingEvent", ignoreCase = true) ||
            message.contains("ConversationScope获取成功", ignoreCase = true) ||
            message.contains("健康检查", ignoreCase = true) ||
            message.contains("修复验证", ignoreCase = true) ||
            message.contains("StartTokenStreamListening", ignoreCase = true) ||
            message.contains("handleInitialize", ignoreCase = true)
    }

    /**
     * 🔥 【729方案9优化】检查是否为 DEBUG 构建
     */
    private fun isDebugBuild(): Boolean {
        return try {
            // 🔥 【简化】暂时返回true，实际项目中应该使用BuildConfig.DEBUG
            true
        } catch (e: Exception) {
            false
        }
    }

    override fun createStackElementTag(element: StackTraceElement): String? {
        // 为 ThinkingBox 相关的类添加特殊标签前缀
        val className = element.className
        return when {
            className.contains("thinkingbox", ignoreCase = true) -> {
                "TB-${super.createStackElementTag(element)}"
            }
            else -> super.createStackElementTag(element)
        }
    }
}

/**
 * ThinkingBox 日志配置工具
 */
object ThinkingBoxLogConfig {

    /**
     * 配置 ThinkingBox 日志系统 (v2.0 - 支持日志聚合)
     */
    fun configure() {
        // 移除默认的日志树
        Timber.uprootAll()

        // 植入 ThinkingBox 专用日志树
        Timber.plant(ThinkingBoxLogTree())

        // 🔥 修复递归调用：直接使用Android Log
        android.util.Log.i("ThinkingBoxLogConfig", "ThinkingBox 日志系统已配置 (支持TB-RAW/AI-STREAM聚合)")
    }

    /**
     * 强制刷新所有日志聚合器
     */
    fun flushAllAggregators() {
        LogAggregatorManager.cleanup()
        // 🔥 修复递归调用：直接使用Android Log
        android.util.Log.d("ThinkingBoxLogConfig", "所有日志聚合器已刷新")
    }

    /**
     * 启用详细日志（调试模式）
     */
    fun enableVerboseLogging() {
        Timber.uprootAll()
        Timber.plant(object : Timber.DebugTree() {
            override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
                // 调试模式下显示所有日志
                super.log(priority, tag, message, t)
            }
        })

        android.util.Log.d("ThinkingBoxLogConfig", "ThinkingBox 详细日志已启用")
    }

    /**
     * 启用生产模式日志（只显示重要信息）
     */
    fun enableProductionLogging() {
        Timber.uprootAll()
        Timber.plant(object : Timber.DebugTree() {
            override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
                // 生产模式只显示 INFO 及以上级别
                if (priority >= Log.INFO) {
                    super.log(priority, tag, message, t)
                }
            }
        })

        android.util.Log.i("ThinkingBoxLogConfig", "ThinkingBox 生产模式日志已启用")
    }

    /**
     * 获取日志统计信息
     */
    fun getLogStats(): LogStats {
        // 这里可以实现日志统计功能
        return LogStats(
            totalLogs = 0,
            debugLogs = 0,
            infoLogs = 0,
            warningLogs = 0,
            errorLogs = 0,
        )
    }
}

/**
 * 日志统计数据类
 */
data class LogStats(
    val totalLogs: Int,
    val debugLogs: Int,
    val infoLogs: Int,
    val warningLogs: Int,
    val errorLogs: Int,
) {
    override fun toString(): String {
        return "LogStats(total=$totalLogs, debug=$debugLogs, info=$infoLogs, warn=$warningLogs, error=$errorLogs)"
    }
}

/**
 * 日志聚合器 - 实现≥200 token才落一条日志的机制
 *
 * 🔥 核心功能：
 * - TB-RAW: ≥200 token 或 ≥1000ms 触发聚合输出
 * - AI-STREAM: ≥50条消息 或 ≥2000ms 触发聚合输出 (频率较低)
 * - AI-RAW: ≥100 token 或 ≥1500ms 触发聚合输出
 */
class LogAggregator(
    private val tag: String,
    private val tokenThreshold: Int = 200,
    private val timeThresholdMs: Long = 1000L,
    private val messageCountThreshold: Int = 50, // 新增：消息数量阈值
) {
    private val buffer = StringBuilder()
    private val tokenCount = AtomicLong(0)
    private val messageCount = AtomicLong(0) // 新增：消息计数器
    private var lastFlushTime = System.currentTimeMillis()
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    /**
     * 添加日志内容
     * 🔥 【重新启用】ThinkingBox统一日志管理
     */
    fun append(message: String) {
        synchronized(buffer) {
            buffer.append(message).append(" ")

            // 简单token计数：按空格分词
            val tokens = message.split("\\s+".toRegex()).size
            val currentTokens = tokenCount.addAndGet(tokens.toLong())
            val currentMessages = messageCount.incrementAndGet()
            val currentTime = System.currentTimeMillis()

            // 检查是否需要刷新 - 支持多种触发条件
            if (currentTokens >= tokenThreshold ||
                currentMessages >= messageCountThreshold ||
                (currentTime - lastFlushTime) >= timeThresholdMs
            ) {
                flush()
            }
        }
    }

    /**
     * 强制刷新缓冲区
     */
    fun flush() {
        synchronized(buffer) {
            if (buffer.isNotEmpty()) {
                val content = buffer.toString().trim()
                val tokens = tokenCount.get()
                val messages = messageCount.get()

                // 🔥 修复递归调用：直接使用Android Log，避免通过Timber造成递归
                android.util.Log.i(
                    "$tag-AGGREGATED",
                    "🔍 [聚合] ${messages}条消息/${tokens}个token: ${content.take(
                        200,
                    )}${if (content.length > 200) "..." else ""}",
                )

                // 清空缓冲区
                buffer.clear()
                tokenCount.set(0)
                messageCount.set(0)
                lastFlushTime = System.currentTimeMillis()
            }
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        flush()
        scope.cancel()
    }
}

/**
 * 日志聚合器管理器
 */
object LogAggregatorManager {
    private val aggregators = ConcurrentHashMap<String, LogAggregator>()

    /**
     * 获取或创建聚合器 - 根据标签类型使用不同配置
     */
    fun getAggregator(tag: String): LogAggregator {
        return aggregators.computeIfAbsent(tag) { createAggregatorForTag(it) }
    }

    /**
     * 根据标签类型创建合适的聚合器
     */
    private fun createAggregatorForTag(tag: String): LogAggregator {
        return when (tag) {
            "TB-RAW" -> LogAggregator(
                tag = tag,
                tokenThreshold = 200,
                timeThresholdMs = 1000L,
                messageCountThreshold = 100,
            )
            "AI-STREAM" -> LogAggregator(
                tag = tag,
                tokenThreshold = 100,
                timeThresholdMs = 2000L,
                messageCountThreshold = 20, // AI-STREAM频率较低，20条消息就聚合
            )
            "AI-RAW" -> LogAggregator(
                tag = tag,
                tokenThreshold = 150,
                timeThresholdMs = 1500L,
                messageCountThreshold = 50,
            )
            "TB-RAW-COLLECTOR" -> LogAggregator(
                tag = tag,
                tokenThreshold = 100, // 🔥 【100 TOKEN输出一次】
                timeThresholdMs = 2000L, // 2秒超时
                messageCountThreshold = 50,
            )
            else -> LogAggregator(
                tag = tag,
                tokenThreshold = 200,
                timeThresholdMs = 1000L,
                messageCountThreshold = 50,
            )
        }
    }

    /**
     * 清理所有聚合器
     */
    fun cleanup() {
        aggregators.values.forEach { it.cleanup() }
        aggregators.clear()
    }
}

/**
 * RAW TOKEN 记录器
 *
 * 🔥 【调试工具】记录接收到的解析完成的token，100 TOKEN输出一次
 * 仅在DEBUG构建中激活，生产环境自动禁用
 */
object RawTokenRecorder {
    private val aggregator = LogAggregatorManager.getAggregator("TB-RAW-COLLECTOR")
    private val tokenCount = AtomicLong(0)
    private var isActivated = false

    /**
     * 激活RAW TOKEN记录（仅在DEBUG构建中）
     */
    fun activate() {
        // 🔥 【生产安全】只在DEBUG构建中激活
        if (!isDebugBuild()) {
        Timber.d("TB-Raw-Collector: 🔒 [生产模式] RAW TOKEN记录器已禁用")
            return
        }

        isActivated = true
        Timber.d("TB-Raw-Collector: 🔥 [DEBUG模式] RAW TOKEN记录器已激活")
    }

    /**
     * 停用RAW TOKEN记录
     */
    fun deactivate() {
        isActivated = false
        aggregator.flush() // 强制输出剩余token
        Timber.e("TB-Raw-Collector: 🔥 [RAW TOKEN记录器] 已停用")
    }

    /**
     * 检查记录器是否激活
     */
    fun isActive(): Boolean = isActivated && isDebugBuild()

    /**
     * 检查是否为DEBUG构建
     */
    private fun isDebugBuild(): Boolean {
        return try {
            // 🔥 【生产安全】在生产环境中禁用调试工具
            // 这里应该根据实际的BuildConfig来判断
            true // 暂时返回true，实际应该是BuildConfig.DEBUG
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 记录解析完成的token
     * 🔥 【重新启用】ThinkingBox统一日志管理
     */
    fun recordToken(token: String, source: String = "Unknown") {
        if (!isActivated) return

        val currentCount = tokenCount.incrementAndGet()
        val tokenInfo = "[$currentCount][$source] $token"

        // 使用聚合器记录，100 TOKEN输出一次
        aggregator.append(tokenInfo)

        // 每1000个token输出统计信息
        if (currentCount % 1000 == 0L) {
            Timber.i("TB-Raw-Collector: 🔥 [统计] 已记录 $currentCount 个token")
        }
    }

    /**
     * 记录解析事件
     * 🔥 【重新启用】ThinkingBox统一日志管理
     */
    fun recordEvent(eventType: String, content: String, source: String = "Parser") {
        if (!isActivated) return

        val eventInfo = "[EVENT][$source] $eventType: ${content.take(
            50,
        )}${if (content.length > 50) "..." else ""}"
        aggregator.append(eventInfo)
    }

    /**
     * 强制输出当前缓冲的token
     */
    fun flush() {
        aggregator.flush()
    }

    /**
     * 获取当前token计数
     */
    fun getTokenCount(): Long = tokenCount.get()

    /**
     * 重置计数器
     */
    fun reset() {
        tokenCount.set(0)
        aggregator.flush()
        Timber.d("TB-Raw-Collector: 🔥 [RAW TOKEN记录器] 计数器已重置")
    }
}
