package com.example.gymbro.features.workout.template.edit.contract

import androidx.compose.runtime.Immutable
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.shared.models.workout.TemplateExerciseDto

/**
 * =========================================================================================
 * 🔥 GymBro Template Edit MVI Contract - 遵循黄金标准 🔥
 * =========================================================================================
 *
 * 本Contract遵循 ProfileBioContract 黄金标准，实现简洁、高效的MVI架构。
 *
 * 🎯 核心原则：
 * 1. State简洁性：最小化状态属性，避免状态爆炸
 * 2. Intent清晰性：动词命名，明确表达用户意图
 * 3. Effect纯净性：仅描述副作用，不包含执行逻辑
 * 4. 单一职责：每个组件职责明确，避免混合关注点
 */
object TemplateEditContract {

    /**
     * Intent - 用户意图
     *
     * 规范：
     * - 动词命名，清晰表达用户或系统的意图
     * - `...Result` 后缀用于从异步操作返回的内部 Intent
     */
    sealed interface Intent : AppIntent {
        // 模板生命周期
        data class LoadTemplate(val templateId: String?) : Intent
        data class CreateEmptyTemplate(val userId: String) : Intent
        object SaveTemplate : Intent
        object SaveAsDraft : Intent
        object CreateAndSaveImmediately : Intent
        object PublishTemplate : Intent
        object DeleteTemplate : Intent

        // 模板内容编辑
        data class UpdateTemplateName(val name: String) : Intent
        data class UpdateTemplateDescription(val description: String) : Intent
        data class AddExercise(val exercise: Exercise) : Intent
        data class AddExercises(val exercises: List<Exercise>) : Intent
        data class UpdateExercise(val exercise: TemplateExerciseDto) : Intent
        data class RemoveExercise(val exerciseId: String) : Intent
        data class ReorderExercises(val fromIndex: Int, val toIndex: Int) : Intent

        // 模板数据设置
        data class SetTemplate(val template: WorkoutTemplate) : Intent
        data class SetCurrentUserId(val userId: String) : Intent
        data class SetVersionHistory(val versions: List<Any>) : Intent

        // UI状态控制
        object ShowExerciseSelector : Intent
        object ToggleExerciseSelector : Intent
        object NavigateBack : Intent
        object PrepareToExit : Intent
        object ShowPreview : Intent
        object ResetNavigationState : Intent
        object ClearError : Intent
        data class HandleError(val error: UiText) : Intent

        // 对话框管理
        object ShowTemplateNameDialog : Intent
        object ShowTemplateDescriptionDialog : Intent
        object DismissDialog : Intent
        data class UpdateTempTemplateName(val name: String) : Intent
        data class UpdateTempTemplateDescription(val description: String) : Intent
        object ConfirmTemplateName : Intent
        object ConfirmTemplateDescription : Intent

        // 版本管理
        object ShowVersionHistory : Intent
        object HideVersionHistory : Intent
        data class RestoreFromVersion(val version: Any) : Intent
        data class VersionCreated(val version: Any) : Intent

        // 快速操作
        data class ShowQuickActions(val exerciseId: String) : Intent
        object HideQuickActions : Intent
        data class QuickDuplicateExercise(val exerciseId: String) : Intent
        data class QuickDeleteExercise(val exerciseId: String) : Intent

        // 拖拽操作
        data class StartDrag(val exerciseId: String, val startIndex: Int) : Intent
        data class UpdateDragPosition(val targetIndex: Int, val offset: Float) : Intent
        data class CompleteDrag(val fromIndex: Int, val toIndex: Int) : Intent
        object CancelDrag : Intent

        // 状态管理
        object SaveSuccess : Intent
        object DraftSaved : Intent
        object PublishCompleted : Intent

        // 内部结果 Intent
        data class LoadTemplateResult(val result: com.example.gymbro.core.error.types.ModernResult<WorkoutTemplate?>) : Intent
        data class SaveTemplateResult(val result: com.example.gymbro.core.error.types.ModernResult<String>) : Intent
        data class DeleteTemplateResult(val result: com.example.gymbro.core.error.types.ModernResult<Unit>) : Intent
    }

    /**
     * State - UI 状态快照
     *
     * 规范：
     * - 必须使用 @Immutable 注解
     * - 所有属性都是 `val`
     * - 包含数据状态、UI 瞬时状态（如加载）和错误状态
     * - 使用派生属性（get()）来计算衍生值，避免在 UI 层计算
     */
    @Immutable
    data class State(
        // 核心数据
        val template: WorkoutTemplate? = null,
        val exercises: List<TemplateExerciseDto> = emptyList(),

        // 基本状态
        val isLoading: Boolean = false,
        val isSaving: Boolean = false,
        val error: UiText? = null,
        val hasUnsavedChanges: Boolean = false,

        // 模板基本信息
        val templateName: String = "",
        val templateDescription: String = "",
        val currentUserId: String? = null,

        // UI状态
        val showExerciseSelector: Boolean = false,
        val showVersionHistory: Boolean = false,
        val isCreatingVersion: Boolean = false,
        val isRestoringVersion: Boolean = false,

        // 对话框状态
        val showTemplateNameDialog: Boolean = false,
        val showTemplateDescriptionDialog: Boolean = false,
        val tempTemplateName: String = "",
        val tempTemplateDescription: String = "",

        // 快速操作
        val showQuickActions: Boolean = false,
        val quickActionTargetId: String? = null,

        // 拖拽相关
        val isDragInProgress: Boolean = false,
        val draggedExerciseId: String? = null,
        val draggedItemIndex: Int = -1,
        val dragTargetIndex: Int = -1,
        val dragOffset: Float = 0f,
        val reorderingEnabled: Boolean = true,



        // 版本信息
        val versionHistory: List<Any> = emptyList(),
        val currentVersion: Int = 1,
        val lastPublishedAt: Long? = null
    ) : UiState {

        // 派生属性 - 在State中计算，UI直接使用
        val isNewTemplate: Boolean get() = template?.id.isNullOrBlank()
        val canSave: Boolean get() = !template?.name.isNullOrBlank() && exercises.isNotEmpty() && !isSaving
        val exerciseCount: Int get() = exercises.size
        val isDraft: Boolean get() = template?.isDraft ?: true
        val isPublished: Boolean get() = template?.isPublished ?: false
    }

    /**
     * Effect - 副作用
     *
     * 规范：
     * - 动词命名，描述一个需要执行的、一次性的事件
     * - 不包含任何执行逻辑，仅为数据载体
     * - 由 ViewModel 发出，UI 层监听并执行
     */
    sealed interface Effect : UiEffect {
        // 通用消息
        data class ShowToast(val message: UiText) : Effect
        data class ShowError(val error: UiText) : Effect

        // 导航
        object NavigateBack : Effect
        object NavigateToExerciseLibrary : Effect
        object NavigateToPreview : Effect

        // 保存相关
        data class SaveSuccess(val templateId: String) : Effect
        object ShowTemplatePublished : Effect
        object ShowDraftSaved : Effect
        // 版本管理
        object ShowVersionRestored : Effect

        // 加载数据
        object LoadTemplateData : Effect

        // 缺失的Effect类型
        data class TemplateCreated(val templateId: String) : Effect
        data class TemplateDeleted(val templateId: String) : Effect
        data class SaveTemplateBasicInfo(val name: String, val description: String) : Effect

        // 导航相关
        data class NavigateToExerciseDetails(val exerciseId: String) : Effect
        data class NavigateToTemplateDetails(val templateId: String) : Effect

        // 对话框相关
        data class ShowSnackbar(val message: UiText, val actionLabel: UiText? = null, val action: (() -> Unit)? = null) : Effect
        object ShowUnsavedChangesDialog : Effect
        data class ShowDeleteConfirmDialog(val templateName: String) : Effect
        object ShowExitConfirmDialog : Effect

        // 保存相关
        object SaveAsDraft : Effect
        object PublishTemplate : Effect
        object CreateAndSaveImmediately : Effect
        object ShowVersionCreated : Effect

        // 退出相关
        object PrepareToExit : Effect
    }
}
