package com.example.gymbro.detekt.quality

import io.gitlab.arturbosch.detekt.api.*
import org.jetbrains.kotlin.psi.*

/**
 * 检查 TODO 和 FIXME 注释
 * 
 * 规则：代码中不允许存在 TODO、FIXME、HACK、XXX 等注释
 * 这些注释表明代码未完成或存在问题
 */
class NoTodoOrFixme(config: Config = Config.empty) : Rule(config) {
    
    override val issue = Issue(
        javaClass.simpleName,
        Severity.CodeSmell,
        "代码中不允许存在 TODO、FIXME 等未完成标记。",
        Debt.FIVE_MINS
    )
    
    private val keywords by config(listOf("TODO", "FIXME", "HACK", "XXX")) { 
        "禁止的关键词列表" 
    }
    
    override fun visitComment(comment: PsiComment) {
        super.visitComment(comment)
        
        val commentText = comment.text.uppercase()
        
        for (keyword in keywords) {
            if (commentText.contains(keyword)) {
                report(
                    CodeSmell(
                        issue,
                        Entity.from(comment),
                        "发现禁用的注释标记 '$keyword'。请完成相关工作或移除此注释。" +
                        "\n注释内容：${comment.text.trim()}"
                    )
                )
                break // 避免同一注释多次报告
            }
        }
    }
    
    override fun visitProperty(property: KtProperty) {
        super.visitProperty(property)
        
        // 检查属性名中是否包含这些关键词
        val propertyName = property.name?.uppercase() ?: return
        
        for (keyword in keywords) {
            if (propertyName.contains(keyword)) {
                report(
                    CodeSmell(
                        issue,
                        Entity.from(property),
                        "属性名 '${property.name}' 包含禁用标记 '$keyword'。请使用更描述性的名称。"
                    )
                )
                break
            }
        }
    }
    
    override fun visitNamedFunction(function: KtNamedFunction) {
        super.visitNamedFunction(function)
        
        // 检查函数名中是否包含这些关键词
        val functionName = function.name?.uppercase() ?: return
        
        for (keyword in keywords) {
            if (functionName.contains(keyword)) {
                report(
                    CodeSmell(
                        issue,
                        Entity.from(function),
                        "函数名 '${function.name}' 包含禁用标记 '$keyword'。请使用更描述性的名称。"
                    )
                )
                break
            }
        }
    }
}