package com.example.gymbro.data.shared.sync

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.shared.sync.SyncCoordinator
import com.example.gymbro.domain.shared.sync.model.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * SyncCoordinator接口的实现
 * 封装了SyncManager的功能，提供了Domain层需要的同步功能
 */
@Singleton
class SyncCoordinatorImpl
@Inject
constructor(
    // 基础同步协调器实现，使用默认配置
) : SyncCoordinator {
    override fun getSyncStatus(): Flow<SyncStatus> =
        flowOf(
            SyncStatus(
                state = SyncStatus.State.IDLE,
                dataType = DataType.ALL,
                currentOperation = "等待同步",
            ),
        )

    override suspend fun getLastSyncTime(dataType: DataType): ModernResult<Long> {
        Timber.d("获取最后同步时间: dataType=%s", dataType)
        // 返回当前时间作为基础实现
        return ModernResult.Success(System.currentTimeMillis())
    }

    override suspend fun getLastSyncResult(dataType: DataType): SyncResult {
        Timber.d("获取最后同步结果: dataType=%s", dataType)
        // 返回模拟成功结果作为基础实现
        return SyncResult(
            success = true,
            dataType = dataType,
            syncStartTime = System.currentTimeMillis() - 1000,
            syncEndTime = System.currentTimeMillis(),
        )
    }

    override suspend fun getSyncConfig(): SyncConfig {
        Timber.d("获取同步配置")
        // 返回默认配置作为基础实现
        return SyncConfig(
            enabled = true,
            autoSyncEnabled = true,
            // 24小时
            conflictResolutionStrategy = ConflictResolutionStrategy.ASK_USER,
        )
    }

    override suspend fun updateSyncConfig(config: SyncConfig): ModernResult<Unit> {
        Timber.d("更新同步配置: %s", config)
        // 基础实现：接受配置但不持久化
        return ModernResult.Success(Unit)
    }

    override suspend fun requestSync(dataType: DataType): ModernResult<Unit> {
        Timber.d("请求同步: dataType=%s", dataType)
        // 基础实现：记录请求但不执行实际同步
        return ModernResult.Success(Unit)
    }

    override suspend fun scheduleSync(
        dataType: DataType,
        entityId: String?,
    ): ModernResult<Unit> {
        Timber.d("安排同步: dataType=%s, entityId=%s", dataType, entityId)
        // 基础实现：记录调度但不执行实际同步
        return ModernResult.Success(Unit)
    }

    override suspend fun startSync(
        dataType: DataType,
        forceFullSync: Boolean,
    ): ModernResult<SyncResult> {
        Timber.d("启动同步: dataType=%s, forceFullSync=%s", dataType, forceFullSync)
        // 基础实现：返回模拟的成功同步结果
        return ModernResult.Success(
            SyncResult(
                success = true,
                dataType = dataType,
                syncStartTime = System.currentTimeMillis() - 1000,
                syncEndTime = System.currentTimeMillis(),
            ),
        )
    }

    override suspend fun cancelSync(): ModernResult<Unit> {
        Timber.d("取消同步")
        // 基础实现：记录取消操作
        return ModernResult.Success(Unit)
    }

    override suspend fun canSync(): Boolean {
        Timber.d("检查是否可以同步")
        // 基础实现：总是返回可以同步
        return true
    }

    override suspend fun resetSyncState(): ModernResult<Unit> {
        Timber.d("重置同步状态")
        // 基础实现：记录重置操作
        return ModernResult.Success(Unit)
    }
}
