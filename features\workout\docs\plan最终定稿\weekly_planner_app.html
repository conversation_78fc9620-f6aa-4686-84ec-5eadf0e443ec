<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>周计划APP</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            max-width: 400px;
            height: 100vh;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        }

        .header {
            display: flex;
            align-items: center;
            padding: 20px;
            background: #f8f9ff;
            border-bottom: 2px solid #e3e8ff;
        }

        .back-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #4F94CD;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #4F94CD;
            color: white;
        }

        .plan-name {
            flex: 1;
            padding: 12px 20px;
            border: 2px solid #4F94CD;
            border-radius: 10px;
            background: white;
            text-align: center;
            font-size: 16px;
            color: #4F94CD;
        }

        .save-btn {
            margin-left: 15px;
            padding: 12px 20px;
            background: #4F94CD;
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .save-btn:hover {
            background: #36648B;
            transform: translateY(-2px);
        }

        .week-tabs {
            display: flex;
            padding: 10px 20px;
            background: white;
            border-bottom: 1px solid #eee;
            overflow-x: auto;
        }

        .week-tab {
            padding: 8px 16px;
            margin-right: 10px;
            border: 1px solid #ff6b6b;
            border-radius: 20px;
            color: #ff6b6b;
            cursor: pointer;
            white-space: nowrap;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .week-tab.active {
            background: #ff6b6b;
            color: white;
            transform: scale(1.05);
        }

        .main-content {
            display: flex;
            height: calc(100vh - 200px);
            position: relative;
        }

        .canvas-area {
            flex: 1;
            background: #f8f9ff;
            position: relative;
            overflow-y: auto;
            padding: 20px;
        }

        .day-item {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            min-height: 60px;
            padding: 15px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .day-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .day-label {
            width: 60px;
            color: #ff6b6b;
            font-weight: bold;
            font-size: 16px;
        }

        .day-content {
            flex: 1;
            min-height: 40px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 10px;
            position: relative;
            transition: all 0.3s ease;
        }

        .day-content.drag-over {
            border-color: #4F94CD;
            background: #f0f7ff;
            transform: scale(1.02);
        }

        .floating-panel {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 80px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 15px 10px;
            z-index: 1000;
        }

        .draft-item {
            width: 60px;
            height: 50px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: grab;
            transition: all 0.3s ease;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
            line-height: 1.2;
        }

        .draft-item:active {
            cursor: grabbing;
        }

        .draft-item:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }

        .draft-item:last-child {
            margin-bottom: 0;
        }

        .bottom-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            background: white;
            border-top: 1px solid #eee;
            padding: 15px 20px;
        }

        .control-item {
            flex: 1;
            text-align: center;
            padding: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 10px;
            margin: 0 5px;
        }

        .control-item.active {
            background: #ff6b6b;
            color: white;
            transform: translateY(-3px);
        }

        .control-item:not(.active) {
            color: #666;
        }

        .control-item:hover {
            background: #f0f0f0;
        }

        .control-item.active:hover {
            background: #ff5252;
        }

        .dropped-item {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            margin: 5px;
            display: inline-block;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: dropIn 0.5s ease;
        }

        .dropped-item:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(102,126,234,0.4);
        }

        @keyframes dropIn {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .drag-helper {
            position: fixed;
            pointer-events: none;
            z-index: 9999;
            opacity: 0.8;
            transform: rotate(5deg);
        }

        .success-indicator {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #4CAF50;
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .success-indicator.show {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <div class="back-btn">←</div>
            <div class="plan-name">plan_name</div>
            <button class="save-btn">保存</button>
        </div>

        <div class="week-tabs">
            <div class="week-tab active" data-week="1">第一周</div>
            <div class="week-tab" data-week="2">第二周</div>
            <div class="week-tab" data-week="3">第三周</div>
            <div class="week-tab" data-week="4">第四周</div>
        </div>

        <div class="main-content">
            <div class="canvas-area">
                <div class="day-item">
                    <div class="day-label">周一</div>
                    <div class="day-content" data-day="monday"></div>
                </div>
                <div class="day-item">
                    <div class="day-label">周二</div>
                    <div class="day-content" data-day="tuesday"></div>
                </div>
                <div class="day-item">
                    <div class="day-label">周三</div>
                    <div class="day-content" data-day="wednesday"></div>
                </div>
                <div class="day-item">
                    <div class="day-label">周四</div>
                    <div class="day-content" data-day="thursday"></div>
                </div>
                <div class="day-item">
                    <div class="day-label">周五</div>
                    <div class="day-content" data-day="friday"></div>
                </div>
            </div>
        </div>

        <div class="floating-panel">
            <div class="draft-item" draggable="true" data-content="会议">草稿</div>
            <div class="draft-item" draggable="true" data-content="学习">草稿</div>
            <div class="draft-item" draggable="true" data-content="运动">草稿</div>
            <div class="draft-item" draggable="true" data-content="阅读">草稿</div>
            <div class="draft-item" draggable="true" data-content="购物">草稿</div>
            <div class="draft-item" draggable="true" data-content="草稿<br>模版">草稿<br>模版</div>
        </div>

        <div class="bottom-controls">
            <div class="control-item active">总重量</div>
            <div class="control-item">总训练天数</div>
            <div class="control-item">已经完成0/0</div>
        </div>

        <div class="success-indicator">添加成功！</div>
    </div>

    <script>
        let draggedElement = null;
        let dragHelper = null;
        let currentWeek = 1;
        const weekData = {
            1: {},
            2: {},
            3: {},
            4: {}
        };

        // 周标签切换
        document.querySelectorAll('.week-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 保存当前周的数据
                saveCurrentWeekData();
                
                // 切换激活状态
                document.querySelectorAll('.week-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                // 更新当前周
                currentWeek = parseInt(this.dataset.week);
                
                // 加载新周的数据
                loadWeekData(currentWeek);
                
                // 添加切换动画
                const canvasArea = document.querySelector('.canvas-area');
                canvasArea.style.transform = 'translateX(-20px)';
                canvasArea.style.opacity = '0.7';
                
                setTimeout(() => {
                    canvasArea.style.transform = 'translateX(0)';
                    canvasArea.style.opacity = '1';
                }, 200);
            });
        });

        // 保存当前周数据
        function saveCurrentWeekData() {
            document.querySelectorAll('.day-content').forEach(dayContent => {
                const day = dayContent.dataset.day;
                const items = Array.from(dayContent.querySelectorAll('.dropped-item')).map(item => item.textContent);
                weekData[currentWeek][day] = items;
            });
        }

        // 加载周数据
        function loadWeekData(week) {
            document.querySelectorAll('.day-content').forEach(dayContent => {
                const day = dayContent.dataset.day;
                dayContent.innerHTML = '';
                
                if (weekData[week][day]) {
                    weekData[week][day].forEach(itemText => {
                        const droppedItem = document.createElement('div');
                        droppedItem.className = 'dropped-item';
                        droppedItem.textContent = itemText;
                        droppedItem.addEventListener('click', function() {
                            this.remove();
                        });
                        dayContent.appendChild(droppedItem);
                    });
                }
            });
        }

        // 拖拽功能
        document.querySelectorAll('.draft-item').forEach(item => {
            item.addEventListener('dragstart', function(e) {
                draggedElement = this;
                
                // 创建拖拽助手
                dragHelper = this.cloneNode(true);
                dragHelper.className = 'draft-item drag-helper';
                document.body.appendChild(dragHelper);
                
                // 设置拖拽数据
                e.dataTransfer.setData('text/plain', this.dataset.content);
                e.dataTransfer.effectAllowed = 'copy';
                
                // 隐藏默认拖拽图像
                const emptyImg = new Image();
                e.dataTransfer.setDragImage(emptyImg, 0, 0);
                
                this.style.opacity = '0.5';
            });

            item.addEventListener('dragend', function() {
                this.style.opacity = '1';
                if (dragHelper) {
                    document.body.removeChild(dragHelper);
                    dragHelper = null;
                }
            });
        });

        // 鼠标移动时更新拖拽助手位置
        document.addEventListener('dragover', function(e) {
            if (dragHelper) {
                dragHelper.style.left = (e.clientX - 30) + 'px';
                dragHelper.style.top = (e.clientY - 25) + 'px';
            }
        });

        // 放置区域事件
        document.querySelectorAll('.day-content').forEach(dayContent => {
            dayContent.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'copy';
                this.classList.add('drag-over');
            });

            dayContent.addEventListener('dragleave', function() {
                this.classList.remove('drag-over');
            });

            dayContent.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('drag-over');
                
                const data = e.dataTransfer.getData('text/plain');
                
                // 创建新的项目元素
                const droppedItem = document.createElement('div');
                droppedItem.className = 'dropped-item';
                droppedItem.textContent = data;
                
                // 添加点击删除功能
                droppedItem.addEventListener('click', function() {
                    this.style.animation = 'dropIn 0.3s ease reverse';
                    setTimeout(() => {
                        this.remove();
                    }, 300);
                });
                
                this.appendChild(droppedItem);
                
                // 显示成功提示
                showSuccessIndicator();
            });
        });

        // 显示成功提示
        function showSuccessIndicator() {
            const indicator = document.querySelector('.success-indicator');
            indicator.classList.add('show');
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 1500);
        }

        // 底部控制按钮
        document.querySelectorAll('.control-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.control-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 保存按钮
        document.querySelector('.save-btn').addEventListener('click', function() {
            saveCurrentWeekData();
            this.textContent = '已保存';
            this.style.background = '#4CAF50';
            
            setTimeout(() => {
                this.textContent = '保存';
                this.style.background = '#4F94CD';
            }, 2000);
        });

        // 返回按钮
        document.querySelector('.back-btn').addEventListener('click', function() {
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });

        // 初始化
        loadWeekData(1);
    </script>
</body>
</html>