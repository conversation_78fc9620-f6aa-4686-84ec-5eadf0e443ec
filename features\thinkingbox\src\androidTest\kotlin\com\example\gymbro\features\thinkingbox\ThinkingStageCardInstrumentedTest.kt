package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.testTag
import androidx.compose.ui.test.assertHeightIsAtMost
import androidx.compose.ui.test.assertIsDisplayed
import androidx.compose.ui.test.assertIsNotDisplayed
import androidx.compose.ui.test.junit4.createComposeRule
import androidx.compose.ui.test.onNodeWithTag
import androidx.compose.ui.test.onNodeWithText
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.features.thinkingbox.domain.model.SegmentKind
import com.example.gymbro.features.thinkingbox.internal.constants.ThinkingBoxStrings
import com.example.gymbro.features.thinkingbox.internal.contract.ThinkingBoxContract
import kotlinx.coroutines.delay
import kotlinx.coroutines.test.runTest
import org.junit.Rule
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * ThinkingStageCard仪器化集成测试
 * 
 * 🎯 测试目标：验证独立渲染生命周期和UI握手机制
 * 📊 测试要求：仪器化测试，真实UI交互验证
 * 🔧 测试框架：Compose UI Test + JUnit 4 + kotlin.test  
 * 🔥 重点：独立渲染状态管理 + onSegmentRendered握手机制 + 高度限制验证
 */
class ThinkingStageCardInstrumentedTest {

    @get:Rule
    val composeTestRule = createComposeRule()

    companion object {
        // 测试标签
        private const val THINKING_STAGE_CARD_TAG = "thinking_stage_card"
        private const val CARD_CONTAINER_TAG = "card_container"
        
        // 测试数据工厂
        private fun createTestSegmentUi(
            id: String,
            kind: SegmentKind = SegmentKind.PHASE,
            title: String? = "测试阶段",
            content: String = "测试思考内容",
            isComplete: Boolean = true,
            isRendered: Boolean = false
        ) = ThinkingBoxContract.SegmentUi(
            id = id,
            kind = kind,
            title = title,
            content = content,
            isComplete = isComplete,
            isRendered = isRendered
        )
    }

    /**
     * 🔥 【独立渲染生命周期】每个ThinkingStageCard管理自己的渲染状态
     */
    @Test
    fun testIndependentRenderingLifecycle_SelfManagedState() = runTest {
        // Given
        val segment = createTestSegmentUi("test-segment", title = "独立渲染测试", content = "渲染内容")
        var renderingCompleted = false
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = segment,
                    modifier = Modifier.testTag(THINKING_STAGE_CARD_TAG),
                    onSegmentRendered = { segmentId ->
                        assertEquals("test-segment", segmentId)
                        renderingCompleted = true
                    }
                )
            }
        }
        
        // When - 等待独立渲染完成
        // 渲染时间 = 标题渲染时间 + 内容渲染时间
        val titleLength = "独立渲染测试".length
        val contentLength = "渲染内容".length
        val expectedTime = (titleLength + contentLength) * 33 + 200 // 额外缓冲时间
        
        delay(expectedTime.toLong())
        composeTestRule.waitForIdle()
        
        // Then - 验证独立渲染完成
        composeTestRule.onNodeWithTag(THINKING_STAGE_CARD_TAG).assertIsDisplayed()
        composeTestRule.onNodeWithText("独立渲染测试").assertIsDisplayed()
        composeTestRule.onNodeWithText("渲染内容").assertIsDisplayed()
        assertTrue(renderingCompleted, "独立渲染应该完成并触发回调")
    }

    /**
     * 🔥 【UI握手机制】onSegmentRendered精确触发验证
     */
    @Test
    fun testUIHandshakeMechanism_PreciseCallback() = runTest {
        // Given
        val segment = createTestSegmentUi("handshake-test", title = "握手测试", content = "握手内容")
        var callbackSegmentId: String? = null
        var callbackCount = 0
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = segment,
                    modifier = Modifier.testTag(THINKING_STAGE_CARD_TAG),
                    onSegmentRendered = { segmentId ->
                        callbackSegmentId = segmentId
                        callbackCount++
                    }
                )
            }
        }
        
        // When - 等待握手机制完成
        delay(1000) // 足够时间完成渲染
        composeTestRule.waitForIdle()
        
        // Then - 验证握手机制
        assertEquals("handshake-test", callbackSegmentId, "回调应该传递正确的segmentId")
        assertEquals(1, callbackCount, "回调应该只触发一次")
        composeTestRule.onNodeWithTag(THINKING_STAGE_CARD_TAG).assertIsDisplayed()
    }

    /**
     * 🔥 【标题显示逻辑】基于SegmentKind的标题显示规则验证
     */
    @Test
    fun testTitleDisplayLogic_SegmentKindBasedRules() {
        // Test Case 1: PERTHINK段使用固定标题
        val perthinkSegment = createTestSegmentUi(
            "perthink-test",
            kind = SegmentKind.PERTHINK,
            title = null // PERTHINK不使用传入的title
        )
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = perthinkSegment,
                    modifier = Modifier.testTag(THINKING_STAGE_CARD_TAG)
                )
            }
        }
        
        composeTestRule.waitForIdle()
        
        // 验证PERTHINK使用固定标题
        composeTestRule.onNodeWithText(ThinkingBoxStrings.PERTHINK_TITLE).assertIsDisplayed()
        
        // Test Case 2: PHASE段使用自定义标题
        val phaseSegment = createTestSegmentUi(
            "phase-test", 
            kind = SegmentKind.PHASE,
            title = "自定义阶段标题"
        )
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = phaseSegment,
                    modifier = Modifier.testTag(THINKING_STAGE_CARD_TAG)
                )
            }
        }
        
        composeTestRule.waitForIdle()
        
        // 验证PHASE使用自定义标题
        composeTestRule.onNodeWithText("自定义阶段标题").assertIsDisplayed()
        
        // Test Case 3: 无标题段的处理
        val noTitleSegment = createTestSegmentUi(
            "no-title-test",
            kind = SegmentKind.PHASE,
            title = null,
            content = "只有内容，没有标题"
        )
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = noTitleSegment,
                    modifier = Modifier.testTag(THINKING_STAGE_CARD_TAG)
                )
            }
        }
        
        composeTestRule.waitForIdle()
        
        // 验证只显示内容，没有额外的标题空间
        composeTestRule.onNodeWithText("只有内容，没有标题").assertIsDisplayed()
    }

    /**
     * 🔥 【四条铁律第3条】ThinkingStageCard高度限制验证
     */
    @Test
    fun testIronLaw3_HeightLimit_StageCardLevel() {
        // Given - 超长内容段
        val longContentSegment = createTestSegmentUi(
            "long-content-test",
            title = "超长内容测试",
            content = "这是超长内容测试。".repeat(200) // 创建超长内容
        )
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = longContentSegment,
                    modifier = Modifier.testTag(THINKING_STAGE_CARD_TAG)
                )
            }
        }
        
        composeTestRule.waitForIdle()
        
        // When & Then - 验证高度限制
        composeTestRule.onNodeWithTag(THINKING_STAGE_CARD_TAG)
            .assertIsDisplayed()
            .assertHeightIsAtMost(300.dp) // 验证有合理的高度限制
    }

    /**
     * 🔥 【渲染状态管理】titleRenderComplete和contentRenderComplete状态协调
     */
    @Test
    fun testRenderingStateManagement_StateCoordination() = runTest {
        // Given
        val segment = createTestSegmentUi(
            "state-coordination-test",
            title = "状态协调测试标题",
            content = "状态协调测试内容"
        )
        var renderingCompleted = false
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = segment,
                    modifier = Modifier.testTag(THINKING_STAGE_CARD_TAG),
                    onSegmentRendered = { 
                        renderingCompleted = true
                    }
                )
            }
        }
        
        // When - 分阶段验证渲染状态
        
        // 初始状态：可能只有部分渲染
        delay(100)
        composeTestRule.waitForIdle()
        composeTestRule.onNodeWithTag(THINKING_STAGE_CARD_TAG).assertIsDisplayed()
        
        // 中间状态：标题可能已完成，内容可能还在渲染
        delay(500)
        composeTestRule.waitForIdle()
        
        // 最终状态：所有渲染完成
        delay(1000)
        composeTestRule.waitForIdle()
        
        // Then - 验证完整渲染
        assertTrue(renderingCompleted, "渲染状态协调应该完成")
        composeTestRule.onNodeWithText("状态协调测试标题").assertIsDisplayed()
        composeTestRule.onNodeWithText("状态协调测试内容").assertIsDisplayed()
    }

    /**
     * 🔥 【空内容处理】无标题或无内容时的正确处理
     */
    @Test
    fun testEmptyContentHandling_CorrectBehavior() = runTest {
        // Test Case 1: 只有标题，无内容
        val titleOnlySegment = createTestSegmentUi(
            "title-only-test",
            title = "仅标题测试", 
            content = ""
        )
        var titleOnlyRendered = false
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = titleOnlySegment,
                    modifier = Modifier.testTag("title_only_card"),
                    onSegmentRendered = { titleOnlyRendered = true }
                )
            }
        }
        
        delay(500)
        composeTestRule.waitForIdle()
        
        // 验证只有标题的情况
        assertTrue(titleOnlyRendered, "只有标题的段应该正确渲染")
        composeTestRule.onNodeWithText("仅标题测试").assertIsDisplayed()
        
        // Test Case 2: 只有内容，无标题  
        val contentOnlySegment = createTestSegmentUi(
            "content-only-test",
            title = null,
            content = "仅内容测试"
        )
        var contentOnlyRendered = false
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = contentOnlySegment,
                    modifier = Modifier.testTag("content_only_card"),
                    onSegmentRendered = { contentOnlyRendered = true }  
                )
            }
        }
        
        delay(500)
        composeTestRule.waitForIdle()
        
        // 验证只有内容的情况
        assertTrue(contentOnlyRendered, "只有内容的段应该正确渲染")
        composeTestRule.onNodeWithText("仅内容测试").assertIsDisplayed()
        
        // Test Case 3: 既无标题也无内容
        val emptySegment = createTestSegmentUi(
            "empty-test",
            title = null,
            content = ""
        )
        var emptyRendered = false
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = emptySegment,
                    modifier = Modifier.testTag("empty_card"),
                    onSegmentRendered = { emptyRendered = true }
                )
            }
        }
        
        delay(200) // 空内容应该快速完成
        composeTestRule.waitForIdle()
        
        // 验证空内容的情况
        assertTrue(emptyRendered, "空段也应该完成渲染（立即触发回调）")
        composeTestRule.onNodeWithTag("empty_card").assertIsDisplayed()
    }

    /**
     * 🔥 【isActive参数】激活状态对渲染的影响验证
     */
    @Test
    fun testActiveState_RenderingEffect() = runTest {
        // Given
        val segment = createTestSegmentUi("active-test", title = "激活状态测试", content = "激活内容")
        var activeRendered = false
        var inactiveRendered = false
        
        // Test Case 1: Active状态
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = segment,
                    isActive = true,
                    modifier = Modifier.testTag("active_card"),
                    onSegmentRendered = { activeRendered = true }
                )
            }
        }
        
        delay(800)
        composeTestRule.waitForIdle()
        
        assertTrue(activeRendered, "激活状态的段应该正常渲染")
        composeTestRule.onNodeWithTag("active_card").assertIsDisplayed()
        
        // Test Case 2: Inactive状态  
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = segment,
                    isActive = false,
                    modifier = Modifier.testTag("inactive_card"),
                    onSegmentRendered = { inactiveRendered = true }
                )
            }
        }
        
        delay(800)
        composeTestRule.waitForIdle()
        
        // 即使是inactive，也应该正常渲染（isActive主要影响样式，不影响功能）
        assertTrue(inactiveRendered, "非激活状态的段也应该正常渲染")
        composeTestRule.onNodeWithTag("inactive_card").assertIsDisplayed()
    }

    /**
     * 🔥 【段状态变化】segment参数变化时的重新渲染验证
     */
    @Test
    fun testSegmentStateChange_ReRendering() = runTest {
        // Given
        var segment by mutableStateOf(createTestSegmentUi("change-test", title = "初始标题", content = "初始内容"))
        var renderingCount = 0
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = segment,
                    modifier = Modifier.testTag(THINKING_STAGE_CARD_TAG),
                    onSegmentRendered = { renderingCount++ }
                )
            }
        }
        
        // 等待初始渲染完成
        delay(800)
        composeTestRule.waitForIdle()
        composeTestRule.onNodeWithText("初始标题").assertIsDisplayed()
        composeTestRule.onNodeWithText("初始内容").assertIsDisplayed()
        assertEquals(1, renderingCount, "初始渲染应该完成")
        
        // When - 更新segment
        composeTestRule.runOnIdle {
            segment = createTestSegmentUi("change-test", title = "更新标题", content = "更新内容")
        }
        
        // 等待重新渲染完成
        delay(800)
        composeTestRule.waitForIdle()
        
        // Then - 验证重新渲染
        composeTestRule.onNodeWithText("更新标题").assertIsDisplayed()
        composeTestRule.onNodeWithText("更新内容").assertIsDisplayed()
        assertEquals(2, renderingCount, "应该触发重新渲染")
    }

    /**
     * 🔥 【并发渲染】多个ThinkingStageCard并发渲染的独立性验证
     */
    @Test
    fun testConcurrentRendering_Independence() = runTest {
        // Given
        val segment1 = createTestSegmentUi("concurrent-1", title = "并发测试1", content = "内容1")
        val segment2 = createTestSegmentUi("concurrent-2", title = "并发测试2", content = "内容2")
        val segment3 = createTestSegmentUi("concurrent-3", title = "并发测试3", content = "内容3")
        
        var rendered1 = false
        var rendered2 = false  
        var rendered3 = false
        
        composeTestRule.setContent {
            GymBroTheme {
                Column(modifier = Modifier.fillMaxSize().padding(8.dp)) {
                    ThinkingStageCard(
                        segment = segment1,
                        modifier = Modifier.testTag("card_1"),
                        onSegmentRendered = { rendered1 = true }
                    )
                    
                    ThinkingStageCard(
                        segment = segment2,
                        modifier = Modifier.testTag("card_2"),
                        onSegmentRendered = { rendered2 = true }
                    )
                    
                    ThinkingStageCard(
                        segment = segment3,
                        modifier = Modifier.testTag("card_3"),
                        onSegmentRendered = { rendered3 = true }
                    )
                }
            }
        }
        
        // When - 等待并发渲染完成
        delay(1500) // 足够时间让所有段完成渲染
        composeTestRule.waitForIdle()
        
        // Then - 验证并发独立性
        assertTrue(rendered1, "并发段1应该独立完成渲染")
        assertTrue(rendered2, "并发段2应该独立完成渲染")
        assertTrue(rendered3, "并发段3应该独立完成渲染")
        
        composeTestRule.onNodeWithTag("card_1").assertIsDisplayed()
        composeTestRule.onNodeWithTag("card_2").assertIsDisplayed()
        composeTestRule.onNodeWithTag("card_3").assertIsDisplayed()
        
        composeTestRule.onNodeWithText("并发测试1").assertIsDisplayed()
        composeTestRule.onNodeWithText("并发测试2").assertIsDisplayed()
        composeTestRule.onNodeWithText("并发测试3").assertIsDisplayed()
    }

    /**
     * 🔥 【设计系统合规】Card样式和间距的Design System合规验证
     */
    @Test
    fun testDesignSystemCompliance_CardStyling() {
        // Given
        val segment = createTestSegmentUi("design-test", title = "设计系统测试", content = "样式内容")
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = segment,
                    modifier = Modifier.testTag(THINKING_STAGE_CARD_TAG)
                )
            }
        }
        
        composeTestRule.waitForIdle()
        
        // When & Then - 验证Design System合规性
        composeTestRule.onNodeWithTag(THINKING_STAGE_CARD_TAG).assertIsDisplayed()
        
        // 验证内容显示（间接验证样式应用正确）
        composeTestRule.onNodeWithText("设计系统测试").assertIsDisplayed()
        composeTestRule.onNodeWithText("样式内容").assertIsDisplayed()
        
        // Card应该有合理的尺寸和显示效果
        composeTestRule.onNodeWithTag(THINKING_STAGE_CARD_TAG)
            .assertHeightIsAtMost(400.dp) // 合理的最大高度
    }

    /**
     * 🔥 【错误处理】异常场景的健壮性验证
     */
    @Test
    fun testErrorHandling_Robustness() = runTest {
        // Test Case 1: 异常长度的ID
        val longIdSegment = createTestSegmentUi(
            id = "very-long-segment-id-that-might-cause-issues-in-some-scenarios",
            title = "长ID测试",
            content = "内容"
        )
        
        var longIdRendered = false
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = longIdSegment,
                    modifier = Modifier.testTag("long_id_card"),
                    onSegmentRendered = { longIdRendered = true }
                )
            }
        }
        
        delay(600)
        composeTestRule.waitForIdle()
        
        assertTrue(longIdRendered, "长ID段应该正常处理")
        composeTestRule.onNodeWithTag("long_id_card").assertIsDisplayed()
        
        // Test Case 2: 特殊字符内容
        val specialCharSegment = createTestSegmentUi(
            "special-char-test",
            title = "特殊字符: !@#$%^&*()",
            content = "内容包含特殊字符: <>&\"'\n\t换行和制表符"
        )
        
        var specialCharRendered = false
        
        composeTestRule.setContent {
            GymBroTheme {
                ThinkingStageCard(
                    segment = specialCharSegment,
                    modifier = Modifier.testTag("special_char_card"),
                    onSegmentRendered = { specialCharRendered = true }
                )
            }
        }
        
        delay(600)
        composeTestRule.waitForIdle()
        
        assertTrue(specialCharRendered, "特殊字符段应该正常处理")
        composeTestRule.onNodeWithTag("special_char_card").assertIsDisplayed()
    }
}