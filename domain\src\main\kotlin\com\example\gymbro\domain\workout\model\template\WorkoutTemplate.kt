package com.example.gymbro.domain.workout.model.template

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.*

/**
 * Workout template domain model
 * Represents a reusable workout plan template
 *
 * @property id Template unique identifier
 * @property name Template name
 * @property description Template description, optional
 * @property userId Creator user ID
 * @property exercises List of included workout exercises
 * @property targetMuscleGroups List of target muscle groups, optional
 * @property estimatedDuration Estimated duration (minutes), optional
 * @property difficulty Difficulty level (1-5), optional
 * @property tags List of tags, optional
 * @property imageUrl Image URL, optional
 * @property isFavorite Whether it is a favorite
 * @property isPublic Whether it is public
 * @property usageCount Usage count
 * @property createdAt Creation timestamp
 * @property updatedAt Update timestamp
 * @property category Workout category, optional
 * @property lastUsedDate Last used date, optional
 *
 * === Phase1版本控制字段 ===
 * @property currentVersion 当前版本号
 * @property isDraft 是否为草稿状态
 * @property isPublished 是否已发布
 * @property lastPublishedAt 最后发布时间
 */
@Serializable
data class WorkoutTemplate(
    @SerialName("id") val id: String = UUID.randomUUID().toString(),
    @SerialName("name") val name: String,
    @SerialName("description") val description: String? = null,
    @SerialName("user_id") val userId: String,
    @SerialName("exercises") val exercises: List<TemplateExercise> = emptyList(),
    @SerialName("target_muscle_groups") val targetMuscleGroups: List<String>? = null,
    @SerialName("estimated_duration") val estimatedDuration: Int? = null,
    @SerialName("difficulty") val difficulty: Int? = null,
    @SerialName("tags") val tags: List<String>? = null,
    @SerialName("image_url") val imageUrl: String? = null,
    @SerialName("is_favorite") val isFavorite: Boolean = false,
    @SerialName("is_public") val isPublic: Boolean = false,
    @SerialName("usage_count") val usageCount: Int = 0,
    @SerialName("created_at") val createdAt: Long = System.currentTimeMillis(),
    @SerialName("updated_at") val updatedAt: Long = System.currentTimeMillis(),
    @SerialName("category") val category: String? = null,
    @SerialName("last_used_date") val lastUsedDate: Long? = null,

    // === Phase1版本控制字段 (与Shared Models同步) ===
    /**
     * 当前版本号（Phase1: TemplateVersion功能）
     */
    @SerialName("current_version") val currentVersion: Int = 1,

    /**
     * 是否为草稿状态（Phase1: TemplateVersion功能）
     */
    @SerialName("is_draft") val isDraft: Boolean = true,

    /**
     * 是否已发布（Phase1: TemplateVersion功能）
     */
    @SerialName("is_published") val isPublished: Boolean = false,

    /**
     * 最后发布时间（Phase1: TemplateVersion功能）
     */
    val lastPublishedAt: Long? = null,
) {
    init {
        require(name.isNotBlank()) { "Template name cannot be blank" }
        require(userId.isNotBlank()) { "User ID cannot be blank" }

        if (difficulty != null) {
            require(difficulty in 1..5) { "Difficulty level must be between 1 and 5" }
        }

        // 修复：兼容旧数据，确保estimatedDuration符合验证规则
        // 🔥 关键修复：放宽验证规则，允许0值，只禁止负数
        if (estimatedDuration != null) {
            require(estimatedDuration >= 0) { "Estimated duration cannot be negative" }
        }

        require(currentVersion >= 1) { "Current version must be greater than 0" }
    }

    /**
     * Get formatted string for primary muscles
     * @return Formatted muscle list
     */
    fun getPrimaryMusclesFormatted(): String {
        return if (targetMuscleGroups.isNullOrEmpty()) {
            "未指定"
        } else {
            targetMuscleGroups.joinToString(", ")
        }
    }

    /**
     * Get difficulty stars
     * @return Star string
     */
    fun getDifficultyStars(): String {
        val actualDifficulty = difficulty ?: 1
        return "★".repeat(actualDifficulty) + "☆".repeat(5 - actualDifficulty)
    }

    /**
     * Get formatted duration
     * @return Formatted duration string
     */
    fun getFormattedDuration(): String {
        val duration = estimatedDuration ?: 0
        return if (duration <= 0) {
            "未知"
        } else if (duration < 60) {
            "${duration}分钟"
        } else {
            val hours = duration / 60
            val minutes = duration % 60
            if (minutes == 0) {
                "${hours}小时"
            } else {
                "${hours}小时${minutes}分钟"
            }
        }
    }

    // === Phase1版本控制扩展方法 ===

    /**
     * 检查是否需要发布新版本
     */
    fun needsVersionPublish(): Boolean = isDraft && !isPublished

    /**
     * 获取版本状态描述
     */
    fun getVersionStatusText(): String = when {
        isDraft && !isPublished -> "草稿 v$currentVersion"
        !isDraft && isPublished -> "已发布 v$currentVersion"
        else -> "v$currentVersion"
    }

    /**
     * 创建发布版本的副本
     */
    fun toPublishedVersion(): WorkoutTemplate = copy(
        isDraft = false,
        isPublished = true,
        lastPublishedAt = System.currentTimeMillis(),
        updatedAt = System.currentTimeMillis(),
    )

    /**
     * 创建草稿版本的副本
     */
    fun toDraftVersion(newVersion: Int = currentVersion + 1): WorkoutTemplate = copy(
        currentVersion = newVersion,
        isDraft = true,
        isPublished = false,
        lastPublishedAt = lastPublishedAt, // 保留上次发布时间
        updatedAt = System.currentTimeMillis(),
    )

    companion object {
        /**
         * Create an empty workout template
         * @param userId User ID
         * @return Empty workout template object
         */
        fun empty(userId: String): WorkoutTemplate {
            return WorkoutTemplate(
                name = "训练模板", // 🔥 修复：统一使用"训练模板"作为默认名称
                description = "无描述",
                userId = userId,
                targetMuscleGroups = emptyList(),
                difficulty = 1,
                estimatedDuration = 30, // 修复：设置为30分钟，符合验证要求 > 0
                exercises = emptyList(),
                tags = emptyList(),
                // Phase1版本控制默认值
                currentVersion = 1,
                isDraft = true,
                isPublished = false,
                lastPublishedAt = null,
            )
        }

        /**
         * 创建兼容旧数据的WorkoutTemplate
         * 自动修复不符合验证规则的字段
         */
        fun createCompatible(
            id: String = UUID.randomUUID().toString(),
            name: String,
            description: String? = null,
            userId: String,
            exercises: List<TemplateExercise> = emptyList(),
            targetMuscleGroups: List<String>? = null,
            estimatedDuration: Int? = null,
            difficulty: Int? = null,
            tags: List<String>? = null,
            imageUrl: String? = null,
            isFavorite: Boolean = false,
            isPublic: Boolean = false,
            usageCount: Int = 0,
            createdAt: Long = System.currentTimeMillis(),
            updatedAt: Long = System.currentTimeMillis(),
            category: String? = null,
            lastUsedDate: Long? = null,
            currentVersion: Int = 1,
            isDraft: Boolean = true,
            isPublished: Boolean = false,
            lastPublishedAt: Long? = null,
        ): WorkoutTemplate {
            return WorkoutTemplate(
                id = id,
                name = name.takeIf { it.isNotBlank() } ?: "训练模板", // 🔥 修复：统一使用"训练模板"作为默认名称
                description = description,
                userId = userId.takeIf { it.isNotBlank() } ?: "unknown_user", // 修复空用户ID
                exercises = exercises,
                targetMuscleGroups = targetMuscleGroups,
                estimatedDuration = when {
                    estimatedDuration == null -> 30 // null -> 30分钟
                    estimatedDuration <= 0 -> 30 // 0或负数 -> 30分钟
                    else -> estimatedDuration // 保持原值
                },
                difficulty = difficulty?.coerceIn(1, 5), // 限制难度范围
                tags = tags,
                imageUrl = imageUrl,
                isFavorite = isFavorite,
                isPublic = isPublic,
                usageCount = usageCount,
                createdAt = createdAt,
                updatedAt = updatedAt,
                category = category,
                lastUsedDate = lastUsedDate,
                currentVersion = currentVersion.coerceAtLeast(1), // 确保版本号 >= 1
                isDraft = isDraft,
                isPublished = isPublished,
                lastPublishedAt = lastPublishedAt,
            )
        }
    }
}
