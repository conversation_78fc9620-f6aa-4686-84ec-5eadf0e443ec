package com.example.gymbro.core.network.buffer

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import timber.log.Timber
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 性能监控实现 - 实时性能指标收集和分析
 *
 * 设计目标：
 * - 实时收集处理性能指标
 * - 支持自适应缓冲决策
 * - 提供性能趋势分析
 * - 轻量级，低开销监控
 */
@Singleton
class PerformanceMonitorImpl @Inject constructor() : PerformanceMonitor {

    companion object {
        private const val TAG = "PerformanceMonitor"
        private const val METRICS_WINDOW_SIZE = 100  // 保留最近100个指标样本
        private const val MEMORY_CHECK_INTERVAL_MS = 1000L  // 内存检查间隔1秒
    }

    // 性能指标状态
    private val _currentMetrics = MutableStateFlow(createDefaultMetrics())
    private val currentMetrics: StateFlow<ProcessingMetrics> = _currentMetrics.asStateFlow()

    // 统计计数器
    private val tokenProcessedCount = AtomicLong(0)
    private val totalLatencyMs = AtomicLong(0)
    private val errorCount = AtomicLong(0)
    private val bufferAdjustmentCount = AtomicLong(0)

    // 性能历史记录（滑动窗口）
    private val metricsHistory = ArrayDeque<ProcessingMetrics>(METRICS_WINDOW_SIZE)
    private val latencyHistory = ArrayDeque<Long>(METRICS_WINDOW_SIZE)

    // 时间戳记录
    private var lastMetricsUpdateTime = System.currentTimeMillis()
    private var lastMemoryCheckTime = 0L
    private var monitoringStartTime = System.currentTimeMillis()

    override fun recordBufferAdjustment(oldSize: Int, newSize: Int, reason: String) {
        bufferAdjustmentCount.incrementAndGet()

        Timber.tag(TAG).i("📊 缓冲区调整: $oldSize → $newSize, 原因: $reason, " +
                "总调整次数: ${bufferAdjustmentCount.get()}")
    }

    override fun recordMetrics(metrics: ProcessingMetrics) {
        synchronized(this) {
            // 更新当前指标
            _currentMetrics.value = metrics

            // 添加到历史记录
            if (metricsHistory.size >= METRICS_WINDOW_SIZE) {
                metricsHistory.removeFirst()
            }
            metricsHistory.addLast(metrics)

            lastMetricsUpdateTime = System.currentTimeMillis()
        }

        // 记录详细日志（简化版本，移除isLoggable检查）
        Timber.tag(TAG).v("📊 性能指标更新: " +
                "tokens/s=${metrics.tokensPerSecond}, " +
                "network=${metrics.networkThroughput}, " +
                "memory=${String.format("%.1f", metrics.memoryUsagePercent * 100)}%, " +
                "buffer=${String.format("%.1f", metrics.bufferUtilization * 100)}%, " +
                "latency=${metrics.avgLatencyMs}ms, " +
                "error_rate=${String.format("%.2f", metrics.errorRate * 100)}%")
    }

    override suspend fun getCurrentMetrics(): ProcessingMetrics {
        val currentTime = System.currentTimeMillis()

        // 如果距离上次更新超过1秒，重新计算指标
        if (currentTime - lastMetricsUpdateTime > 1000) {
            val calculatedMetrics = calculateCurrentMetrics()
            recordMetrics(calculatedMetrics)
            return calculatedMetrics
        }

        return _currentMetrics.value
    }

    /**
     * 记录Token处理延迟
     */
    fun recordTokenLatency(latencyMs: Long) {
        tokenProcessedCount.incrementAndGet()
        totalLatencyMs.addAndGet(latencyMs)

        synchronized(this) {
            if (latencyHistory.size >= METRICS_WINDOW_SIZE) {
                latencyHistory.removeFirst()
            }
            latencyHistory.addLast(latencyMs)
        }
    }

    /**
     * 记录处理错误
     */
    fun recordError(errorType: String, errorMessage: String) {
        errorCount.incrementAndGet()

        Timber.tag(TAG).w("📊 处理错误: type=$errorType, message=$errorMessage, " +
                "总错误数: ${errorCount.get()}")
    }

    /**
     * 获取性能统计摘要
     */
    fun getPerformanceSummary(): PerformanceSummary {
        val currentTime = System.currentTimeMillis()
        val uptimeMs = currentTime - monitoringStartTime
        val totalTokens = tokenProcessedCount.get()
        val totalErrors = errorCount.get()
        val totalAdjustments = bufferAdjustmentCount.get()

        val avgLatency = if (totalTokens > 0) {
            totalLatencyMs.get() / totalTokens
        } else {
            0L
        }

        val throughput = if (uptimeMs > 0) {
            (totalTokens * 1000.0 / uptimeMs).toFloat()
        } else {
            0f
        }

        val errorRate = if (totalTokens > 0) {
            (totalErrors.toFloat() / totalTokens)
        } else {
            0f
        }

        return PerformanceSummary(
            uptimeMs = uptimeMs,
            totalTokensProcessed = totalTokens,
            totalErrors = totalErrors,
            totalBufferAdjustments = totalAdjustments,
            avgLatencyMs = avgLatency,
            overallThroughput = throughput,
            overallErrorRate = errorRate,
            currentMetrics = _currentMetrics.value
        )
    }

    /**
     * 重置所有统计数据
     */
    fun reset() {
        tokenProcessedCount.set(0)
        totalLatencyMs.set(0)
        errorCount.set(0)
        bufferAdjustmentCount.set(0)

        synchronized(this) {
            metricsHistory.clear()
            latencyHistory.clear()
        }

        monitoringStartTime = System.currentTimeMillis()
        lastMetricsUpdateTime = monitoringStartTime

        _currentMetrics.value = createDefaultMetrics()

        Timber.tag(TAG).i("🔄 性能监控重置完成")
    }

    /**
     * 计算当前性能指标
     */
    private fun calculateCurrentMetrics(): ProcessingMetrics {
        val currentTime = System.currentTimeMillis()
        val timeDeltaMs = currentTime - lastMetricsUpdateTime
        val timeDeltaSeconds = timeDeltaMs / 1000.0f

        // 计算Token处理速度
        val recentTokens = tokenProcessedCount.get()
        val tokensPerSecond = if (timeDeltaSeconds > 0) {
            recentTokens / timeDeltaSeconds
        } else {
            0f
        }

        // 计算平均延迟
        val avgLatency = synchronized(this) {
            if (latencyHistory.isNotEmpty()) {
                latencyHistory.average().toLong()
            } else {
                0L
            }
        }

        // 获取内存使用情况
        val memoryUsage = getMemoryUsagePercent()

        // 计算缓冲区利用率（简化实现）
        val bufferUtilization = 0.5f // TODO: 从实际缓冲区获取

        // 计算网络吞吐量（估算）
        val networkThroughput = tokensPerSecond * 1.1f // 假设网络略快于处理

        // 计算错误率
        val totalTokens = tokenProcessedCount.get()
        val errorRate = if (totalTokens > 0) {
            errorCount.get().toFloat() / totalTokens
        } else {
            0f
        }

        return ProcessingMetrics(
            tokensPerSecond = tokensPerSecond,
            networkThroughput = networkThroughput,
            memoryUsagePercent = memoryUsage,
            bufferUtilization = bufferUtilization,
            avgLatencyMs = avgLatency,
            errorRate = errorRate
        )
    }

    /**
     * 获取内存使用百分比
     */
    private fun getMemoryUsagePercent(): Float {
        val currentTime = System.currentTimeMillis()

        // 限制内存检查频率
        if (currentTime - lastMemoryCheckTime < MEMORY_CHECK_INTERVAL_MS) {
            return _currentMetrics.value.memoryUsagePercent
        }

        lastMemoryCheckTime = currentTime

        return try {
            val runtime = Runtime.getRuntime()
            val totalMemory = runtime.totalMemory()
            val freeMemory = runtime.freeMemory()
            val usedMemory = totalMemory - freeMemory
            val maxMemory = runtime.maxMemory()

            (usedMemory.toFloat() / maxMemory).coerceIn(0f, 1f)
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "获取内存使用情况失败")
            0.5f // 默认值
        }
    }

    /**
     * 创建默认性能指标
     */
    private fun createDefaultMetrics(): ProcessingMetrics {
        return ProcessingMetrics(
            tokensPerSecond = 0f,
            networkThroughput = 0f,
            memoryUsagePercent = 0.5f,
            bufferUtilization = 0f,
            avgLatencyMs = 0L,
            errorRate = 0f
        )
    }
}

/**
 * 📊 性能统计摘要
 */
data class PerformanceSummary(
    val uptimeMs: Long,                     // 运行时间
    val totalTokensProcessed: Long,         // 总处理Token数
    val totalErrors: Long,                  // 总错误数
    val totalBufferAdjustments: Long,       // 总缓冲调整次数
    val avgLatencyMs: Long,                 // 平均延迟
    val overallThroughput: Float,           // 总体吞吐量
    val overallErrorRate: Float,            // 总体错误率
    val currentMetrics: ProcessingMetrics   // 当前指标
)
