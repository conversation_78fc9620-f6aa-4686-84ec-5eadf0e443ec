# 🔧 Step 3: PromptBuilder唯一化 - Detekt规则
# 
# 使用标准 Detekt 规则检查 core-network 模块

style:
  # 禁止通配符导入，确保明确的依赖关系
  WildcardImport:
    active: true
    excludeImports:
      - 'java.util.*'
    excludes: ['**/test/**', '**/androidTest/**']

  # 确保使用 val 而不是 var
  VarCouldBeVal:
    active: true
    ignoreLateinitVar: false
    excludes: ['**/test/**', '**/androidTest/**']

naming:
  # 确保网络配置类命名规范
  ClassNaming:
    active: true
    classPattern: '[A-Z][a-zA-Z0-9]*'
    excludes: ['**/test/**', '**/androidTest/**']

  # 函数命名规范
  FunctionNaming:
    active: true
    functionPattern: '[a-z][a-zA-Z0-9]*'
    excludeClassPattern: '$^'
    ignoreAnnotated: ['Composable']
    excludes: ['**/test/**', '**/androidTest/**']

complexity:
  # 限制方法复杂度
  CyclomaticComplexMethod:
    active: true
    threshold: 15
    ignoreSingleWhenExpression: true
    excludes: ['**/test/**', '**/androidTest/**']
  
  # 限制长方法
  LongMethod:
    active: true
    threshold: 80
    excludes: ['**/test/**', '**/androidTest/**']

  # 限制大类
  LargeClass:
    active: true
    threshold: 500
    excludes: ['**/test/**', '**/androidTest/**']

# 确保正确的依赖注入使用
coroutines:
  # 检查协程作用域使用
  GlobalCoroutineUsage:
    active: true
    excludes: ['**/test/**', '**/androidTest/**']

  # 确保注入合适的 Dispatcher
  InjectDispatcher:
    active: true
    dispatcherNames:
      - 'IO'
      - 'Default'
      - 'Unconfined'
    excludes: ['**/test/**', '**/androidTest/**']
