package com.example.gymbro.domain.workout.model.plan

import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.shared.models.workout.PlanProgressStatus
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.*

/**
 * 日训练计划领域模型 - v3.1 进度跟踪增强版
 *
 * 表示训练计划中特定一天的训练安排
 * 🎯 支持一天多个Template调度，进度跟踪，强制使用TemplateVersion
 *
 * @property dayNumber 天数（从1开始）
 * @property templateVersionIds 调度的TemplateVersion ID列表（强制使用）
 * @property isRestDay 是否为休息日
 * @property dayNotes 该天的训练备注
 * @property isCompleted 是否已完成（兼容性字段）
 * @property progress 训练进度状态（PlanProgressStatus）
 */
@Serializable
data class DayPlan(
    @SerialName("day_number") val dayNumber: Int,
    @SerialName("template_version_ids") val templateVersionIds: List<String> = emptyList(),
    @SerialName("is_rest_day") val isRestDay: Boolean = false,
    @SerialName("day_notes") val dayNotes: UiText? = null,
    @SerialName("is_completed") val isCompleted: Boolean = false,
    @SerialName("progress") val progress: PlanProgressStatus = PlanProgressStatus.NOT_STARTED,
) {
    /**
     * 检查该天是否有训练安排
     */
    fun hasWorkout(): Boolean = templateVersionIds.isNotEmpty() && !isRestDay

    /**
     * 获取训练模板数量
     */
    fun getTemplateCount(): Int = if (isRestDay) 0 else templateVersionIds.size

    /**
     * 检查是否为有效的训练日
     */
    fun isValidWorkoutDay(): Boolean = !isRestDay && templateVersionIds.isNotEmpty()

    /**
     * 转换为Function Call格式
     */
    fun toFunctionCallDay(): com.example.gymbro.shared.models.workout.PlanFunctionCallDay {
        return com.example.gymbro.shared.models.workout.PlanFunctionCallDay(
            dayNumber = dayNumber,
            isRestDay = isRestDay,
            templateIds = templateVersionIds,
            notes = dayNotes?.toString(),
        )
    }

    /**
     * 转换为Calendar Entry格式
     */
    fun toCalendarEntry(
        date: String,
        isCompleted: Boolean = this.isCompleted,
    ): com.example.gymbro.shared.models.workout.CalendarEntryData {
        return com.example.gymbro.shared.models.workout.CalendarEntryData(
            date = date,
            dayNumber = dayNumber,
            isRestDay = isRestDay,
            templateIds = templateVersionIds,
            workoutCount = getTemplateCount(),
            notes = dayNotes?.toString(),
            estimatedDuration = null,
            isCompleted = isCompleted,
        )
    }

    companion object {
        /**
         * 创建休息日
         */
        fun createRestDay(
            dayNumber: Int,
            notes: UiText? = null,
        ): DayPlan = DayPlan(
            dayNumber = dayNumber,
            templateVersionIds = emptyList(),
            isRestDay = true,
            dayNotes = notes,
            isCompleted = false,
            progress = PlanProgressStatus.NOT_STARTED,
        )

        /**
         * 创建训练日
         */
        fun createWorkoutDay(
            dayNumber: Int,
            templateVersionIds: List<String>,
            notes: UiText? = null,
            isCompleted: Boolean = false,
            progress: PlanProgressStatus = PlanProgressStatus.NOT_STARTED,
        ): DayPlan = DayPlan(
            dayNumber = dayNumber,
            templateVersionIds = templateVersionIds,
            isRestDay = false,
            dayNotes = notes,
            isCompleted = isCompleted,
            progress = progress,
        )
    }
}
