package com.example.gymbro.features.thinkingbox.domain.model

import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * Segment 单元测试
 *
 * 🎯 测试覆盖：
 * - 不可变性验证
 * - 工厂方法测试
 * - 状态转换方法测试
 * - 生命周期状态查询方法测试
 */
@DisplayName("Segment 领域模型测试")
class SegmentTest {

    @Nested
    @DisplayName("工厂方法测试")
    inner class FactoryMethodTests {

        @Test
        @DisplayName("createPerthink应该创建正确的PERTHINK段")
        fun `createPerthink should create correct PERTHINK segment`() {
            // When
            val segment = Segment.createPerthink("初始思考内容")

            // Then
            assertEquals("perthink", segment.id)
            assertEquals(SegmentKind.PERTHINK, segment.kind)
            assertEquals("<PERSON><PERSON> is thinking", segment.title)
            assertEquals("初始思考内容", segment.content)
            assertFalse(segment.closed)
            assertFalse(segment.rendered)
        }

        @Test
        @DisplayName("createPhase应该创建正确的PHASE段")
        fun `createPhase should create correct PHASE segment`() {
            // When
            val segment = Segment.createPhase("phase-1", "分析阶段", "分析内容")

            // Then
            assertEquals("phase-1", segment.id)
            assertEquals(SegmentKind.PHASE, segment.kind)
            assertEquals("分析阶段", segment.title)
            assertEquals("分析内容", segment.content)
            assertFalse(segment.closed)
            assertFalse(segment.rendered)
        }

        @Test
        @DisplayName("createFinalPhase应该创建正确的FINAL_PHASE段")
        fun `createFinalPhase should create correct FINAL_PHASE segment`() {
            // When
            val segment = Segment.createFinalPhase()

            // Then
            assertEquals("final-phase", segment.id)
            assertEquals(SegmentKind.FINAL_PHASE, segment.kind)
            assertEquals("Final Answer", segment.title)
            assertEquals("", segment.content)
            assertTrue(segment.closed) // final-phase立即闭合
            assertFalse(segment.rendered)
        }
    }

    @Nested
    @DisplayName("不可变性测试")
    inner class ImmutabilityTests {

        @Test
        @DisplayName("appendContent应该返回新实例而不修改原实例")
        fun `appendContent should return new instance without modifying original`() {
            // Given
            val original = Segment.createPerthink("原始内容")

            // When
            val updated = original.appendContent("追加内容")

            // Then
            assertEquals("原始内容", original.content)
            assertEquals("原始内容追加内容", updated.content)
            assertEquals(original.id, updated.id)
            assertEquals(original.kind, updated.kind)
        }

        @Test
        @DisplayName("markClosed应该返回新实例而不修改原实例")
        fun `markClosed should return new instance without modifying original`() {
            // Given
            val original = Segment.createPerthink("内容")

            // When
            val closed = original.markClosed()

            // Then
            assertFalse(original.closed)
            assertTrue(closed.closed)
            assertEquals(original.content, closed.content)
        }

        @Test
        @DisplayName("markRendered应该返回新实例而不修改原实例")
        fun `markRendered should return new instance without modifying original`() {
            // Given
            val original = Segment.createPerthink("内容")

            // When
            val rendered = original.markRendered()

            // Then
            assertFalse(original.rendered)
            assertTrue(rendered.rendered)
            assertEquals(original.content, rendered.content)
        }
    }

    @Nested
    @DisplayName("状态查询方法测试")
    inner class StateQueryTests {

        @Test
        @DisplayName("isEmpty应该正确判断内容是否为空")
        fun `isEmpty should correctly check if content is empty`() {
            // Given
            val emptySegment = Segment.createPerthink("")
            val nonEmptySegment = Segment.createPerthink("有内容")

            // Then
            assertTrue(emptySegment.isEmpty())
            assertFalse(nonEmptySegment.isEmpty())
        }

        @Test
        @DisplayName("isReadyForRender应该基于closed状态")
        fun `isReadyForRender should be based on closed state`() {
            // Given
            val openSegment = Segment.createPerthink("内容")
            val closedSegment = openSegment.markClosed()

            // Then
            assertFalse(openSegment.isReadyForRender())
            assertTrue(closedSegment.isReadyForRender())
        }

        @Test
        @DisplayName("canBeRemoved应该基于rendered状态")
        fun `canBeRemoved should be based on rendered state`() {
            // Given
            val unrenderedSegment = Segment.createPerthink("内容")
            val renderedSegment = unrenderedSegment.markRendered()

            // Then
            assertFalse(unrenderedSegment.canBeRemoved())
            assertTrue(renderedSegment.canBeRemoved())
        }

        @Test
        @DisplayName("getTextLength应该返回正确的内容长度")
        fun `getTextLength should return correct content length`() {
            // Given
            val segment = Segment.createPerthink("测试内容123")

            // Then
            assertEquals(6, segment.getTextLength()) // 6个字符
        }

        @Test
        @DisplayName("getTextContent应该返回内容字符串")
        fun `getTextContent should return content string`() {
            // Given
            val content = "测试获取内容方法"
            val segment = Segment.createPerthink(content)

            // Then
            assertEquals(content, segment.getTextContent())
        }
    }

    @Nested
    @DisplayName("链式操作测试")
    inner class ChainedOperationTests {

        @Test
        @DisplayName("应该支持链式的不可变操作")
        fun `should support chained immutable operations`() {
            // Given
            val original = Segment.createPerthink("起始")

            // When
            val result = original
                .appendContent("追加1")
                .appendContent("追加2")
                .markClosed()
                .markRendered()

            // Then
            assertEquals("起始", original.content) // 原实例不变
            assertEquals("起始追加1追加2", result.content)
            assertTrue(result.closed)
            assertTrue(result.rendered)
        }
    }

    @Nested
    @DisplayName("边界情况测试")
    inner class EdgeCaseTests {

        @Test
        @DisplayName("空字符串追加应该正常工作")
        fun `empty string append should work correctly`() {
            // Given
            val segment = Segment.createPerthink("原内容")

            // When
            val result = segment.appendContent("")

            // Then
            assertEquals("原内容", result.content)
        }

        @Test
        @DisplayName("大量内容追加应该正常工作")
        fun `large content append should work correctly`() {
            // Given
            val segment = Segment.createPerthink("起始")
            val largeContent = "a".repeat(10000)

            // When
            val result = segment.appendContent(largeContent)

            // Then
            assertEquals("起始" + largeContent, result.content)
            assertEquals(10004, result.getTextLength())
        }

        @Test
        @DisplayName("特殊字符内容应该正确处理")
        fun `special characters should be handled correctly`() {
            // Given
            val specialContent = "包含\n换行\t制表符\"引号'单引号的内容"
            val segment = Segment.createPerthink(specialContent)

            // Then
            assertEquals(specialContent, segment.getTextContent())
            assertEquals(specialContent.length, segment.getTextLength())
        }
    }
}