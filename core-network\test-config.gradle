// 🚀 Core-Network 测试配置
// 用于运行新架构组件的单元测试

apply plugin: 'kotlin-android'

android {
    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
            
            // 🚀 测试JVM参数优化
            all {
                jvmArgs '-XX:MaxMetaspaceSize=512m'
                jvmArgs '-Xmx2g'
                
                // 🚀 启用并行测试执行
                maxParallelForks = Runtime.runtime.availableProcessors().intdiv(2) ?: 1
                
                // 🚀 测试超时配置
                timeout = Duration.ofMinutes(10)
                
                // 🚀 测试报告配置
                reports {
                    html.enabled = true
                    junitXml.enabled = true
                }
                
                // 🚀 测试日志配置
                testLogging {
                    events "passed", "skipped", "failed", "standardOut", "standardError"
                    exceptionFormat "full"
                    showCauses true
                    showExceptions true
                    showStackTraces true
                }
                
                // 🚀 系统属性传递
                systemProperty 'junit.jupiter.execution.parallel.enabled', 'true'
                systemProperty 'junit.jupiter.execution.parallel.mode.default', 'concurrent'
                systemProperty 'junit.jupiter.execution.parallel.mode.classes.default', 'concurrent'
            }
        }
    }
}

dependencies {
    // 🚀 测试框架依赖
    testImplementation 'org.junit.jupiter:junit-jupiter:5.9.2'
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.9.2'
    testImplementation 'org.junit.jupiter:junit-jupiter-engine:5.9.2'
    testImplementation 'org.junit.jupiter:junit-jupiter-params:5.9.2'
    testImplementation 'org.junit.platform:junit-platform-suite:1.9.2'
    
    // 🚀 Kotlin协程测试
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3'
    
    // 🚀 Mock框架
    testImplementation 'io.mockk:mockk:1.13.8'
    testImplementation 'io.mockk:mockk-android:1.13.8'
    
    // 🚀 断言库
    testImplementation 'org.assertj:assertj-core:3.24.2'
    
    // 🚀 JSON测试支持
    testImplementation 'org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.0'
}

// 🚀 测试任务配置
tasks.withType(Test) {
    useJUnitPlatform()
    
    // 🚀 测试分类
    systemProperty 'junit.jupiter.tag.include', System.getProperty('test.tags', '')
    
    // 🚀 性能测试配置
    if (project.hasProperty('performance.test')) {
        systemProperty 'performance.test.enabled', 'true'
        timeout = Duration.ofMinutes(30) // 性能测试需要更长时间
    }
    
    // 🚀 集成测试配置
    if (project.hasProperty('integration.test')) {
        systemProperty 'integration.test.enabled', 'true'
        timeout = Duration.ofMinutes(20)
    }
}

// 🚀 自定义测试任务
task unitTestsOnly(type: Test) {
    description = '只运行单元测试'
    group = 'verification'
    
    useJUnitPlatform {
        excludeTags 'integration', 'performance'
    }
}

task performanceTests(type: Test) {
    description = '运行性能测试'
    group = 'verification'
    
    useJUnitPlatform {
        includeTags 'performance'
    }
    
    systemProperty 'performance.test.enabled', 'true'
    timeout = Duration.ofMinutes(30)
}

task integrationTests(type: Test) {
    description = '运行集成测试'
    group = 'verification'
    
    useJUnitPlatform {
        includeTags 'integration'
    }
    
    systemProperty 'integration.test.enabled', 'true'
    timeout = Duration.ofMinutes(20)
}

task coreNetworkTests(type: Test) {
    description = '运行Core-Network新架构测试套件'
    group = 'verification'
    
    useJUnitPlatform {
        includeClassNamePatterns '.*CoreNetworkTestSuite.*'
    }
    
    // 🚀 新架构测试专用配置
    systemProperty 'core.network.new.architecture', 'true'
    
    doFirst {
        println "🚀 开始运行Core-Network新架构测试套件"
        println "📊 测试范围: buffer, detector, receiver, processor"
        println "🎯 验证目标: 延迟减少90%, 架构简化60%"
    }
    
    doLast {
        println "✅ Core-Network新架构测试完成"
    }
}

// 🚀 测试覆盖率配置
apply plugin: 'jacoco'

jacoco {
    toolVersion = "0.8.8"
}

jacocoTestReport {
    dependsOn test
    
    reports {
        xml.enabled true
        html.enabled true
        csv.enabled false
    }
    
    // 🚀 覆盖率包含/排除配置
    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: [
                '**/di/**',           // 排除DI模块
                '**/testing/**',      // 排除测试工具
                '**/*Test*',          // 排除测试类
                '**/*Mock*',          // 排除Mock类
                '**/BuildConfig.*'    // 排除构建配置
            ])
        }))
    }
}

task jacocoTestCoverageVerification(type: JacocoCoverageVerification) {
    dependsOn jacocoTestReport
    
    violationRules {
        rule {
            limit {
                counter = 'LINE'
                value = 'COVEREDRATIO'
                minimum = 0.80 // 80%行覆盖率
            }
        }
        
        rule {
            limit {
                counter = 'BRANCH'
                value = 'COVEREDRATIO'
                minimum = 0.70 // 70%分支覆盖率
            }
        }
    }
}

// 🚀 测试报告聚合
task testReport(type: TestReport) {
    description = '生成聚合测试报告'
    group = 'verification'
    
    destinationDir = file("$buildDir/reports/allTests")
    reportOn test, unitTestsOnly, performanceTests, integrationTests
}

// 🚀 清理任务
task cleanTestReports(type: Delete) {
    delete "$buildDir/reports/tests"
    delete "$buildDir/reports/allTests"
    delete "$buildDir/jacoco"
}

clean.dependsOn cleanTestReports
