package com.example.gymbro.core.network.processor

import com.example.gymbro.core.network.detector.ContentType
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

/**
 * 🚀 StreamingProcessor 单元测试
 *
 * 测试目标：
 * - 验证不同内容类型的处理逻辑
 * - 测试即时处理性能
 * - 验证错误处理机制
 * - 测试输出净化功能
 */
class StreamingProcessorTest {

    private lateinit var contentExtractor: ContentExtractor
    private lateinit var outputSanitizer: OutputSanitizer
    private lateinit var streamingProcessor: StreamingProcessor

    @BeforeEach
    fun setup() {
        contentExtractor = mockk<ContentExtractor>(relaxed = true)
        outputSanitizer = mockk<OutputSanitizer>(relaxed = true)
        streamingProcessor = StreamingProcessorImpl(contentExtractor, outputSanitizer)
    }

    @Test
    fun `应该正确处理JSON SSE内容`() {
        // Given
        val token = "data: {\"choices\":[{\"delta\":{\"content\":\"Hello\"}}]}"
        val conversationId = "test-conversation"
        val extractedContent = "Hello"
        val sanitizedContent = "Hello"

        every { contentExtractor.extractJsonSseContent(token) } returns extractedContent
        every { outputSanitizer.sanitizeForDirectOutput(extractedContent) } returns sanitizedContent

        // When
        val result = streamingProcessor.processImmediate(token, ContentType.JSON_SSE, conversationId)

        // Then
        assertEquals(sanitizedContent, result)
        verify { contentExtractor.extractJsonSseContent(token) }
        verify { outputSanitizer.sanitizeForDirectOutput(extractedContent) }
    }

    @Test
    fun `应该正确处理JSON流内容`() {
        // Given
        val token = "{\"text\": \"Hello World\"}"
        val conversationId = "test-conversation"
        val extractedContent = "Hello World"
        val sanitizedContent = "Hello World"

        every { contentExtractor.extractJsonStreamContent(token) } returns extractedContent
        every { outputSanitizer.sanitizeForDirectOutput(extractedContent) } returns sanitizedContent

        // When
        val result = streamingProcessor.processImmediate(token, ContentType.JSON_STREAM, conversationId)

        // Then
        assertEquals(sanitizedContent, result)
        verify { contentExtractor.extractJsonStreamContent(token) }
        verify { outputSanitizer.sanitizeForDirectOutput(extractedContent) }
    }

    @Test
    fun `应该正确处理XML ThinkingBox内容`() {
        // Given
        val token = "<thinking>This is a thought</thinking>"
        val conversationId = "test-conversation"
        val sanitizedContent = "<thinking>This is a thought</thinking>"

        every { outputSanitizer.sanitizeForDirectOutput(token) } returns sanitizedContent

        // When
        val result = streamingProcessor.processImmediate(token, ContentType.XML_THINKING, conversationId)

        // Then
        assertEquals(sanitizedContent, result)
        verify { outputSanitizer.sanitizeForDirectOutput(token) }
        // 验证没有调用内容提取器（直接处理）
        verify(exactly = 0) { contentExtractor.extractJsonSseContent(any()) }
        verify(exactly = 0) { contentExtractor.extractJsonStreamContent(any()) }
    }

    @Test
    fun `应该正确处理WebSocket帧内容`() {
        // Given
        val token = "WS:frame_data"
        val conversationId = "test-conversation"
        val extractedContent = "frame_data"
        val sanitizedContent = "frame_data"

        every { contentExtractor.extractWebSocketContent(token) } returns extractedContent
        every { outputSanitizer.sanitizeForDirectOutput(extractedContent) } returns sanitizedContent

        // When
        val result = streamingProcessor.processImmediate(token, ContentType.WEBSOCKET_FRAME, conversationId)

        // Then
        assertEquals(sanitizedContent, result)
        verify { contentExtractor.extractWebSocketContent(token) }
        verify { outputSanitizer.sanitizeForDirectOutput(extractedContent) }
    }

    @Test
    fun `应该正确处理纯文本内容`() {
        // Given
        val token = "Plain text content"
        val conversationId = "test-conversation"
        val sanitizedContent = "Plain text content"

        every { outputSanitizer.sanitizeForDirectOutput(token) } returns sanitizedContent

        // When
        val result = streamingProcessor.processImmediate(token, ContentType.PLAIN_TEXT, conversationId)

        // Then
        assertEquals(sanitizedContent, result)
        verify { outputSanitizer.sanitizeForDirectOutput(token) }
    }

    @Test
    fun `应该处理内容提取器异常`() {
        // Given
        val token = "invalid json data"
        val conversationId = "test-conversation"

        every { contentExtractor.extractJsonSseContent(token) } throws RuntimeException("Parse error")

        // When
        val result = streamingProcessor.processImmediate(token, ContentType.JSON_SSE, conversationId)

        // Then
        assertEquals("", result, "异常时应该返回空字符串")
        verify { contentExtractor.extractJsonSseContent(token) }
    }

    @Test
    fun `应该处理输出净化器异常`() {
        // Given
        val token = "test content"
        val conversationId = "test-conversation"

        every { outputSanitizer.sanitizeForDirectOutput(token) } throws RuntimeException("Sanitize error")

        // When
        val result = streamingProcessor.processImmediate(token, ContentType.PLAIN_TEXT, conversationId)

        // Then
        assertEquals("", result, "异常时应该返回空字符串")
    }

    @Test
    fun `应该跳过空内容提取结果`() {
        // Given
        val token = "data: {\"invalid\": \"format\"}"
        val conversationId = "test-conversation"

        every { contentExtractor.extractJsonSseContent(token) } returns ""

        // When
        val result = streamingProcessor.processImmediate(token, ContentType.JSON_SSE, conversationId)

        // Then
        assertEquals("", result)
        verify { contentExtractor.extractJsonSseContent(token) }
        verify(exactly = 0) { outputSanitizer.sanitizeForDirectOutput(any()) }
    }

    @Test
    fun `处理时间应该在阈值内`() {
        // Given
        val token = "test content"
        val conversationId = "test-conversation"

        every { outputSanitizer.sanitizeForDirectOutput(token) } returns token

        // When
        val startTime = System.currentTimeMillis()
        streamingProcessor.processImmediate(token, ContentType.PLAIN_TEXT, conversationId)
        val endTime = System.currentTimeMillis()

        // Then
        val processingTime = endTime - startTime
        assertTrue(processingTime < 10, "处理时间应该小于10ms") // 宽松的阈值用于测试
    }

    @Test
    fun `ContentExtractorImpl应该正确提取JSON SSE内容`() {
        // Given
        val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }
        val extractor = ContentExtractorImpl(json)

        // When & Then - OpenAI格式
        val openAiToken = "data: {\"choices\":[{\"delta\":{\"content\":\"Hello\"}}]}"
        val openAiResult = extractor.extractJsonSseContent(openAiToken)
        assertEquals("Hello", openAiResult)

        // When & Then - Claude格式
        val claudeToken = "data: {\"delta\":{\"text\":\"World\"}}"
        val claudeResult = extractor.extractJsonSseContent(claudeToken)
        assertEquals("World", claudeResult)

        // When & Then - 通用格式
        val genericToken = "data: {\"content\":\"Test\"}"
        val genericResult = extractor.extractJsonSseContent(genericToken)
        assertEquals("Test", genericResult)

        // When & Then - 完成标记
        val doneToken = "data: [DONE]"
        val doneResult = extractor.extractJsonSseContent(doneToken)
        assertEquals("", doneResult)

        // When & Then - 非SSE格式
        val nonSseToken = "regular text"
        val nonSseResult = extractor.extractJsonSseContent(nonSseToken)
        assertEquals("", nonSseResult)
    }

    @Test
    fun `ContentExtractorImpl应该正确提取JSON流内容`() {
        // Given
        val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }
        val extractor = ContentExtractorImpl(json)

        // When & Then - text字段
        val textToken = "{\"text\": \"Hello World\"}"
        val textResult = extractor.extractJsonStreamContent(textToken)
        assertEquals("Hello World", textResult)

        // When & Then - message字段
        val messageToken = "{\"message\": \"Test Message\"}"
        val messageResult = extractor.extractJsonStreamContent(messageToken)
        assertEquals("Test Message", messageResult)

        // When & Then - content字段
        val contentToken = "{\"content\": \"Content Text\"}"
        val contentResult = extractor.extractJsonStreamContent(contentToken)
        assertEquals("Content Text", contentResult)

        // When & Then - 无效JSON
        val invalidToken = "invalid json"
        val invalidResult = extractor.extractJsonStreamContent(invalidToken)
        assertEquals("", invalidResult)
    }

    @Test
    fun `ContentExtractorImpl应该正确提取WebSocket内容`() {
        // Given
        val json = kotlinx.serialization.json.Json { ignoreUnknownKeys = true }
        val extractor = ContentExtractorImpl(json)

        // When & Then - WebSocket前缀
        val wsToken = "WS:frame_data_here"
        val wsResult = extractor.extractWebSocketContent(wsToken)
        assertEquals("frame_data_here", wsResult)

        // When & Then - 无前缀
        val plainToken = "plain_data"
        val plainResult = extractor.extractWebSocketContent(plainToken)
        assertEquals("plain_data", plainResult)
    }

    @Test
    fun `OutputSanitizerImpl应该正确净化输出`() {
        // Given
        val piiSanitizer = mockk<com.example.gymbro.core.network.security.PiiSanitizer>(relaxed = true)
        val sanitizer = OutputSanitizerImpl(piiSanitizer)
        val content = "Test content with PII"
        val sanitizedContent = "Test content with [REDACTED]"

        every { piiSanitizer.sanitizeContent(content) } returns sanitizedContent

        // When
        val result = sanitizer.sanitizeForDirectOutput(content)

        // Then
        assertEquals(sanitizedContent, result)
        verify { piiSanitizer.sanitizeContent(content) }
    }

    @Test
    fun `OutputSanitizerImpl应该处理空内容`() {
        // Given
        val piiSanitizer = mockk<com.example.gymbro.core.network.security.PiiSanitizer>(relaxed = true)
        val sanitizer = OutputSanitizerImpl(piiSanitizer)

        // When
        val result = sanitizer.sanitizeForDirectOutput("")

        // Then
        assertEquals("", result)
        verify(exactly = 0) { piiSanitizer.sanitizeContent(any()) }
    }
}
