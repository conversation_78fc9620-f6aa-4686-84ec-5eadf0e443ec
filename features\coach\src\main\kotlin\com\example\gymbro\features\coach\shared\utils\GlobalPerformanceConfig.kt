package com.example.gymbro.features.coach.shared.utils

import androidx.compose.ui.unit.dp
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.sample
import kotlin.time.Duration.Companion.milliseconds

/**
 * 全局性能配置 - 符合MVI 2.0黄金标准
 *
 * 统一管理所有性能相关的配置和优化策略，确保：
 * 1. 所有ViewModel使用相同的性能优化
 * 2. 避免重复的网络监控启动
 * 3. 统一的Flow背压处理
 * 4. 合理的延迟和节流配置
 *
 * 遵循规范：
 * - 网络监控防抖：2秒
 * - UI Flow背压：conflate()
 * - 网络Flow采样：1秒
 * - 启动延迟：500ms
 */
object GlobalPerformanceConfig {

    /**
     * 网络监控配置
     */
    object Network {
        /** 网络状态防抖时间 - 避免频繁状态变化 */
        const val DEBOUNCE_TIME_MS = 2000L

        /** 网络事件采样间隔 - 限制最高更新频率 */
        val SAMPLE_INTERVAL = 1000.milliseconds

        /** 是否启用网络监控性能日志 */
        const val ENABLE_PERFORMANCE_LOGGING = true
    }

    /**
     * ViewModel初始化配置
     */
    object ViewModel {
        /** 延迟初始化时间 - 避免启动时阻塞主线程 */
        const val DELAYED_INIT_MS = 500L

        /** 是否启用ViewModel性能监控 */
        const val ENABLE_PERFORMANCE_MONITORING = true

        /** 重组警告阈值 */
        const val RECOMPOSITION_WARNING_THRESHOLD = 10
    }

    /**
     * Flow优化配置
     */
    object Flow {
        /** UI Flow采样间隔 */
        val UI_SAMPLE_INTERVAL = 16.milliseconds // 一帧的时间

        /** 网络Flow采样间隔 */
        val NETWORK_SAMPLE_INTERVAL = 1000.milliseconds

        /** 流式响应采样间隔 */
        val STREAMING_SAMPLE_INTERVAL = 50.milliseconds
    }

    /**
     * 动画配置
     */
    object Animation {
        /** 最大并发动画数量 */
        const val MAX_CONCURRENT_ANIMATIONS = 3

        /** 动画节流间隔 */
        val THROTTLE_INTERVAL = 16.milliseconds

        /** 是否启用动画性能监控 */
        const val ENABLE_ANIMATION_MONITORING = true
    }

    /**
     * 搜索优化配置
     */
    object SEARCH {
        /** 搜索防抖时间 - 避免频繁搜索请求 */
        const val DEBOUNCE_MS = 300L

        /** 搜索结果缓存时间 */
        const val CACHE_TTL_MS = 5 * 60 * 1000L // 5分钟

        /** 最大搜索结果数量 */
        const val MAX_RESULTS = 50
    }

    /**
     * 输入优化配置
     */
    object Input {
        /** 大文本阈值 - 超过此长度的文本需要特殊优化 */
        const val LARGE_TEXT_THRESHOLD = 1000

        /** 基础防抖延迟 */
        const val BASE_DEBOUNCE_MS = 50L

        /** 短文本阈值 - 小于此长度的文本无需防抖 */
        const val SHORT_TEXT_THRESHOLD = 300

        /** 中等文本阈值 */
        const val MEDIUM_TEXT_THRESHOLD = 500

        /** 自适应延迟倍数 */
        const val ADAPTIVE_DELAY_MULTIPLIER_MEDIUM = 1.2f
        const val ADAPTIVE_DELAY_MULTIPLIER_LARGE = 1.5f
    }

    /**
     * 内存优化配置
     */
    object Memory {
        /** Flow缓存大小 */
        const val FLOW_CACHE_SIZE = 50

        /** 对象池大小 */
        const val OBJECT_POOL_SIZE = 20

        /** GC触发阈值（MB） */
        const val GC_THRESHOLD_MB = 100
    }

    /**
     * 调试日志控制配置 - 🔧 新增
     */
    object Debug {
        /** 是否启用Flow性能监控日志 */
        const val ENABLE_FLOW_MONITORING = false // 🔧 默认关闭，减少日志噪音

        /** 是否启用动画监控日志 */
        const val ENABLE_ANIMATION_MONITORING = false // 🔧 默认关闭

        /** 是否启用重组警告日志 */
        const val ENABLE_RECOMPOSITION_WARNINGS = true // 🔧 保持开启，但优化频率

        /** 是否启用网络监控日志 */
        const val ENABLE_NETWORK_MONITORING = false // 🔧 默认关闭

        /** 重组警告阈值 */
        const val RECOMPOSITION_WARNING_THRESHOLD = 20 // 🔧 提高阈值

        /** 是否在生产环境启用性能监控 */
        const val ENABLE_PRODUCTION_MONITORING = false
    }

    /**
     * 🔥 金属跃动效果配置 - 新增
     */
    object MetallicRing {
        /** 默认动画时长 */
        const val DEFAULT_ANIMATION_DURATION_MS = 3000L

        /** 最小动画时长 */
        const val MIN_ANIMATION_DURATION_MS = 1000L

        /** 最大动画时长 */
        const val MAX_ANIMATION_DURATION_MS = 8000L

        /** 默认阴影强度 */
        const val DEFAULT_SHADOW_INTENSITY = 0.3f

        /** 默认描边宽度 */
        val DEFAULT_STROKE_WIDTH = 2.dp

        /** 默认圆角半径 */
        val DEFAULT_CORNER_RADIUS = 12.dp

        /** 是否启用HDR效果 */
        const val ENABLE_HDR_BY_DEFAULT = false

        /** 重组监控阈值 */
        const val RECOMPOSITION_THRESHOLD = 15
    }

    /**
     * 🎯 液态玻璃优化配置 - 新增
     */
    object LiquidGlass {
        /** 是否启用复杂着色器 */
        const val ENABLE_COMPLEX_SHADERS = false // 🔧 默认关闭复杂着色器

        /** 是否启用动画效果 */
        const val ENABLE_ANIMATIONS = false // 🔧 默认关闭动画

        /** 是否启用交互效果 */
        const val ENABLE_INTERACTIONS = false // 🔧 默认关闭交互

        /** 最大模糊半径 */
        const val MAX_BLUR_RADIUS = 4f

        /** 最大扭曲强度 */
        const val MAX_DISTORTION_STRENGTH = 0.005f

        /** 最大色散强度 */
        const val MAX_CHROMATIC_ABERRATION = 0.005f

        /** 重组监控阈值 */
        const val RECOMPOSITION_THRESHOLD = 25
    }

    /**
     * 🧠 ThinkingBox配置 - 新增
     */
    object ThinkingBox {
        /** 打字机效果延迟 */
        const val TYPING_DELAY_MS = 33L // 33ms延迟，约30fps的流畅度

        /** 最大思考行数限制 */
        const val MAX_THINKING_LINES = 8

        /** 文本截断检查阈值 */
        const val TEXT_TRUNCATION_THRESHOLD = 10

        /** 段渲染防抖延迟 */
        const val SEGMENT_RENDER_DEBOUNCE_MS = 100L

        /** Token流处理防抖延迟 */
        const val TOKEN_STREAM_DEBOUNCE_MS = 50L

        /** 最大段队列大小 */
        const val MAX_SEGMENT_QUEUE_SIZE = 50

        /** 思考历史写入防抖延迟 */
        const val HISTORY_WRITE_DEBOUNCE_MS = 100L

        /** 是否启用文本截断安全检查 */
        const val ENABLE_TRUNCATION_SAFETY_CHECK = true

        /** textLayoutResult空值检查超时 */
        const val LAYOUT_RESULT_TIMEOUT_MS = 1000L
    }
}

/**
 * 全局Flow优化扩展函数
 */

/**
 * 应用UI Flow优化 - 统一的UI性能配置
 */
@OptIn(kotlinx.coroutines.FlowPreview::class)
fun <T> Flow<T>.applyUiOptimizations(): Flow<T> {
    return this
        .distinctUntilChanged()
        .conflate() // MVI 2.0规范：UI Flow使用conflate()
        .sample(GlobalPerformanceConfig.Flow.UI_SAMPLE_INTERVAL)
}

/**
 * 应用网络Flow优化 - 统一的网络性能配置
 */
@OptIn(kotlinx.coroutines.FlowPreview::class)
fun <T> Flow<T>.applyNetworkOptimizations(): Flow<T> {
    return this
        .distinctUntilChanged()
        .conflate()
        .sample(GlobalPerformanceConfig.Flow.NETWORK_SAMPLE_INTERVAL)
}

/**
 * 应用流式响应优化 - 统一的流式性能配置
 */
@OptIn(kotlinx.coroutines.FlowPreview::class)
fun <T> Flow<T>.applyStreamingOptimizations(): Flow<T> {
    return this
        .distinctUntilChanged()
        .conflate()
        .sample(GlobalPerformanceConfig.Flow.STREAMING_SAMPLE_INTERVAL)
}

/**
 * 性能监控标签
 */
object PerformanceTags {
    const val NETWORK_EVENTS = "NetworkEvents"
    const val AI_COACH_SCREEN = "AiCoachScreen"
    const val HISTORY_SCREEN = "HistoryScreen"
    const val NETWORK_BANNER = "NetworkBanner"
    const val STREAMING_CHAT = "StreamingChat"
    const val INPUT_DEBOUNCE = "InputDebounce" // 🎯 文本输入防抖监控
}
