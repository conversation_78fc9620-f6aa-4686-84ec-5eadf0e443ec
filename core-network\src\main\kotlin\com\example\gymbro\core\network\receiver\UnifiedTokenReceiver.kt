package com.example.gymbro.core.network.receiver

import com.example.gymbro.core.network.buffer.AdaptiveBufferManager
import com.example.gymbro.core.network.buffer.PerformanceMonitor
import com.example.gymbro.core.network.buffer.ProcessingMetrics
import com.example.gymbro.core.network.detector.ContentType
import com.example.gymbro.core.network.detector.DetectionResult
import com.example.gymbro.core.network.detector.ProgressiveProtocolDetector
import com.example.gymbro.core.network.logging.TokenLogCollector
import com.example.gymbro.core.network.processor.StreamingProcessor
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 统一Token接收器 - 新架构核心组件
 * 
 * 设计目标：
 * - 统一所有协议的Token接收入口
 * - 前100-200 token内完成快速协议识别
 * - 智能缓冲即时流式处理
 * - 直接输出到ThinkingBox，延迟<30ms
 * - 集成性能监控和自适应调整
 * - 🔥 【新增】集成RAW TOKEN批量日志采集
 */
@Singleton
class UnifiedTokenReceiver @Inject constructor(
    private val protocolDetector: ProgressiveProtocolDetector,
    private val streamingProcessor: StreamingProcessor,
    private val adaptiveBufferManager: AdaptiveBufferManager,
    private val performanceMonitor: PerformanceMonitor,
    private val tokenLogCollector: TokenLogCollector // 🔥 【新增】Token日志采集器
) {
    companion object {
        private const val TAG = "UnifiedTokenReceiver"
        
        // 性能监控配置
        private const val METRICS_UPDATE_INTERVAL_MS = 2000L
        private const val MAX_DETECTION_TOKENS = 200
        
        // 🔥 【RAW TOKEN日志采集】批量收集配置
        private const val TOKEN_BATCH_SIZE = 20  // 每20个token批量采集一次
        private const val TOKEN_COLLECT_TIMEOUT_MS = 100L  // 采集超时100ms
    }
    
    // 性能统计
    @Volatile
    private var totalTokensReceived = 0L
    
    @Volatile
    private var totalConversations = 0L
    
    @Volatile
    private var lastMetricsUpdateTime = 0L
    
    // 🔥 【RAW TOKEN日志采集】Token批量收集缓存
    private val tokenCollectionBuffer = mutableListOf<String>()
    
    /**
     * 统一Token流接收处理
     * 
     * @param source Token来源（HTTP SSE/WebSocket/HTTP Basic等）
     * @param conversationId 会话ID，用于路由和状态管理
     * @return 处理后的Token流，直接输出给ThinkingBox
     */
    suspend fun receiveTokenStream(
        source: TokenSource,
        conversationId: String
    ): Flow<String> = flow {
        val startTime = System.currentTimeMillis()
        var tokenCount = 0
        var detectedType: ContentType? = null
        var detectionComplete = false
        
        Timber.tag(TAG).i("🔄 开始接收Token流: conversationId=$conversationId, source=${source.sourceType}")
        
        // 重置协议检测器
        protocolDetector.reset()
        totalConversations++
        
        try {
            source.tokenFlow.collect { token ->
                val tokenStartTime = System.currentTimeMillis()
                tokenCount++
                totalTokensReceived++
                
                // 🔥 【RAW TOKEN日志采集】批量收集token
                collectTokenForLogging(token, source.sourceType, conversationId)
                
                // 阶段1：协议检测（仅前200 token）
                if (!detectionComplete && tokenCount <= MAX_DETECTION_TOKENS) {
                    val detectionResult = protocolDetector.detectWithConfidence(token)
                    
                    when (detectionResult) {
                        is DetectionResult.Confirmed -> {
                            detectedType = detectionResult.type
                            detectionComplete = true
                            
                            val detectionTime = System.currentTimeMillis() - startTime
                            Timber.tag(TAG).i("🔍 协议检测完成: type=${detectedType}, " +
                                    "tokens=$tokenCount, time=${detectionTime}ms, " +
                                    "confidence=${detectionResult.confidence}")
                        }
                        is DetectionResult.Probable -> {
                            if (detectionResult.confidence > 0.8f) {
                                detectedType = detectionResult.type
                                detectionComplete = true
                                
                                Timber.tag(TAG).i("🔍 高置信度检测: type=${detectedType}, " +
                                        "confidence=${detectionResult.confidence}")
                            }
                        }
                        DetectionResult.INSUFFICIENT_DATA -> {
                            // 继续收集数据
                        }
                    }
                }
                
                // 阶段2：即时流式处理
                if (detectedType != null) {
                    val processed = streamingProcessor.processImmediate(
                        token = token,
                        contentType = detectedType!!,
                        conversationId = conversationId
                    )
                    
                    // 立即输出，减少延迟
                    if (processed.isNotEmpty()) {
                        emit(processed)
                    }
                } else if (tokenCount > MAX_DETECTION_TOKENS) {
                    // 检测超时，使用默认处理
                    detectedType = ContentType.PLAIN_TEXT
                    detectionComplete = true
                    
                    Timber.tag(TAG).w("⚠️ 协议检测超时，使用默认处理: conversationId=$conversationId")
                    
                    val processed = streamingProcessor.processImmediate(
                        token = token,
                        contentType = detectedType!!,
                        conversationId = conversationId
                    )
                    
                    if (processed.isNotEmpty()) {
                        emit(processed)
                    }
                }
                
                // 记录Token处理延迟
                val tokenLatency = System.currentTimeMillis() - tokenStartTime
                if (performanceMonitor is com.example.gymbro.core.network.buffer.PerformanceMonitorImpl) {
                    performanceMonitor.recordTokenLatency(tokenLatency)
                }
                
                // 定期更新性能指标
                updatePerformanceMetricsIfNeeded(System.currentTimeMillis())
            }
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ Token流处理失败: conversationId=$conversationId")
            
            // 记录错误
            if (performanceMonitor is com.example.gymbro.core.network.buffer.PerformanceMonitorImpl) {
                performanceMonitor.recordError("TOKEN_STREAM_ERROR", e.message ?: "Unknown error")
            }
            
            throw e
        } finally {
            val totalTime = System.currentTimeMillis() - startTime
            
            // 🔥 【RAW TOKEN日志采集】刷新剩余token到日志
            flushTokenCollectionBuffer(source.sourceType, conversationId)
            
            Timber.tag(TAG).i("✅ Token流接收完成: conversationId=$conversationId, " +
                    "total_tokens=$tokenCount, total_time=${totalTime}ms, " +
                    "detected_type=$detectedType")
        }
    }
    
    /**
     * 🔥 【RAW TOKEN日志采集】收集token用于批量日志记录
     */
    private suspend fun collectTokenForLogging(token: String, source: String, conversationId: String) {
        // 使用一个局部变量来避免在临界区内调用挂起函数
        val shouldFlush: Boolean
        val tokensToFlush: List<String>
        
        synchronized(tokenCollectionBuffer) {
            tokenCollectionBuffer.add(token)
            
            // 检查是否需要刷新
            shouldFlush = tokenCollectionBuffer.size >= TOKEN_BATCH_SIZE
            tokensToFlush = if (shouldFlush) {
                val tokens = tokenCollectionBuffer.toList()
                tokenCollectionBuffer.clear()
                tokens
            } else {
                emptyList()
            }
        }
        
        // 在临界区外执行挂起函数
        if (shouldFlush) {
            try {
                tokenLogCollector.collectReceivedTokens(
                    tokens = tokensToFlush,
                    source = source,
                    conversationId = conversationId
                )
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "⚠️ Token日志采集失败: source=$source")
            }
        }
    }
    
    /**
     * 🔥 【RAW TOKEN日志采集】刷新缓冲区中剩余的token
     */
    private suspend fun flushTokenCollectionBuffer(source: String, conversationId: String) {
        val tokensToFlush: List<String>
        
        synchronized(tokenCollectionBuffer) {
            tokensToFlush = if (tokenCollectionBuffer.isNotEmpty()) {
                val tokens = tokenCollectionBuffer.toList()
                tokenCollectionBuffer.clear()
                tokens
            } else {
                emptyList()
            }
        }
        
        // 在临界区外执行挂起函数
        if (tokensToFlush.isNotEmpty()) {
            try {
                tokenLogCollector.collectReceivedTokens(
                    tokens = tokensToFlush,
                    source = source,
                    conversationId = conversationId
                )
            } catch (e: Exception) {
                Timber.tag(TAG).w(e, "⚠️ 最终Token刷新失败: source=$source")
            }
        }
    }
    
    /**
     * 获取接收器状态信息
     */
    fun getReceiverStatus(): ReceiverStatus {
        val bufferStatus = adaptiveBufferManager.getStatus()
        val detectionStatus = protocolDetector.getDetectionStatus()
        
        return ReceiverStatus(
            totalTokensReceived = totalTokensReceived,
            totalConversations = totalConversations,
            currentBufferSize = bufferStatus.currentBufferSize,
            bufferAdjustments = bufferStatus.adjustmentCount,
            detectionStage = detectionStatus.currentStage,
            detectedType = detectionStatus.detectedType
        )
    }
    
    /**
     * 重置统计信息
     */
    fun resetStatistics() {
        totalTokensReceived = 0L
        totalConversations = 0L
        lastMetricsUpdateTime = 0L
        
        // 重置性能监控器
        if (performanceMonitor is com.example.gymbro.core.network.buffer.PerformanceMonitorImpl) {
            performanceMonitor.reset()
        }
        
        Timber.tag(TAG).i("🔄 统计信息已重置")
    }
    
    /**
     * 定期更新性能指标
     */
    private suspend fun updatePerformanceMetricsIfNeeded(currentTime: Long) {
        if (currentTime - lastMetricsUpdateTime >= METRICS_UPDATE_INTERVAL_MS) {
            val timeDelta = currentTime - lastMetricsUpdateTime
            val tokenRate = if (timeDelta > 0) {
                (totalTokensReceived * 1000.0f / timeDelta)
            } else {
                0f
            }
            
            // 获取当前性能指标
            val currentMetrics = performanceMonitor.getCurrentMetrics()
            
            // 更新Token处理速度
            val updatedMetrics = currentMetrics.copy(
                tokensPerSecond = tokenRate,
                networkThroughput = tokenRate * 1.1f // 估算网络吞吐量
            )
            
            // 调整缓冲区大小
            adaptiveBufferManager.adjustBufferSize(updatedMetrics)
            
            // 记录性能指标
            performanceMonitor.recordMetrics(updatedMetrics)
            
            lastMetricsUpdateTime = currentTime
        }
    }
}

/**
 * 📡 Token来源接口
 * 
 * 统一不同协议的Token输入接口
 */
interface TokenSource {
    val tokenFlow: Flow<String>
    val sourceType: String  // "HTTP_SSE", "WEBSOCKET", "HTTP_BASIC"
}

/**
 * 🚀 HTTP SSE Token来源实现
 */
class HttpSseTokenSource(
    override val tokenFlow: Flow<String>
) : TokenSource {
    override val sourceType: String = "HTTP_SSE"
}

/**
 * 🚀 WebSocket Token来源实现
 */
class WebSocketTokenSource(
    override val tokenFlow: Flow<String>
) : TokenSource {
    override val sourceType: String = "WEBSOCKET"
}

/**
 * 🚀 HTTP Basic Token来源实现
 */
class HttpBasicTokenSource(
    override val tokenFlow: Flow<String>
) : TokenSource {
    override val sourceType: String = "HTTP_BASIC"
}

/**
 * 📊 接收器状态信息
 */
data class ReceiverStatus(
    val totalTokensReceived: Long,
    val totalConversations: Long,
    val currentBufferSize: Int,
    val bufferAdjustments: Long,
    val detectionStage: com.example.gymbro.core.network.detector.DetectionStage,
    val detectedType: ContentType?
)
