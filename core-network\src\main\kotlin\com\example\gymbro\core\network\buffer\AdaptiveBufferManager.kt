package com.example.gymbro.core.network.buffer

import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 自适应缓冲管理器 - 智能缓冲策略核心组件
 * 
 * 设计目标：
 * - 根据实时性能动态调整缓冲区大小
 * - 防止UI阻塞和内存溢出
 * - 保持背压控制机制
 * - 支持性能监控和自动调整
 */
@Singleton
class AdaptiveBufferManager @Inject constructor(
    private val performanceMonitor: PerformanceMonitor
) {
    companion object {
        private const val TAG = "AdaptiveBufferManager"
        
        // 缓冲区大小范围
        private const val MIN_BUFFER_SIZE = 16      // 最小缓冲：16个token
        private const val MAX_BUFFER_SIZE = 128     // 最大缓冲：128个token
        private const val DEFAULT_BUFFER_SIZE = 32  // 默认缓冲：32个token
        
        // 性能阈值
        private const val BACKPRESSURE_THRESHOLD = 0.8f  // 80%触发背压
        private const val MEMORY_PRESSURE_THRESHOLD = 0.8f  // 80%内存压力
        private const val PROCESSING_SPEED_RATIO = 1.2f  // 处理速度比率
    }
    
    @Volatile
    private var currentBufferSize = DEFAULT_BUFFER_SIZE
    
    @Volatile
    private var adjustmentCount = 0L
    
    @Volatile
    private var lastAdjustmentTime = 0L
    
    /**
     * 根据实时性能动态调整缓冲区大小
     * 
     * @param metrics 处理性能指标
     */
    fun adjustBufferSize(metrics: ProcessingMetrics) {
        val currentTime = System.currentTimeMillis()
        
        // 防止频繁调整（最少间隔1秒）
        if (currentTime - lastAdjustmentTime < 1000) {
            return
        }
        
        val processingSpeed = metrics.tokensPerSecond
        val networkSpeed = metrics.networkThroughput
        val memoryPressure = metrics.memoryUsagePercent
        val bufferUtilization = metrics.bufferUtilization
        
        val oldBufferSize = currentBufferSize
        
        when {
            // 处理速度跟得上，内存压力低，可以减少缓冲
            processingSpeed >= networkSpeed * PROCESSING_SPEED_RATIO && 
            memoryPressure < 0.6f && 
            bufferUtilization < 0.5f -> {
                currentBufferSize = maxOf(MIN_BUFFER_SIZE, currentBufferSize - 8)
                Timber.tag(TAG).d("🔽 减少缓冲: $oldBufferSize → $currentBufferSize (处理速度充足)")
            }
            
            // 处理速度跟不上，或内存压力高，增加缓冲
            processingSpeed < networkSpeed * 0.8f || 
            memoryPressure > MEMORY_PRESSURE_THRESHOLD ||
            bufferUtilization > BACKPRESSURE_THRESHOLD -> {
                currentBufferSize = minOf(MAX_BUFFER_SIZE, currentBufferSize + 16)
                Timber.tag(TAG).w("🔼 增加缓冲: $oldBufferSize → $currentBufferSize " +
                        "(处理速度不足或内存压力)")
            }
            
            // 稳定状态，保持当前缓冲大小
            else -> {
                Timber.tag(TAG).v("⚖️ 缓冲稳定: $currentBufferSize (性能平衡)")
            }
        }
        
        if (oldBufferSize != currentBufferSize) {
            adjustmentCount++
            lastAdjustmentTime = currentTime
            
            // 记录调整事件
            performanceMonitor.recordBufferAdjustment(
                oldSize = oldBufferSize,
                newSize = currentBufferSize,
                reason = determineAdjustmentReason(metrics)
            )
        }
    }
    
    /**
     * 创建自适应的SharedFlow
     * 
     * @return 配置了当前最优缓冲区大小的SharedFlow
     */
    fun <T> createAdaptiveFlow(): MutableSharedFlow<T> {
        return MutableSharedFlow(
            replay = 0,
            extraBufferCapacity = currentBufferSize,
            onBufferOverflow = BufferOverflow.SUSPEND
        )
    }
    
    /**
     * 获取当前缓冲区大小
     */
    fun getCurrentBufferSize(): Int = currentBufferSize
    
    /**
     * 获取缓冲管理器状态
     */
    fun getStatus(): BufferManagerStatus {
        return BufferManagerStatus(
            currentBufferSize = currentBufferSize,
            minBufferSize = MIN_BUFFER_SIZE,
            maxBufferSize = MAX_BUFFER_SIZE,
            adjustmentCount = adjustmentCount,
            lastAdjustmentTime = lastAdjustmentTime
        )
    }
    
    /**
     * 强制设置缓冲区大小（用于测试或特殊场景）
     */
    fun forceBufferSize(size: Int) {
        require(size in MIN_BUFFER_SIZE..MAX_BUFFER_SIZE) {
            "缓冲区大小必须在 $MIN_BUFFER_SIZE 到 $MAX_BUFFER_SIZE 之间"
        }
        
        val oldSize = currentBufferSize
        currentBufferSize = size
        
        Timber.tag(TAG).i("🔧 强制设置缓冲: $oldSize → $currentBufferSize")
    }
    
    /**
     * 重置为默认缓冲大小
     */
    fun resetToDefault() {
        val oldSize = currentBufferSize
        currentBufferSize = DEFAULT_BUFFER_SIZE
        
        Timber.tag(TAG).i("🔄 重置缓冲: $oldSize → $currentBufferSize")
    }
    
    private fun determineAdjustmentReason(metrics: ProcessingMetrics): String {
        return when {
            metrics.memoryUsagePercent > MEMORY_PRESSURE_THRESHOLD -> "内存压力"
            metrics.bufferUtilization > BACKPRESSURE_THRESHOLD -> "背压触发"
            metrics.tokensPerSecond < metrics.networkThroughput * 0.8f -> "处理速度不足"
            metrics.tokensPerSecond >= metrics.networkThroughput * PROCESSING_SPEED_RATIO -> "处理速度充足"
            else -> "性能平衡"
        }
    }
}

/**
 * 📊 处理性能指标
 */
data class ProcessingMetrics(
    val tokensPerSecond: Float,         // 处理速度（token/秒）
    val networkThroughput: Float,       // 网络吞吐量（token/秒）
    val memoryUsagePercent: Float,      // 内存使用百分比（0.0-1.0）
    val bufferUtilization: Float,       // 缓冲区利用率（0.0-1.0）
    val avgLatencyMs: Long,             // 平均延迟（毫秒）
    val errorRate: Float                // 错误率（0.0-1.0）
)

/**
 * 📈 缓冲管理器状态
 */
data class BufferManagerStatus(
    val currentBufferSize: Int,
    val minBufferSize: Int,
    val maxBufferSize: Int,
    val adjustmentCount: Long,
    val lastAdjustmentTime: Long
)

/**
 * 📊 性能监控接口
 */
interface PerformanceMonitor {
    /**
     * 记录缓冲区调整事件
     */
    fun recordBufferAdjustment(oldSize: Int, newSize: Int, reason: String)
    
    /**
     * 记录性能指标
     */
    fun recordMetrics(metrics: ProcessingMetrics)
    
    /**
     * 获取当前性能指标
     */
    suspend fun getCurrentMetrics(): ProcessingMetrics
}
