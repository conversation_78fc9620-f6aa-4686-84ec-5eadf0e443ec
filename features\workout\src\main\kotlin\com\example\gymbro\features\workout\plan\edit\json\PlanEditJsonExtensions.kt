package com.example.gymbro.features.workout.plan.edit.json

import com.example.gymbro.features.workout.plan.edit.PlanEditContract
import com.example.gymbro.domain.workout.model.plan.DayPlan
import com.example.gymbro.shared.models.workout.PlanCalendarData
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber

/**
 * Plan Edit JSON Extension Functions - 扩展函数库
 *
 * 🎯 核心功能：
 * - 为PlanEditContract.State提供便捷的JSON操作
 * - 简化JSON序列化/反序列化调用
 * - 提供类型安全的转换方法
 * - 支持链式调用和函数式编程
 *
 * 🏗️ 架构特点：
 * - 遵循Kotlin扩展函数最佳实践
 * - 与PlanEditJsonProcessor完全集成
 * - 提供错误安全的操作
 * - 支持函数式编程风格
 */

/**
 * 将PlanEditContract.State转换为JSON字符串
 *
 * @return JSON字符串
 */
fun PlanEditContract.State.toJson(): String {
    return PlanEditJsonProcessor.stateToJson(this)
}

/**
 * 安全地将PlanEditContract.State转换为JSON字符串
 * 保证不会抛出异常
 *
 * @return JSON字符串，失败时返回默认JSON
 */
fun PlanEditContract.State.toSafeJson(): String {
    return PlanEditJsonProcessor.safeStateToJson(this)
}

/**
 * 将PlanEditContract.State转换为日历格式JSON
 *
 * @return 日历格式JSON字符串
 */
fun PlanEditContract.State.toCalendarJson(): String {
    return PlanEditJsonProcessor.stateToCalendarJson(this)
}

/**
 * 检查状态是否可以安全序列化
 *
 * @return 是否可以序列化
 */
fun PlanEditContract.State.canSerialize(): Boolean {
    return try {
        PlanEditJsonProcessor.stateToJson(this)
        true
    } catch (e: Exception) {
        false
    }
}

/**
 * 获取状态的JSON摘要信息
 * 只包含关键字段，用于日志和调试
 *
 * @return 摘要JSON字符串
 */
fun PlanEditContract.State.toSummaryJson(): String {
    return try {
        val summary = mapOf(
            "plan_id" to planId,
            "plan_name" to planName,
            "total_weeks" to weekPlans.size,
            "total_days" to weekPlans.values.flatten().size,
            "workout_days" to weekPlans.values.flatten().count { !it.isRestDay },
            "rest_days" to weekPlans.values.flatten().count { it.isRestDay },
            "has_unsaved_changes" to hasUnsavedChanges,
            "last_saved_time" to lastSavedTime
        )
        Json.encodeToString(summary)
    } catch (e: Exception) {
        Timber.e(e, "生成状态摘要失败")
        "{\"error\": \"summary_generation_failed\"}"
    }
}

/**
 * 从JSON字符串创建PlanEditContract.State
 *
 * @param jsonString JSON字符串
 * @return PlanEditContract.State实例
 */
fun String.toPlanEditState(): PlanEditContract.State {
    return PlanEditJsonProcessor.stateFromJson(this)
}

/**
 * 验证JSON字符串是否为有效的PlanEditContract.State
 *
 * @return 是否有效
 */
fun String.isValidPlanEditStateJson(): Boolean {
    return PlanEditJsonProcessor.validateStateJson(this)
}

/**
 * 从可能损坏的JSON恢复PlanEditContract.State
 *
 * @param fallbackState 备用状态
 * @return 恢复的状态
 */
fun String.recoverToPlanEditState(
    fallbackState: PlanEditContract.State = PlanEditContract.State()
): PlanEditContract.State {
    return PlanEditJsonProcessor.recoverStateFromCorruptedJson(this, fallbackState)
}

/**
 * 计算状态的数据统计信息
 *
 * @return 统计信息Map
 */
fun PlanEditContract.State.getStatistics(): Map<String, Any> {
    val allDays = weekPlans.values.flatten()
    return mapOf(
        "total_weeks" to weekPlans.size,
        "total_days" to allDays.size,
        "workout_days" to allDays.count { !it.isRestDay },
        "rest_days" to allDays.count { it.isRestDay },
        "total_templates" to allDays.sumOf { it.templateVersionIds.size },
        "completed_days" to allDays.count { it.isCompleted },
        "completion_rate" to if (allDays.isNotEmpty()) {
            allDays.count { it.isCompleted }.toDouble() / allDays.size
        } else 0.0,
        "has_unsaved_changes" to hasUnsavedChanges,
        "is_empty_plan" to (allDays.isEmpty() || allDays.all { it.isRestDay && it.templateVersionIds.isEmpty() })
    )
}

/**
 * 检查状态是否为空计划
 *
 * @return 是否为空计划
 */
fun PlanEditContract.State.isEmpty(): Boolean {
    val allDays = weekPlans.values.flatten()
    return allDays.isEmpty() || allDays.all { it.isRestDay && it.templateVersionIds.isEmpty() }
}

/**
 * 检查状态是否有有效的训练安排
 *
 * @return 是否有有效训练安排
 */
fun PlanEditContract.State.hasValidWorkouts(): Boolean {
    return weekPlans.values.flatten().any { !it.isRestDay && it.templateVersionIds.isNotEmpty() }
}

/**
 * 获取计划的完成进度
 *
 * @return 完成进度 (0.0 - 1.0)
 */
fun PlanEditContract.State.getCompletionProgress(): Double {
    val allDays = weekPlans.values.flatten()
    return if (allDays.isNotEmpty()) {
        allDays.count { it.isCompleted }.toDouble() / allDays.size
    } else 0.0
}

/**
 * 获取指定周的训练天数
 *
 * @param week 周数
 * @return 训练天数
 */
fun PlanEditContract.State.getWorkoutDaysInWeek(week: Int): Int {
    return weekPlans[week]?.count { !it.isRestDay } ?: 0
}

/**
 * 获取指定周的休息天数
 *
 * @param week 周数
 * @return 休息天数
 */
fun PlanEditContract.State.getRestDaysInWeek(week: Int): Int {
    return weekPlans[week]?.count { it.isRestDay } ?: 0
}

/**
 * 检查指定周是否有训练安排
 *
 * @param week 周数
 * @return 是否有训练安排
 */
fun PlanEditContract.State.hasWorkoutsInWeek(week: Int): Boolean {
    return weekPlans[week]?.any { !it.isRestDay && it.templateVersionIds.isNotEmpty() } ?: false
}

/**
 * 获取所有使用的模板ID
 *
 * @return 模板ID集合
 */
fun PlanEditContract.State.getAllTemplateIds(): Set<String> {
    return weekPlans.values.flatten()
        .flatMap { it.templateVersionIds }
        .toSet()
}

/**
 * 检查是否使用了指定模板
 *
 * @param templateId 模板ID
 * @return 是否使用了该模板
 */
fun PlanEditContract.State.usesTemplate(templateId: String): Boolean {
    return weekPlans.values.flatten()
        .any { it.templateVersionIds.contains(templateId) }
}

/**
 * 获取模板使用次数
 *
 * @param templateId 模板ID
 * @return 使用次数
 */
fun PlanEditContract.State.getTemplateUsageCount(templateId: String): Int {
    return weekPlans.values.flatten()
        .sumOf { dayPlan -> dayPlan.templateVersionIds.count { it == templateId } }
}

/**
 * 创建状态的深拷贝（通过JSON序列化/反序列化）
 *
 * @return 深拷贝的状态
 */
fun PlanEditContract.State.deepCopy(): PlanEditContract.State {
    return try {
        this.toJson().toPlanEditState()
    } catch (e: Exception) {
        Timber.e(e, "状态深拷贝失败，返回原状态")
        this
    }
}

/**
 * 比较两个状态的差异
 *
 * @param other 另一个状态
 * @return 差异描述
 */
fun PlanEditContract.State.diffWith(other: PlanEditContract.State): Map<String, Any> {
    return mapOf(
        "plan_id_changed" to (planId != other.planId),
        "plan_name_changed" to (planName != other.planName),
        "week_plans_changed" to (weekPlans != other.weekPlans),
        "selected_week_changed" to (selectedWeek != other.selectedWeek),
        "selected_day_changed" to (selectedDay != other.selectedDay),
        "unsaved_changes_diff" to (hasUnsavedChanges != other.hasUnsavedChanges),
        "last_saved_time_diff" to (lastSavedTime - other.lastSavedTime)
    )
}
