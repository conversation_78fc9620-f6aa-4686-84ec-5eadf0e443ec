package com.example.gymbro.detekt

import io.gitlab.arturbosch.detekt.api.*
import com.example.gymbro.detekt.mvi.*
import com.example.gymbro.detekt.design.*
import com.example.gymbro.detekt.logging.*
import com.example.gymbro.detekt.quality.*

/**
 * GymBro 项目自定义 Detekt 规则集
 * 
 * 包含以下规则分类：
 * 1. MVI 架构规则 - 确保 MVI 模式的正确实现
 * 2. Design Token 规则 - 强制使用设计系统令牌
 * 3. 日志规则 - 规范化日志使用
 * 4. 质量规则 - 项目特定的代码质量要求
 */
class GymBroRuleSetProvider : RuleSetProvider {
    
    override val ruleSetId: String = "gymbro-rules"
    
    override fun instance(config: Config): RuleSet = RuleSet(
        ruleSetId,
        listOf(
            // MVI 架构规则
            MviIntentNaming(config),
            ImmutableStateClass(config),
            
            // Design Token 规则
            NoHardcodedDesignValues(config),
            UseWorkoutColors(config),
            
            // 日志规则
            MaxTimberLogsPerFunction(config),
            LoggingModuleRestriction(config),
            
            // 质量规则
            NoTodoOrFixme(config)
        )
    )
}