# Core Network Module - 应用层统一管理·服务消费模式

**Version:** 2.0 - 应用层统一管理架构
**Last Updated:** 2025-01-18

## 📋 模块概述

Core Network模块是GymBro项目的**网络基础设施服务层**，实现了「应用层统一管理·服务消费模式」的架构设计。网络层的生命周期由`AppStartupManager`统一管理，功能模块只消费网络服务，不管理网络生命周期，实现了清晰的架构分层和职责分离。

## 🎯 核心特性

### 🔥 1. 应用层统一管理 (v2.0)
- **🚀 AppStartupManager管理**: 网络层生命周期由应用启动管理器统一控制
- **🌐 优先初始化**: 网络基础设施在第1层优先启动，为其他模块提供基础服务
- **🔄 服务消费模式**: 功能模块只消费网络状态和服务，不管理网络生命周期
- **📊 状态透明**: 通过StateFlow提供实时的网络状态监控

### ✅ 2. 智能协议选择：AdaptiveStreamClient
- **📡 默认HTTP+SSE**: 主要协议，稳定可靠的准流式传输
- **📄 降级HTTP基础**: SSE不支持时的备选方案，模拟流式输出
- **🔗 WebSocket专用**: 仅用于热更新配置的指定模型（o1-preview, o1-mini, gpt-4-turbo, claude-3-opus）
- **🔄 动态协议检测**: 根据模型类型和服务器能力自动选择最优协议

### ✅ 3. 零硬编码架构：NetworkConfigManager
- **📝 动态配置获取**: 从NetworkConfigManager获取所有API配置
- **🔧 零硬编码**: 移除所有硬编码的API密钥和URL
- **⚙️ 热更新支持**: 支持运行时更新WebSocket支持的模型列表
- **🛡️ 配置验证**: validate()方法确保配置有效性

### ✅ 4. 统一流式接口：LlmStreamClient
- **🎯 统一接口**: 通过LlmStreamClient提供统一的流式接口
- **🔧 自动降级**: HTTP+SSE失败时自动降级到HTTP基础协议
- **📊 状态管理**: 统一的连接状态和错误处理
- **🔍 协议透明**: 上层业务无需关心具体使用的协议

### ✅ 5. 统一的输出通道：DirectOutputChannel
- **🔥 直接输出**: 跳过中间层，直接将token发送到目标组件
- **🧹 架构简化**: 移除复杂的事件总线和路由系统
- **⚡ 零延迟**: 最小化token传输延迟
- **🛡️ 类型安全**: 强类型的输出接口

## 🏗️ 新架构设计

### 📁 目录结构
```
core-network/
 ├─ adapter/                   # 适配器层
 │   └─ ThinkingBoxAdapter.kt      # ThinkingBox适配器
 ├─ buffer/                    # 缓冲管理
 │   ├─ AdaptiveBufferManager.kt   # 自适应缓冲管理
 │   ├─ SlidingWindowBuffer.kt     # 滑动窗口缓冲
 │   └─ PerformanceMonitorImpl.kt  # 性能监控实现
 ├─ detector/                  # 协议检测
 │   ├─ FeatureMatcher.kt          # 特征匹配器
 │   └─ ProgressiveProtocolDetector.kt # 渐进式协议检测
 ├─ processor/                 # 流处理核心
 │   ├─ StreamingProcessor.kt      # 流处理接口
 │   └─ StreamingProcessorImpl.kt  # 流处理实现
 ├─ output/                    # 输出通道
 │   └─ DirectOutputChannel.kt     # 直接输出通道
 ├─ receiver/                  # 接收器
 │   └─ UnifiedTokenReceiver.kt    # 统一Token接收器
 ├─ rest/                      # REST API
 │   ├─ RestClientImpl.kt          # REST客户端实现
 │   └─ interceptors/              # 拦截器链
 ├─ config/                    # 配置管理
 │   ├─ NetworkConfig.kt           # 网络配置数据类
 │   ├─ NetworkConfigManager.kt    # 配置管理器
 │   └─ AiTaskType.kt              # AI任务类型枚举
 ├─ monitor/                   # 网络监控
 │   ├─ NetworkWatchdog.kt         # 网络状态监控
 │   └─ AndroidNetworkMonitor.kt   # Android网络监控实现
 ├─ security/                  # 安全处理
 │   ├─ StringXmlEscaper.kt        # XML转义处理
 │   └─ PiiSanitizer.kt            # PII数据净化
 └─ di/CoreNetworkModule.kt    # 统一DI配置
```

### 🔧 智能协议选择架构
```kotlin
// 协议选择策略
class AdaptiveStreamClient {
    // 1. 检查模型是否为WebSocket专用
    private fun isWebSocketSupportedModel(model: String): Boolean {
        return webSocketSupportedModels.any { supportedModel ->
            model.contains(supportedModel, ignoreCase = true)
        }
    }

    // 2. 智能协议选择
    private suspend fun selectOptimalProtocol(
        model: String,
        baseUrl: String,
        apiKey: String
    ): SupportedProtocol {
        // WebSocket专用模型检查
        if (isWebSocketSupportedModel(model)) {
            // 验证WebSocket可用性
            if (protocolDetector.detectWebSocketSupport()) {
                return SupportedProtocol.WEBSOCKET
            }
        }

        // 默认HTTP+SSE，降级HTTP基础
        return detectHttpProtocol(baseUrl, apiKey)
    }
}
```

### 🔗 配置驱动架构
```kotlin
// NetworkConfig.kt - 零硬编码配置
data class NetworkConfig(
    val wsBase: String,      // WebSocket基础URL
    val restBase: String,    // REST API基础URL
    val apiKey: String,      // API密钥
    // 其他网络配置...
)

// AdaptiveStreamClient.kt - 动态配置获取
@Singleton
class AdaptiveStreamClient @Inject constructor(
    private val configManager: NetworkConfigManager // 🔥 从配置管理器获取
) {
    override fun streamChat(request: ChatRequest): Flow<String> {
        val config = configManager.getCurrentConfig()
        return streamChat(request, config.restBase, config.apiKey)
    }
}
```

## 🏗️ 应用层统一管理架构 (v2.0)

### 🎯 架构设计理念

Core Network模块在v2.0中实现了「应用层统一管理·服务消费模式」的架构重构：

#### **生命周期管理分离**

**Before (旧架构 - 分散管理)**:
```kotlin
// ❌ 各个功能模块自行管理网络生命周期
class AiCoachViewModel {
    init {
        networkWatchdog.startWatching() // 功能模块启动网络监控
    }

    override fun onCleared() {
        networkWatchdog.stopWatching() // 功能模块停止网络监控
    }
}

class ProfileViewModel {
    init {
        networkWatchdog.startWatching() // 重复启动
    }
}
```

**After (新架构 - 统一管理)**:
```kotlin
// ✅ 应用层统一管理网络生命周期
class AppStartupManager {
    suspend fun startNetworkLayer() {
        // 第1层：网络基础设施统一启动
        val networkJobs = listOf(
            async { initNetworkWatchdog() },      // 统一启动网络监控
            async { initAiProviderManager() },    // 统一初始化AI提供商
            async { initProtocolDetector() }      // 统一初始化协议检测
        )
        networkJobs.awaitAll()
    }

    private suspend fun initNetworkWatchdog() {
        networkWatchdog.startWatching() // 只在应用层启动一次
    }
}

// ✅ 功能模块只消费网络服务
class AiCoachViewModel {
    init {
        // 只监听网络状态，不管理网络生命周期
        observeNetworkEvents()
    }

    private fun observeNetworkEvents() {
        networkWatchdog.networkEvents.collect { event ->
            handleNetworkStateChange(event) // 只处理状态变化
        }
    }
}
```

#### **分层启动策略**

```
应用启动 (AppStartupManager)
├── 第1层：网络基础设施 (0-500ms) ✅
│   ├── NetworkWatchdog.startWatching()
│   ├── AiProviderManager.initialize()
│   └── ProtocolDetector.initialize()
├── 第2层：AI核心组件 (500-2000ms)
│   ├── BgeEngineManager.initialize()
│   └── PromptRegistry.initialize()
└── 第3层：功能模块 (2000ms+)
    └── 依赖已就绪的网络服务 ✅
```

#### **服务消费模式**

功能模块现在采用纯粹的服务消费模式：

```kotlin
// ✅ 推荐：只消费网络状态
networkWatchdog.networkEvents.collect { event ->
    when (event.type) {
        NetworkEventType.CONNECTED -> handleNetworkConnected()
        NetworkEventType.DISCONNECTED -> handleNetworkDisconnected()
    }
}

// ✅ 推荐：只调用状态刷新
networkWatchdog.refreshNetworkState()

// ❌ 禁止：管理网络生命周期
// networkWatchdog.startWatching()  - 已移除
// networkWatchdog.stopWatching()   - 已移除
// networkWatchdog.pauseWatching()  - 已移除
// networkWatchdog.resumeWatching() - 已移除
```

### 🔧 NetworkWatchdog使用指南

#### **应用层使用（AppStartupManager）**

```kotlin
// ✅ 只在应用启动时调用
class AppStartupManager {
    private suspend fun initNetworkWatchdog() {
        networkWatchdog.startWatching()
        // 等待网络状态稳定
        delay(500)
    }
}
```

#### **功能模块使用（推荐模式）**

```kotlin
// ✅ 功能模块的正确使用方式
class AnyFeatureViewModel {
    init {
        monitorNetworkState() // 只监听状态
    }

    private fun monitorNetworkState() {
        viewModelScope.launch {
            networkWatchdog.networkEvents
                .filterNotNull()
                .collect { event ->
                    updateNetworkStatus(event)
                }
        }
    }

    fun refreshNetworkStatus() {
        // ✅ 允许：触发状态刷新
        networkWatchdog.refreshNetworkState()
    }

    // ❌ 禁止：生命周期管理
    // override fun onCleared() {
    //     networkWatchdog.stopWatching() // 已移除
    // }
}
```

### 📊 状态监控API

```kotlin
// 检查网络层是否就绪
val isNetworkReady = appStartupManager.isNetworkReady()

// 监听网络状态变化
networkWatchdog.networkEvents.collect { event ->
    when (event.type) {
        NetworkEventType.CONNECTED -> {
            // 网络已连接，可以进行网络请求
        }
        NetworkEventType.DISCONNECTED -> {
            // 网络断开，显示离线提示
        }
        NetworkEventType.METERED -> {
            // 计费网络，可能需要调整数据使用策略
        }
    }
}

// 获取当前网络状态
val currentState = networkWatchdog.currentNetworkState.value
```

## 🚀 使用方式

### 智能流式对话
```kotlin
@Inject
lateinit var llmStreamClient: LlmStreamClient

// 自动协议选择的流式对话
val request = ChatRequest(
    model = "o1-preview", // 🔥 自动识别为WebSocket专用模型
    messages = listOf(ChatMessage(role = "user", content = "复杂推理任务")),
    stream = true
)

llmStreamClient.streamChat(request)
    .collect { response ->
        // AdaptiveStreamClient自动选择最优协议
        // o1-preview -> WebSocket (如果可用)
        // 普通模型 -> HTTP+SSE (默认)
        // 降级 -> HTTP基础 (备选)
        println("收到: $response")
    }
```

### 热更新WebSocket模型配置
```kotlin
@Inject
lateinit var adaptiveStreamClient: AdaptiveStreamClient

// 运行时更新WebSocket支持的模型
val newModels = setOf(
    "o1-preview",
    "o1-mini",
    "gpt-4-turbo",
    "claude-3-opus",
    "gemini-2.0-flash-thinking" // 🔥 新增模型
)

adaptiveStreamClient.updateWebSocketSupportedModels(newModels)
```

### 直接输出通道使用
```kotlin
@Inject
lateinit var directOutputChannel: DirectOutputChannel

// 🔥 直接输出模式：无中间路由，直接发送到目标
val messageId = "conversation-response-123"

// 直接发送token到ThinkingBox
launch {
    listOf("Hello", " from", " direct", " output").forEach { token ->
        directOutputChannel.sendToken(messageId, token)
    }
    directOutputChannel.closeChannel(messageId) // 关闭通道
}

// 监听处理结果
directOutputChannel.processingResults
    .collect { result ->
        when (result.status) {
            ProcessingStatus.SUCCESS -> println("处理成功: ${result.messageId}")
            ProcessingStatus.ERROR -> println("处理错误: ${result.error}")
        }
    }
```

### 协议状态监控
```kotlin
@Inject
lateinit var adaptiveStreamClient: AdaptiveStreamClient

// 查看当前使用的协议
val currentProtocol = adaptiveStreamClient.getCurrentProtocol()
println("当前协议: ${currentProtocol.getDescription()}")

// 查看WebSocket支持的模型
val supportedModels = adaptiveStreamClient.getWebSocketSupportedModels()
println("WebSocket支持模型: $supportedModels")

// 强制重新检测协议
val detectedProtocol = adaptiveStreamClient.forceRedetectProtocol(baseUrl, apiKey)
println("重新检测协议: ${detectedProtocol.getDescription()}")
```

## 🧪 测试

### 运行测试
```bash
# 运行单元测试
./gradlew :core-network:testDebugUnitTest

# 生成测试覆盖率报告
./gradlew :core-network:jacocoTestReport

# 验证测试覆盖率 (≥80%)
./gradlew :core-network:verifyTestCoverage

# 运行代码质量检查
./gradlew :core-network:detekt
```

### 测试覆盖率要求
- **总体覆盖率**: ≥ 80%
- **核心类覆盖率**:
  - AdaptiveStreamClient: ≥ 90%
  - ProtocolDetector: ≥ 85%
  - NetworkConfigManager: ≥ 85%
  - NetworkConfig: 100%

## 🔧 配置

### 开发环境配置
```kotlin
// app/build.gradle.kts
android {
    buildTypes {
        debug {
            buildConfigField("String", "WS_BASE", "\"wss://dev-api.deepseek.com\"")
            buildConfigField("String", "REST_BASE", "\"https://dev-api.deepseek.com\"")
            buildConfigField("String", "AI_API_KEY", "\"sk-dev-key\"")
            buildConfigField("boolean", "ENABLE_DEBUG_LOGGING", "true")
        }
        release {
            buildConfigField("String", "WS_BASE", "\"wss://api.deepseek.com\"")
            buildConfigField("String", "REST_BASE", "\"https://api.deepseek.com\"")
            buildConfigField("String", "AI_API_KEY", "\"sk-prod-key\"")
            buildConfigField("boolean", "ENABLE_DEBUG_LOGGING", "false")
        }
    }
}
```

### 协议配置示例
```json
{
  "websocket_supported_models": [
    "o1-preview",
    "o1-mini",
    "gpt-4-turbo",
    "claude-3-opus",
    "gemini-2.0-flash-thinking"
  ],
  "default_protocol": "HTTP_SSE",
  "enable_protocol_detection": true,
  "protocol_detection_timeout_ms": 5000
}
```

## 📊 监控指标

### 协议选择指标
- `protocol_selection_count`: 协议选择次数统计
- `websocket_model_usage`: WebSocket专用模型使用统计
- `http_sse_fallback_count`: HTTP+SSE降级次数
- `protocol_detection_latency_ms`: 协议检测延迟

### 传输性能指标
- `http_sse_token_latency_ms`: HTTP+SSE Token延迟
- `websocket_token_latency_ms`: WebSocket Token延迟
- `http_basic_response_time_ms`: HTTP基础响应时间
- `protocol_switch_count`: 协议切换次数

## 🔄 协议选择流程

```mermaid
graph TD
    A[收到聊天请求] --> B{检查模型类型}
    B -->|WebSocket专用模型| C[检测WebSocket支持]
    B -->|普通模型| D[使用HTTP+SSE]
    C -->|支持| E[使用WebSocket]
    C -->|不支持| F[降级到HTTP+SSE]
    D --> G{HTTP+SSE可用?}
    F --> G
    G -->|可用| H[HTTP+SSE流式传输]
    G -->|不可用| I[降级到HTTP基础]
    E --> J[WebSocket流式传输]
    H --> K[返回流式响应]
    I --> L[模拟流式响应]
    J --> K
    L --> K
```

## 🎯 设计原则

1. **智能选择优先**: 根据模型类型和服务器能力自动选择最优协议
2. **零硬编码**: 所有配置通过NetworkConfigManager动态获取
3. **优雅降级**: 协议不可用时自动降级到备选方案
4. **热更新支持**: 支持运行时更新协议配置和模型列表
5. **统一接口**: 通过LlmStreamClient提供协议透明的统一接口

## 📚 相关文档

- [WebSocket实现详解](docs/websocket-implementation.md)
- [网络配置指南](docs/network-configuration.md)
- [测试策略文档](docs/testing-strategy.md)
- [性能优化指南](docs/performance-optimization.md)
- [故障排查手册](docs/troubleshooting.md)
    ↑
core-network (接口+实现)
    ↑
di (DI配置装配)
    ↑
data (Repository实现)
    ↑
features (ViewModel+UI)
```

### 连接分配策略

| 通道类型     | 作用                | OkHttp API          | 复用策略            |
| ------------ | ------------------- | ------------------- | ------------------- |
| **LLM流**    | token流/tool_call   | `newWebSocket()`    | 专用client，1连接   |
| **REST**     | Profile、订阅、配置 | `Retrofit + OkHttp` | 共享池，8连接/5分钟 |
| **CDN/静态** | 图片、JSON、模型包  | 同REST + Cache      | 50MB缓存            |

## 📦 模块结构

```
core-network/
├── src/main/kotlin/com/example/gymbro/core/network/
│   ├── ws/                     # WebSocket客户端
│   │   ├── LlmStreamClient.kt
│   │   ├── LlmStreamClientImpl.kt
│   │   ├── WsConfig.kt
│   │   ├── WsState.kt
│   │   ├── WsFrame.kt
│   │   └── TokenOffsetStore.kt
│   ├── rest/                   # REST客户端
│   │   ├── RestClient.kt
│   │   ├── RestClientImpl.kt
│   │   ├── RestConfig.kt
│   │   ├── ApiResult.kt
│   │   ├── SafeApiCall.kt
│   │   └── interceptors/       # 拦截器链
│   │       ├── AuthInterceptor.kt
│   │       ├── NetworkStatusInterceptor.kt
│   │       ├── RetryInterceptor.kt
│   │       └── SafeLoggingInterceptor.kt
│   ├── monitor/                # 网络监控
│   │   ├── NetworkMonitor.kt
│   │   ├── AndroidNetworkMonitor.kt
│   │   └── NetworkWatchdog.kt
│   └── model/                  # 网络数据模型
│       └── placeholder/
└── docs/                       # 文档
    ├── README.md
    ├── TREE.md
    ├── INTERFACES.md
    ├── task2.md
    ├── workflow.md
    └── task2-implementation-report.md
```

## 🚀 快速开始

### 1. 依赖注入配置

```kotlin
// 在Application模块中包含CoreNetworkModule
@HiltAndroidApp
class GymBroApplication : Application() {
    // CoreNetworkModule会自动被Hilt发现和安装
}
```

### 2. WebSocket流式客户端使用

```kotlin
@Inject
lateinit var llmStreamClient: LlmStreamClient

// 流式AI对话
val chatRequest = ChatRequest(
    model = "deepseek-chat",
    messages = listOf(ChatMessage("user", "Hello")),
    stream = true
)

llmStreamClient.streamChat(chatRequest)
    .collect { response ->
        // 处理流式响应
        println("AI Response: $response")
    }
```

### 3. REST客户端使用

```kotlin
@Inject
lateinit var restClient: RestClient

// GET请求
val result = restClient.get("https://api.example.com/data")
when (result) {
    is ApiResult.Success -> println("Data: ${result.data}")
    is ApiResult.Error -> println("Error: ${result.error}")
}

// POST请求
val postResult = restClient.post(
    url = "https://api.example.com/submit",
    body = """{"key": "value"}"""
)
```

### 4. 网络状态监控

```kotlin
@Inject
lateinit var networkWatchdog: NetworkWatchdog

// 监听网络事件
networkWatchdog.events
    .collect { event ->
        when (event) {
            is NetworkEvent.Connected -> println("网络已连接")
            is NetworkEvent.Lost -> println("网络连接丢失")
            is NetworkEvent.Restored -> println("网络连接恢复")
        }
    }
```

## ⚙️ 配置选项

### WebSocket配置 (WsConfig)
- `pingIntervalSec`: 心跳间隔（默认15秒）
- `pongTimeoutSec`: Pong超时（默认5秒）
- `maxReconnectAttempts`: 最大重连次数（默认5次）
- `baseBackoffMs`: 重连基础延迟（默认1秒）
- `maxBackoffMs`: 最大重连延迟（默认30秒）

### REST配置 (RestConfig)
- `connectTimeoutSec`: 连接超时（默认30秒）
- `readTimeoutSec`: 读取超时（默认30秒）
- `maxRetries`: 最大重试次数（默认2次）
- `retryDelayMs`: 重试延迟（默认2秒）
- `enableLogging`: 是否启用日志（默认false）

## 🧪 测试

### 单元测试
```bash
./gradlew :core-network:testDebugUnitTest
```

### 集成测试
```bash
./gradlew :core-network:connectedAndroidTest
```

### 测试覆盖率
- **目标覆盖率**: >85%
- **核心组件**: LlmStreamClientImpl, RestClientImpl, 拦截器链
- **测试工具**: MockWebServer, JUnit5, Mockk

## 📊 性能指标

### 连接性能
- **WebSocket连接建立**: <2秒
- **REST请求响应**: <1秒（缓存命中<100ms）
- **重连恢复时间**: <5秒（指数退避）

### 资源使用
- **内存占用**: <10MB（包含50MB磁盘缓存）
- **CPU使用**: <5%（空闲时<1%）
- **网络流量**: 优化压缩，减少50%冗余请求

## 🔧 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查网络状态和API密钥配置
   - 查看日志中的证书钉扎错误
   - 验证防火墙和代理设置

2. **REST请求超时**
   - 检查网络连接质量
   - 调整超时配置参数
   - 查看重试机制日志

3. **缓存问题**
   - 清理HTTP缓存目录
   - 检查磁盘空间是否充足
   - 验证缓存策略配置

### 调试模式
```kotlin
// 启用详细日志
RestConfig(enableLogging = true)
WsConfig(/* 调试配置 */)
```

## 📈 监控与指标

### 网络指标收集
- 请求成功率、响应时间、错误率
- WebSocket连接稳定性、重连频率
- 缓存命中率、流量使用情况

### 告警配置
- 连接失败率 >10%
- 平均响应时间 >3秒
- 重连频率 >5次/分钟

## 🔄 版本历史

- **V2.0** (Current): Task2架构实现，统一OkHttp栈
- **V1.5**: NetworkWatchdog事件流，UI Banner集成
- **V1.0**: 基础WebSocket和REST客户端实现

## 📞 支持

- **文档**: 查看`docs/`目录下的详细文档
- **问题反馈**: 创建GitHub Issue
- **架构讨论**: 参考`docs/workflow.md`工作流程

---

**Core Network模块为GymBro项目提供了企业级的网络基础设施，支持高并发、低延迟的AI交互体验。**
