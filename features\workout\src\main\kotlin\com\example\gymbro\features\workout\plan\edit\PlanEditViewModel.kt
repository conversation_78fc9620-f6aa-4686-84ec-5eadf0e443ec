package com.example.gymbro.features.workout.plan.edit

import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.domain.workout.usecase.ManageWorkoutPlansUseCase
import com.example.gymbro.domain.workout.usecase.template.TemplateManagementUseCase
import com.example.gymbro.features.workout.plan.edit.internal.effect.PlanEditEffectHandler
import com.example.gymbro.features.workout.plan.edit.internal.reducer.PlanEditReducer
import com.example.gymbro.features.workout.plan.edit.json.toJson
import com.example.gymbro.features.workout.plan.edit.json.toCalendarJson
import com.example.gymbro.features.workout.plan.edit.json.toPlanEditState
import com.example.gymbro.features.workout.plan.edit.json.getStatistics
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * Plan Edit ViewModel - 训练计划编辑ViewModel
 *
 * 🎯 核心功能：
 * - 计划数据的加载和保存
 * - 模板数据管理
 * - 用户交互状态管理
 * - 错误处理和重试逻辑
 *
 * 🏗️ 架构特点：
 * - 继承BaseMviViewModel，严格MVI 2.0合规
 * - 使用UseCase处理业务逻辑
 * - 通过Reducer管理状态转换
 * - 通过EffectHandler处理副作用
 */
@HiltViewModel
class PlanEditViewModel @Inject constructor(
    private val manageWorkoutPlansUseCase: ManageWorkoutPlansUseCase,
    private val templateManagementUseCase: TemplateManagementUseCase,
    private val planEditReducer: PlanEditReducer,
    private val planEditEffectHandler: PlanEditEffectHandler,
) : BaseMviViewModel<PlanEditContract.Intent, PlanEditContract.State, PlanEditContract.Effect>(
    initialState = PlanEditContract.State()
) {

    override val reducer: Reducer<PlanEditContract.Intent, PlanEditContract.State, PlanEditContract.Effect> = planEditReducer

    init {
        // 🔥【强制】唯一正确的 Effect 处理方式
        initializeEffectHandler()

        // 初始化加载模板
        dispatch(PlanEditContract.Intent.LoadTemplates)
    }

    // 🔥【强制】唯一正确的 Effect 处理方式
    override fun initializeEffectHandler() {
        planEditEffectHandler.initialize(
            scope = handlerScope, // 使用基类提供的独立协程作用域
            dispatch = this::dispatch // 传递 dispatch 方法引用
        )

        viewModelScope.launch {
            effect.collect { effect ->
                planEditEffectHandler.handle(effect)
            }
        }
    }

    // === 公共方法供UI调用 ===

    /**
     * 加载计划数据
     */
    fun loadPlan(planId: String) {
        dispatch(PlanEditContract.Intent.LoadPlan(planId))
    }

    /**
     * 保存计划
     */
    fun savePlan() {
        dispatch(PlanEditContract.Intent.SavePlan)
    }

    /**
     * 切换视图模式
     */
    fun toggleCalendarView() {
        dispatch(PlanEditContract.Intent.ToggleCalendarView)
    }

    /**
     * 更新日计划
     */
    fun updateDayPlan(week: Int, day: Int, dayPlan: com.example.gymbro.domain.workout.model.plan.DayPlan) {
        dispatch(PlanEditContract.Intent.UpdateDayPlan(week, day, dayPlan))
    }

    /**
     * 显示模板选择器
     */
    fun showTemplateSelector(week: Int, day: Int) {
        dispatch(PlanEditContract.Intent.ShowTemplateSelector(week, day))
    }

    /**
     * 隐藏模板选择器
     */
    fun hideTemplateSelector() {
        dispatch(PlanEditContract.Intent.HideTemplateSelector)
    }

    /**
     * 选择模板
     */
    fun selectTemplate(template: com.example.gymbro.domain.workout.model.template.WorkoutTemplate) {
        dispatch(PlanEditContract.Intent.SelectTemplate(template))
    }

    /**
     * 切换休息日状态
     */
    fun toggleRestDay(week: Int, day: Int) {
        dispatch(PlanEditContract.Intent.ToggleRestDay(week, day))
    }

    /**
     * 添加模板到指定日期
     */
    fun addTemplateToDay(week: Int, day: Int, templateId: String) {
        dispatch(PlanEditContract.Intent.AddTemplateToDay(week, day, templateId))
    }

    /**
     * 从指定日期移除模板
     */
    fun removeTemplateFromDay(week: Int, day: Int, templateIndex: Int) {
        dispatch(PlanEditContract.Intent.RemoveTemplateFromDay(week, day, templateIndex))
    }

    /**
     * 清除错误状态
     */
    fun clearError() {
        dispatch(PlanEditContract.Intent.ClearError)
    }

    /**
     * 重试上次操作
     */
    fun retryLastAction() {
        dispatch(PlanEditContract.Intent.RetryLastAction)
    }

    // === JSON序列化功能 ===

    /**
     * 导出当前状态为JSON格式
     * 用于数据备份、调试或与其他系统集成
     *
     * @return JSON字符串
     */
    fun exportStateAsJson(): String {
        return try {
            state.value.toJson()
        } catch (e: Exception) {
            Timber.e(e, "导出状态为JSON失败")
            "{\"error\": \"export_failed\"}"
        }
    }

    /**
     * 导出当前状态为日历格式JSON
     * 用于与日历系统集成或数据可视化
     *
     * @return 日历格式JSON字符串
     */
    fun exportStateAsCalendarJson(): String {
        return try {
            state.value.toCalendarJson()
        } catch (e: Exception) {
            Timber.e(e, "导出状态为日历JSON失败")
            "{\"error\": \"calendar_export_failed\"}"
        }
    }

    /**
     * 从JSON字符串导入状态
     * 用于数据恢复或从其他系统导入
     *
     * @param jsonString JSON字符串
     */
    fun importStateFromJson(jsonString: String) {
        viewModelScope.launch {
            try {
                val importedState = jsonString.toPlanEditState()
                // 更新状态，保留当前的UI状态
                val newState = importedState.copy(
                    isLoading = false,
                    isSaving = false,
                    error = null,
                    showTemplateSelector = state.value.showTemplateSelector,
                    availableTemplates = state.value.availableTemplates
                )
                updateState(newState)
                Timber.i("成功从JSON导入状态: planId=${newState.planId}")
            } catch (e: Exception) {
                Timber.e(e, "从JSON导入状态失败")
                dispatch(PlanEditContract.Intent.ErrorResult(
                    com.example.gymbro.core.ui.text.UiText.DynamicString("导入失败: ${e.message}")
                ))
            }
        }
    }

    /**
     * 获取当前状态的统计信息
     * 用于数据分析和用户反馈
     *
     * @return 统计信息Map
     */
    fun getStateStatistics(): Map<String, Any> {
        return state.value.getStatistics()
    }

    /**
     * 检查当前状态是否可以安全序列化
     *
     * @return 是否可以序列化
     */
    fun canExportState(): Boolean {
        return try {
            state.value.toJson()
            true
        } catch (e: Exception) {
            Timber.w(e, "状态序列化检查失败")
            false
        }
    }

    /**
     * 获取状态摘要信息（用于日志和调试）
     *
     * @return 摘要信息字符串
     */
    fun getStateSummary(): String {
        return try {
            val stats = state.value.getStatistics()
            "计划: ${state.value.planName} | " +
                    "周数: ${stats["total_weeks"]} | " +
                    "训练天: ${stats["workout_days"]} | " +
                    "完成率: ${"%.1f".format((stats["completion_rate"] as Double) * 100)}%"
        } catch (e: Exception) {
            "状态摘要获取失败"
        }
    }

    // === 私有辅助方法 ===

    /**
     * 直接更新状态（用于JSON导入）
     */
    private fun updateState(newState: PlanEditContract.State) {
        // 这里需要根据实际的BaseMviViewModel实现来更新状态
        // 通常通过发送特殊的Intent来更新整个状态
        dispatch(PlanEditContract.Intent.LoadPlanResult(
            planId = newState.planId,
            weekPlans = newState.weekPlans
        ))
    }
}
