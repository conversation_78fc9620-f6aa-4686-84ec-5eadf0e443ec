package com.example.gymbro.core.ml.monitor

import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * BGE内存监控器 - 现代设备优化版
 *
 * 🎯 功能特性：
 * - 实时监控内存使用情况
 * - 根据设备内存自动调整阈值
 * - 针对现代8GB+内存设备优化
 * - 更宽松的内存管理，避免过度限制性能
 */
@Singleton
class BgeMemoryMonitor @Inject constructor() {

    /**
     * 内存指标数据类
     */
    data class MemoryMetrics(
        val usedMemory: Long,
        val freeMemory: Long,
        val totalMemory: Long,
        val maxMemory: Long,
        val timestamp: Long = System.currentTimeMillis(),
    ) {
        val usagePercentage: Float = usedMemory.toFloat() / maxMemory

        // 🔥 根据设备内存动态调整阈值
        val deviceMemoryGB: Float = maxMemory / (1024f * 1024f * 1024f)

        // 🔥 现代设备优化：更宽松的内存管理
        val isHighPressure: Boolean = when {
            deviceMemoryGB >= 8f -> usagePercentage > 0.85f // 8GB+设备：85%才警戒
            deviceMemoryGB >= 6f -> usagePercentage > 0.82f // 6GB设备：82%警戒
            deviceMemoryGB >= 4f -> usagePercentage > 0.8f // 4GB设备：80%警戒
            else -> usagePercentage > 0.75f // 低内存设备：75%警戒
        }

        val isCriticalPressure: Boolean = when {
            deviceMemoryGB >= 8f -> usagePercentage > 0.92f // 8GB+设备：92%才严重
            deviceMemoryGB >= 6f -> usagePercentage > 0.9f // 6GB设备：90%严重
            else -> usagePercentage > 0.88f // 其他设备：88%严重
        }

        val availableMemoryMB: Long = freeMemory / (1024 * 1024)
        val usedMemoryMB: Long = usedMemory / (1024 * 1024)
        val maxMemoryMB: Long = maxMemory / (1024 * 1024)

        // 🔥 动态BGE运行阈值：优化内存利用率，减少零向量返回
        val bgeWarningThresholdMB: Long = when {
            deviceMemoryGB >= 8f -> 200L // 8GB+设备：200MB警戒线（降低100MB）
            deviceMemoryGB >= 6f -> 180L // 6GB设备：180MB警戒线（降低70MB）
            deviceMemoryGB >= 4f -> 150L // 4GB设备：150MB警戒线（降低50MB）
            else -> 120L // 低内存设备：120MB警戒线（降低30MB）
        }

        val bgeOptimalThresholdMB: Long = when {
            deviceMemoryGB >= 8f -> 350L // 8GB+设备：350MB理想运行（降低150MB）
            deviceMemoryGB >= 6f -> 280L // 6GB设备：280MB理想运行（降低120MB）
            deviceMemoryGB >= 4f -> 220L // 4GB设备：220MB理想运行（降低80MB）
            else -> 160L // 低内存设备：160MB理想运行（降低40MB）
        }
    }

    /**
     * 收集当前内存指标
     */
    fun collectMetrics(): MemoryMetrics {
        val runtime = Runtime.getRuntime()
        return MemoryMetrics(
            usedMemory = runtime.totalMemory() - runtime.freeMemory(),
            freeMemory = runtime.freeMemory(),
            totalMemory = runtime.totalMemory(),
            maxMemory = runtime.maxMemory(),
        )
    }

    /**
     * 记录内存状态到日志 - 现代设备优化版
     */
    fun logMemoryStatus() {
        val metrics = collectMetrics()

        when {
            metrics.isCriticalPressure -> {
                Timber.e(
                    "🚨 BGE内存严重告警: ${(metrics.usagePercentage * 100).toInt()}% (${metrics.usedMemoryMB}MB/${metrics.maxMemoryMB}MB)",
                )
                Timber.e("🚨 设备内存: ${String.format("%.1f", metrics.deviceMemoryGB)}GB, 建议立即GC或降级")
            }
            metrics.isHighPressure -> {
                Timber.w(
                    "⚠️ BGE内存压力告警: ${(metrics.usagePercentage * 100).toInt()}% (${metrics.usedMemoryMB}MB/${metrics.maxMemoryMB}MB)",
                )
                Timber.w("⚠️ 设备内存: ${String.format("%.1f", metrics.deviceMemoryGB)}GB, 建议适度优化")
            }
            metrics.availableMemoryMB < metrics.bgeWarningThresholdMB -> {
                Timber.w(
                    "⚠️ BGE可用内存偏低: ${metrics.availableMemoryMB}MB < ${metrics.bgeWarningThresholdMB}MB (警戒线)",
                )
            }
            else -> {
                Timber.d(
                    "🔍 BGE内存状态良好: ${(metrics.usagePercentage * 100).toInt()}% (${metrics.usedMemoryMB}MB/${metrics.maxMemoryMB}MB)",
                )
                Timber.d(
                    "🔍 设备内存: ${String.format(
                        "%.1f",
                        metrics.deviceMemoryGB,
                    )}GB, 可用: ${metrics.availableMemoryMB}MB",
                )
            }
        }
    }

    /**
     * 检查是否建议触发GC - 现代设备优化版
     */
    fun shouldTriggerGC(): Boolean {
        val metrics = collectMetrics()
        // 🔥 根据设备内存动态调整GC触发阈值
        val gcThreshold = when {
            metrics.deviceMemoryGB >= 8f -> 0.85f // 8GB+设备：85%才触发GC
            metrics.deviceMemoryGB >= 6f -> 0.82f // 6GB设备：82%触发GC
            metrics.deviceMemoryGB >= 4f -> 0.8f // 4GB设备：80%触发GC
            else -> 0.75f // 低内存设备：75%触发GC
        }
        return metrics.usagePercentage > gcThreshold
    }

    /**
     * 获取建议的批处理大小 - 优化版：提高内存利用率
     */
    fun getRecommendedBatchSize(): Int {
        val metrics = collectMetrics()
        return when {
            metrics.isCriticalPressure -> 1
            metrics.isHighPressure -> when {
                metrics.deviceMemoryGB >= 8f -> 6 // 8GB+设备即使高压力也可以处理6个（提高50%）
                metrics.deviceMemoryGB >= 6f -> 4 // 6GB设备处理4个（提高33%）
                else -> 3 // 其他设备处理3个（提高50%）
            }
            metrics.availableMemoryMB < metrics.bgeWarningThresholdMB -> when {
                metrics.deviceMemoryGB >= 8f -> 8 // 8GB+设备可以处理8个（提高33%）
                metrics.deviceMemoryGB >= 6f -> 6 // 6GB设备处理6个（提高50%）
                else -> 4 // 其他设备处理4个（提高33%）
            }
            else -> when {
                metrics.deviceMemoryGB >= 8f -> 16 // 8GB+设备可以处理16个（提高33%）
                metrics.deviceMemoryGB >= 6f -> 12 // 6GB设备处理12个（提高20%）
                metrics.deviceMemoryGB >= 4f -> 10 // 4GB设备处理10个（提高25%）
                else -> 8 // 低内存设备处理8个（提高33%）
            }
        }
    }

    /**
     * 获取内存状态摘要 - 现代设备优化版
     */
    fun getMemoryStatusSummary(): String {
        val metrics = collectMetrics()
        return "Memory: ${metrics.usedMemoryMB}MB/${metrics.maxMemoryMB}MB (${(metrics.usagePercentage * 100).toInt()}%) | " +
            "Device: ${String.format("%.1f", metrics.deviceMemoryGB)}GB | " +
            "Available: ${metrics.availableMemoryMB}MB | " +
            "BGE Threshold: ${metrics.bgeWarningThresholdMB}MB"
    }

    /**
     * 检查是否有足够内存进行BGE操作 - 现代设备优化版
     */
    fun hasEnoughMemoryForBGE(): Boolean {
        val metrics = collectMetrics()
        // 🔥 使用动态阈值替换固定的200MB限制
        return metrics.availableMemoryMB >= metrics.bgeWarningThresholdMB
    }

    /**
     * 获取BGE运行状态评估
     */
    fun getBgeRunningStatus(): BgeRunningStatus {
        val metrics = collectMetrics()
        return when {
            metrics.availableMemoryMB >= metrics.bgeOptimalThresholdMB -> BgeRunningStatus.OPTIMAL
            metrics.availableMemoryMB >= metrics.bgeWarningThresholdMB -> BgeRunningStatus.GOOD
            metrics.isCriticalPressure -> BgeRunningStatus.CRITICAL
            else -> BgeRunningStatus.LIMITED
        }
    }

    /**
     * BGE运行状态枚举
     */
    enum class BgeRunningStatus {
        OPTIMAL, // 理想状态：可以全速运行
        GOOD, // 良好状态：正常运行
        LIMITED, // 受限状态：需要降低性能
        CRITICAL, // 危险状态：建议暂停或降级
    }
}
