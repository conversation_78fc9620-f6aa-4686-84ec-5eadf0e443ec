# GymBro CICD Detekt Configuration - MVI Architecture Rules
# 专注于ktlint和detekt的简化配置，强化MVI规范检查
# 强制执行：函数<80行、文件<500行、timber log限制等约束

build:
  maxIssues: 0
  excludeCorrectable: false
  weights: {}

config:
  validation: true
  warningsAsErrors: true
  checkExhaustiveness: false
  excludes: '.*/test/.*,.*/androidTest/.*'

processors:
  active: true
  exclude:
    - 'DetektProgressListener'

console-reports:
  active: true
  exclude:
    - 'ProjectStatisticsReport'
    - 'ComplexityReport'
    - 'NotificationReport'

output-reports:
  active: true
  exclude: []

# MVI Architecture Rules - Core Focus
# ===================================

comments:
  active: false

naming:
  active: true
  ClassNaming:
    active: true
    classPattern: '[A-Z][a-zA-Z0-9]*'
  FunctionNaming:
    active: true
    functionPattern: '[a-z][a-zA-Z0-9]*'
    excludeClassPattern: '$^'
    ignoreAnnotated: ['Composable']
  VariableNaming:
    active: true
    variablePattern: '[a-z][A-Za-z0-9]*'
    privateVariablePattern: '(_)?[a-z][A-Za-z0-9]*'

style:
  active: true
  DataClassShouldBeImmutable:
    active: true
  ForbiddenComment:
    active: true
    comments:
      - reason: 'Forbidden TODO marker in comment'
        value: 'TODO:'
      - reason: 'Forbidden FIXME marker in comment'
        value: 'FIXME:'
      - reason: 'Forbidden STOPSHIP marker in comment'
        value: 'STOPSHIP:'
  MaxLineLength:
    active: true
    maxLineLength: 120
    excludePackageStatements: true
    excludeImportStatements: true
  VarCouldBeVal:
    active: true
    ignoreLateinitVar: false
  WildcardImport:
    active: true
    excludeImports:
      - 'java.util.*'

complexity:
  active: true
  CyclomaticComplexMethod:
    active: true
    threshold: 10  # 降低复杂度阈值，确保函数简洁
  LargeClass:
    active: true
    threshold: 500  # 🔥 强制文件行数<500行
  LongMethod:
    active: true
    threshold: 80   # 🔥 强制函数行数<80行
  LongParameterList:
    active: true
    functionThreshold: 6
    constructorThreshold: 7
    ignoreDefaultParameters: false
    ignoreDataClasses: true
  NestedBlockDepth:
    active: true
    threshold: 4
  TooManyFunctions:
    active: true
    excludes: ['.*/test/.*', '.*/androidTest/.*']
    thresholdInFiles: 11
    thresholdInClasses: 11
    thresholdInInterfaces: 11
    thresholdInObjects: 11
    thresholdInEnums: 11
  StringLiteralDuplication:
    active: true
    excludes: ['.*/test/.*', '.*/androidTest/.*']
    threshold: 3
    ignoreAnnotation: true
    excludeStringsWithLessThan5Characters: true
  ComplexCondition:
    active: true
    threshold: 4

potential-bugs:
  active: true
  DoubleMutabilityForCollection:
    active: true
    mutableTypes:
      - 'kotlin.collections.MutableList'
      - 'kotlin.collections.MutableMap'
      - 'kotlin.collections.MutableSet'
      - 'java.util.ArrayList'
      - 'java.util.LinkedHashSet'
      - 'java.util.HashSet'
      - 'java.util.LinkedHashMap'
      - 'java.util.HashMap'
  EqualsAlwaysReturnsTrueOrFalse:
    active: true
  EqualsWithHashCodeExist:
    active: true
  UnnecessaryNotNullOperator:
    active: true
  UnnecessarySafeCall:
    active: true
  UnreachableCode:
    active: true
  UnsafeCallOnNullableType:
    active: true
    excludes: ['.*/test/.*', '.*/androidTest/.*']

empty-blocks:
  active: true
  EmptyFunctionBlock:
    active: true
    ignoreOverridden: false
  EmptyClassBlock:
    active: true
  EmptyIfBlock:
    active: true
  EmptyWhenBlock:
    active: true

exceptions:
  active: true
  TooGenericExceptionCaught:
    active: true
    excludes: ['.*/test/.*', '.*/androidTest/.*']
    exceptionNames:
      - 'Error'
      - 'Exception'
      - 'RuntimeException'
      - 'Throwable'
  TooGenericExceptionThrown:
    active: true
    exceptionNames:
      - 'Error'
      - 'Exception'
      - 'RuntimeException'
      - 'Throwable'

performance:
  active: true
  ForEachOnRange:
    active: true
    excludes: ['.*/test/.*', '.*/androidTest/.*']
  SpreadOperator:
    active: true
    excludes: ['.*/test/.*', '.*/androidTest/.*']

coroutines:
  active: true
  GlobalCoroutineUsage:
    active: true
  InjectDispatcher:
    active: true
    dispatcherNames:
      - 'IO'
      - 'Default'
      - 'Unconfined'
  RedundantSuspendModifier:
    active: true
  SleepInsteadOfDelay:
    active: true

# ==============================================
# GymBro 项目自定义规则集（暂时禁用进行测试）
# ==============================================

# 注意：自定义规则暂时禁用，待构建完成后再启用
# MVI 架构相关规则
# gymbro-rules:
#   active: true
#   MviIntentNaming:
#     active: true
#   ImmutableStateClass:
#     active: true
#   
#   # Design Token 使用规则
#   NoHardcodedDesignValues:
#     active: true
#   UseWorkoutColors:
#     active: true
#   
#   # 日志规则
#   MaxTimberLogsPerFunction:
#     active: true
#     maxLogsPerFunction: 1
#   LoggingModuleRestriction:
#     active: true
#   
#   # 质量规则
#   NoTodoOrFixme:
#     active: true
#     keywords:
#       - "TODO"
#       - "FIXME"
#       - "HACK"
#       - "XXX"
