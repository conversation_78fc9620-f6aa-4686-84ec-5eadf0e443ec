package com.example.gymbro.startup

import com.example.gymbro.core.ai.prompt.registry.PromptRegistry
import com.example.gymbro.core.ai.tokenizer.TokenizerService
import com.example.gymbro.core.ml.service.BgeEngineManager
import com.example.gymbro.core.network.monitor.NetworkWatchdog
import com.example.gymbro.core.startup.StartupStatusProvider
import com.example.gymbro.domain.coach.config.AiProviderManager
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import timber.log.Timber
import com.example.gymbro.domain.workout.usecase.template.TemplatesDataManager
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 应用启动管理器 - 智能后台静默加载策略
 *
 * 核心职责：
 * 1. 分层并行启动所有重量级组件
 * 2. 网络层优先初始化策略
 * 3. 智能依赖关系管理
 * 4. 统一加载状态监控
 * 5. 优雅降级机制
 *
 * 启动策略：
 * - 第1层：网络基础设施（NetworkWatchdog、AiProviderManager）
 * - 第2层：AI核心组件（BGE引擎、PromptRegistry、TokenizerService）
 * - 第3层：功能模块（ThinkingBox、ChatHistory、PerformanceMonitor）
 *
 * 目标：用户进入Coach页面时实现"零等待"体验
 */
@Singleton
class AppStartupManager @Inject constructor(
    private val networkWatchdog: NetworkWatchdog,
    private val aiProviderManager: AiProviderManager,
    private val bgeEngineManager: BgeEngineManager,
    private val promptRegistry: PromptRegistry,
    private val tokenizerService: TokenizerService,
    private val templatesDataManager: TemplatesDataManager, // 🔥 新增：模板数据管理器
) : StartupStatusProvider {
    companion object {
        private const val TAG = "AppStartup"
        private const val STARTUP_TIMEOUT_MS = 30_000L // 30秒超时
    }

    /**
     * 启动阶段枚举
     */
    enum class StartupPhase {
        NOT_STARTED,
        NETWORK_LAYER_STARTING,
        NETWORK_LAYER_READY,
        AI_CORE_STARTING,
        AI_CORE_READY,
        FEATURE_MODULES_STARTING,
        FEATURE_MODULES_READY,
        ALL_READY,
        ERROR,
    }

    /**
     * 组件启动状态
     */
    data class ComponentStatus(
        val name: String,
        val isReady: Boolean = false,
        val error: Throwable? = null,
        val startTime: Long = 0L,
        val readyTime: Long = 0L,
    ) {
        val duration: Long get() = if (readyTime > 0) readyTime - startTime else 0L
    }

    /**
     * 启动状态
     */
    data class StartupState(
        val phase: StartupPhase = StartupPhase.NOT_STARTED,
        val components: Map<String, ComponentStatus> = emptyMap(),
        val overallProgress: Float = 0f,
        val isNetworkReady: Boolean = false,
        val isAiCoreReady: Boolean = false,
        val isAllReady: Boolean = false,
        val errors: List<Throwable> = emptyList(),
    )

    // 启动状态管理
    private val _startupState = MutableStateFlow(StartupState())
    val startupState: StateFlow<StartupState> = _startupState.asStateFlow()

    // 🔥 实现StartupStatusProvider接口 - 为features模块提供启动状态
    private val _startupReadiness = MutableStateFlow(
        StartupStatusProvider.StartupReadiness(
            isNetworkReady = false,
            isAiCoreReady = false,
            isBgeEngineReady = false,
            overallProgress = 0f,
            estimatedTimeToReady = 8000L,
            canStartCoach = false,
        ),
    )
    override val startupReadiness: StateFlow<StartupStatusProvider.StartupReadiness> =
        _startupReadiness.asStateFlow()

    // 启动协程作用域
    private val startupScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 组件状态跟踪
    private val componentStatuses = mutableMapOf<String, ComponentStatus>()

    // 🔥 用户交互窗口监控 - 5-10秒目标窗口
    private var appStartTime: Long = 0L
    private val USER_INTERACTION_WINDOW_MS = 10_000L // 10秒用户交互窗口

    init {
        Timber.tag(TAG).i("🚀 AppStartupManager初始化完成")
    }

    /**
     * 启动所有组件的后台加载
     * 使用分层并行策略，确保不阻塞主线程
     */
    fun startBackgroundLoading() {
        if (_startupState.value.phase != StartupPhase.NOT_STARTED) {
            Timber.tag(TAG).w("⚠️ 启动已在进行中，忽略重复调用")
            return
        }

        appStartTime = System.currentTimeMillis()
        Timber.tag(TAG).i("🎬 开始应用后台静默加载，目标：${USER_INTERACTION_WINDOW_MS}ms内完成核心服务")

        startupScope.launch {
            try {
                // 🔥 【用户体验优化】并行启动策略，确保10秒窗口内完成
                val startupJob = launch {
                    // 第1层：网络基础设施优先启动
                    startNetworkLayer()

                    // 第2层：AI核心组件并行启动
                    startAiCoreLayer()

                    // 第3层：功能模块启动
                    startFeatureModules()

                    // 标记全部就绪
                    markAllReady()
                }

                // 🔥 【用户交互窗口监控】检查是否在目标窗口内完成
                launch {
                    delay(USER_INTERACTION_WINDOW_MS)
                    checkUserInteractionWindowTarget()
                }

                startupJob.join()
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "❌ 启动过程发生错误")
                updatePhase(StartupPhase.ERROR)
                addError(e)
            }
        }
    }

    /**
     * 第1层：网络基础设施启动
     */
    private suspend fun startNetworkLayer() = coroutineScope {
        updatePhase(StartupPhase.NETWORK_LAYER_STARTING)
        Timber.tag(TAG).i("🌐 第1层：启动网络基础设施")

        // 并行启动网络组件
        val networkJobs = listOf(
            async { startComponent("NetworkWatchdog") { initNetworkWatchdog() } },
            async { startComponent("AiProviderManager") { initAiProviderManager() } },
        )

        // 等待所有网络组件就绪
        networkJobs.awaitAll()

        updatePhase(StartupPhase.NETWORK_LAYER_READY)
        updateNetworkReady(true)
        Timber.tag(TAG).i("✅ 第1层：网络基础设施就绪")
    }

    /**
     * 第2层：AI核心组件启动
     */
    private suspend fun startAiCoreLayer() = coroutineScope {
        updatePhase(StartupPhase.AI_CORE_STARTING)
        Timber.tag(TAG).i("🧠 第2层：启动AI核心组件")

        // 并行启动AI组件
        val aiJobs = listOf(
            async { startComponent("BgeEngine") { initBgeEngineAsync() } },
            async { startComponent("PromptRegistry") { initPromptRegistry() } },
            async { startComponent("TokenizerService") { initTokenizerService() } },
        )

        // 等待所有AI组件就绪
        aiJobs.awaitAll()

        updatePhase(StartupPhase.AI_CORE_READY)
        updateAiCoreReady(true)
        Timber.tag(TAG).i("✅ 第2层：AI核心组件就绪")
    }

    /**
     * 第3层：功能模块启动
     */
    private suspend fun startFeatureModules() = coroutineScope {
        updatePhase(StartupPhase.FEATURE_MODULES_STARTING)
        Timber.tag(TAG).i("⚙️ 第3层：启动功能模块")

        // 🔥 新增：启动模板数据管理器 - 实现单点查询架构
        val featureJobs = listOf(
            async { startComponent("TemplatesDataManager") { initTemplatesDataManager() } },
        )
        
        // 等待所有功能模块就绪
        featureJobs.awaitAll()

        updatePhase(StartupPhase.FEATURE_MODULES_READY)
        Timber.tag(TAG).i("✅ 第3层：功能模块就绪")
    }

    /**
     * 标记全部就绪
     */
    private fun markAllReady() {
        updatePhase(StartupPhase.ALL_READY)
        updateAllReady(true)

        val totalElapsedTime = System.currentTimeMillis() - appStartTime
        val totalDuration = componentStatuses.values.sumOf { it.duration }

        Timber.tag(TAG).i("🎉 所有组件启动完成！")
        Timber.tag(TAG).i("  - 总耗时: ${totalElapsedTime}ms")
        Timber.tag(TAG).i("  - 组件耗时: ${totalDuration}ms")
        Timber.tag(TAG)
            .i("  - 用户体验: ${if (totalElapsedTime <= USER_INTERACTION_WINDOW_MS) "✅ 优秀" else "⚠️ 需优化"}")

        // 打印启动报告
        logStartupReport()
    }

    /**
     * 🔥 检查用户交互窗口目标是否达成
     */
    private fun checkUserInteractionWindowTarget() {
        val elapsedTime = System.currentTimeMillis() - appStartTime
        val currentState = _startupState.value

        Timber.tag(TAG).i("🎯 用户交互窗口检查 (${elapsedTime}ms):")
        Timber.tag(TAG).i("  - 网络就绪: ${currentState.isNetworkReady}")
        Timber.tag(TAG).i("  - AI核心就绪: ${currentState.isAiCoreReady}")
        Timber.tag(TAG).i("  - 整体进度: ${(currentState.overallProgress * 100).toInt()}%")

        when {
            currentState.isAllReady -> {
                Timber.tag(TAG).i("🎉 优秀！${elapsedTime}ms内完成所有组件初始化")
            }

            canStartCoachModule() -> {
                Timber.tag(TAG).i("✅ 良好！${elapsedTime}ms内Coach模块可正常启动")
            }

            currentState.isNetworkReady -> {
                Timber.tag(TAG).w("⚠️ 网络就绪但AI核心未完成，用户可能需要等待")
            }

            else -> {
                Timber.tag(TAG).e("❌ 关键组件未在${USER_INTERACTION_WINDOW_MS}ms内就绪，用户体验受影响")
                // 记录性能问题，供后续优化分析
                recordPerformanceIssue("STARTUP_TIMEOUT", elapsedTime)
            }
        }
    }

    /**
     * 记录性能问题
     */
    private fun recordPerformanceIssue(issue: String, elapsedTime: Long) {
        val performanceData = mapOf(
            "issue" to issue,
            "elapsedTime" to elapsedTime,
            "networkReady" to _startupState.value.isNetworkReady,
            "aiCoreReady" to _startupState.value.isAiCoreReady,
            "overallProgress" to _startupState.value.overallProgress,
            "componentStatuses" to componentStatuses.mapValues {
                mapOf(
                    "isReady" to it.value.isReady,
                    "duration" to it.value.duration,
                    "hasError" to (it.value.error != null),
                )
            },
        )

        Timber.tag("STARTUP-PERFORMANCE").w("📊 性能问题记录: $performanceData")
    }

    /**
     * 启动单个组件的通用方法
     */
    private suspend fun startComponent(
        name: String,
        initializer: suspend () -> Unit,
    ) {
        val startTime = System.currentTimeMillis()
        updateComponentStatus(name, ComponentStatus(name, false, null, startTime))

        try {
            Timber.tag(TAG).d("🔄 启动组件: $name")

            withTimeout(STARTUP_TIMEOUT_MS) {
                initializer()
            }

            val readyTime = System.currentTimeMillis()
            updateComponentStatus(name, ComponentStatus(name, true, null, startTime, readyTime))

            Timber.tag(TAG).d("✅ 组件就绪: $name (${readyTime - startTime}ms)")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ 组件启动失败: $name")
            updateComponentStatus(name, ComponentStatus(name, false, e, startTime))
            addError(e)
        }
    }

    // ================================
    // 组件初始化方法
    // ================================

    private suspend fun initNetworkWatchdog() {
        // 🔥 【架构修正】在应用启动时统一初始化网络监控
        // 这是网络层的核心职责，应该在应用级别管理生命周期
        networkWatchdog.startWatching()

        // 等待网络状态稳定，确保后续模块可以依赖网络服务
        delay(500)

        Timber.tag(TAG).d("🌐 NetworkWatchdog已在应用层启动，功能模块可安全依赖网络状态")
    }

    private suspend fun initAiProviderManager() {
        // 触发lazy属性初始化
        aiProviderManager.availableProviders
        // 预热当前提供商
        aiProviderManager.currentProvider.first()
    }

    private suspend fun initBgeEngineAsync() {
        // 🔥 【启动优化】智能BGE引擎初始化策略
        // 目标：在用户进入Coach页面前完成预热，实现零等待体验

        val startTime = System.currentTimeMillis()
        Timber.tag(TAG).d("🧠 开始BGE引擎异步初始化和预热...")

        startupScope.launch {
            try {
                // 🔥 Step 1: 触发异步初始化（非阻塞）
                bgeEngineManager.initialize()

                // 🔥 Step 2: 等待初始化完成，但设置合理超时
                val initResult = withTimeoutOrNull(15_000L) {
                    bgeEngineManager.engineStatus.first { status ->
                        when (status) {
                            com.example.gymbro.core.ml.embedding.EngineStatus.READY -> {
                                val duration = System.currentTimeMillis() - startTime
                                Timber.tag(TAG).i("✅ BGE引擎就绪完成，耗时: ${duration}ms")
                                true
                            }

                            com.example.gymbro.core.ml.embedding.EngineStatus.ERROR -> {
                                Timber.tag(TAG).e("❌ BGE引擎初始化失败")
                                true
                            }

                            else -> false
                        }
                    }
                }

                if (initResult == null) {
                    Timber.tag(TAG).w("⏰ BGE引擎初始化超时，将在后台继续")
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "❌ BGE引擎异步初始化异常")
            }
        }

        // 🔥 【非阻塞策略】不等待BGE完成，让其他组件并行启动
        // BGE引擎会在后台继续初始化，Coach模块启动时会自动检测就绪状态
    }

    private suspend fun initPromptRegistry() {
        // PromptRegistry的异步初始化将在后续实现
        // 目前确保基本配置可用
        promptRegistry.getCurrentConfig()
    }

    private suspend fun initTokenizerService() {
        // TokenizerService通常是轻量级的
        // 主要确保实例化和基本功能可用
    }

    private suspend fun initTemplatesDataManager() {
        // 🔥 【单点查询架构】初始化模板数据管理器
        // 目标：确保整个应用只有一个地方查询模板数据
        val startTime = System.currentTimeMillis()
        Timber.tag(TAG).d("📋 开始Templates数据管理器初始化...")
        
        try {
            // 🔥 初始化数据管理器，触发首次数据加载
            templatesDataManager.initialize()
            
            val duration = System.currentTimeMillis() - startTime
            Timber.tag(TAG).i("✅ Templates数据管理器初始化完成，耗时: ${duration}ms")
            Timber.tag(TAG).d("📋 单点查询架构就绪，所有模板数据将通过唯一数据源分发")
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ Templates数据管理器初始化失败")
            throw e
        }
    }

    // ================================
    // 状态更新方法
    // ================================

    private fun updatePhase(phase: StartupPhase) {
        _startupState.value = _startupState.value.copy(
            phase = phase,
            overallProgress = calculateProgress(phase),
        )
        // 🔥 同步更新StartupReadiness
        updateStartupReadiness()
    }

    private fun updateComponentStatus(name: String, status: ComponentStatus) {
        componentStatuses[name] = status
        _startupState.value = _startupState.value.copy(
            components = componentStatuses.toMap(),
        )
        // 🔥 同步更新StartupReadiness
        updateStartupReadiness()
    }

    private fun updateNetworkReady(ready: Boolean) {
        _startupState.value = _startupState.value.copy(isNetworkReady = ready)
        // 🔥 同步更新StartupReadiness
        updateStartupReadiness()
    }

    private fun updateAiCoreReady(ready: Boolean) {
        _startupState.value = _startupState.value.copy(isAiCoreReady = ready)
        // 🔥 同步更新StartupReadiness
        updateStartupReadiness()
    }

    private fun updateAllReady(ready: Boolean) {
        _startupState.value = _startupState.value.copy(isAllReady = ready)
        // 🔥 同步更新StartupReadiness
        updateStartupReadiness()
    }

    /**
     * 🔥 更新StartupReadiness状态 - 供features模块使用
     */
    private fun updateStartupReadiness() {
        val current = _startupState.value
        _startupReadiness.value = StartupStatusProvider.StartupReadiness(
            isNetworkReady = current.isNetworkReady,
            isAiCoreReady = current.isAiCoreReady,
            isBgeEngineReady = componentStatuses["BgeEngine"]?.isReady ?: false,
            overallProgress = current.overallProgress,
            estimatedTimeToReady = estimateTimeToReady(),
            canStartCoach = canStartCoachModule(),
        )
    }

    private fun addError(error: Throwable) {
        _startupState.value = _startupState.value.copy(
            errors = _startupState.value.errors + error,
        )
    }

    private fun calculateProgress(phase: StartupPhase): Float = when (phase) {
        StartupPhase.NOT_STARTED -> 0f
        StartupPhase.NETWORK_LAYER_STARTING -> 0.1f
        StartupPhase.NETWORK_LAYER_READY -> 0.3f
        StartupPhase.AI_CORE_STARTING -> 0.4f
        StartupPhase.AI_CORE_READY -> 0.8f
        StartupPhase.FEATURE_MODULES_STARTING -> 0.9f
        StartupPhase.FEATURE_MODULES_READY -> 0.95f
        StartupPhase.ALL_READY -> 1.0f
        StartupPhase.ERROR -> _startupState.value.overallProgress // 保持当前进度
    }

    private fun logStartupReport() {
        val totalElapsedTime = System.currentTimeMillis() - appStartTime
        val report = buildString {
            appendLine("📊 启动性能报告:")
            appendLine("=============================")
            appendLine("🕐 总耗时: ${totalElapsedTime}ms")
            appendLine("🎯 目标窗口: ${USER_INTERACTION_WINDOW_MS}ms")
            appendLine(
                "📈 达成情况: ${if (totalElapsedTime <= USER_INTERACTION_WINDOW_MS) "✅ 优秀" else "⚠️ 超时 (+${totalElapsedTime - USER_INTERACTION_WINDOW_MS}ms)"}",
            )
            appendLine("📊 启动阶段: ${_startupState.value.phase}")
            appendLine("🌐 网络就绪: ${_startupState.value.isNetworkReady}")
            appendLine("🧠 AI核心就绪: ${_startupState.value.isAiCoreReady}")
            appendLine("📶 整体进度: ${(_startupState.value.overallProgress * 100).toInt()}%")
            appendLine("=============================")
            appendLine("🔧 组件详细状态:")
            componentStatuses.values.sortedBy { it.startTime }.forEach { status ->
                val statusIcon = when {
                    status.isReady -> "✅"
                    status.error != null -> "❌"
                    else -> "🔄"
                }
                val durationInfo = if (status.duration > 0) " (${status.duration}ms)" else ""
                appendLine("  $statusIcon ${status.name}$durationInfo")
                if (status.error != null) {
                    appendLine("      ↳ 错误: ${status.error.message}")
                }
            }
            if (_startupState.value.errors.isNotEmpty()) {
                appendLine("=============================")
                appendLine("❌ 错误汇总 (${_startupState.value.errors.size}个):")
                _startupState.value.errors.forEachIndexed { index, error ->
                    appendLine("  ${index + 1}. ${error.message}")
                }
            }
            appendLine("=============================")
        }
        Timber.tag(TAG).i(report)

        // 🔥 额外记录性能数据供分析
        Timber.tag("STARTUP-METRICS").i(
            """
            |📊 启动指标: {
            |  "totalTime": $totalElapsedTime,
            |  "targetTime": ${USER_INTERACTION_WINDOW_MS},
            |  "withinTarget": ${totalElapsedTime <= USER_INTERACTION_WINDOW_MS},
            |  "networkTime": ${componentStatuses["NetworkWatchdog"]?.duration ?: 0},
            |  "bgeTime": ${componentStatuses["BgeEngine"]?.duration ?: 0},
            |  "overallProgress": ${_startupState.value.overallProgress}
            |}
            """.trimMargin(),
        )
    }

    /**
     * 🔥 智能启动状态检查 - 用于Coach模块启动优化
     *
     * 针对实际使用场景优化：用户点击APP到进入coach主页需要5-10秒时间
     * 在这个窗口内确保所有核心服务就绪，实现零等待体验
     */
    override fun getStartupReadiness(): StartupStatusProvider.StartupReadiness {
        return _startupReadiness.value
    }

    /**
     * Coach模块启动就绪检查
     */
    override fun canStartCoachModule(): Boolean {
        // 最低要求：网络层就绪 + AI核心基本可用
        return _startupState.value.isNetworkReady &&
            _startupState.value.overallProgress >= 0.8f
    }

    /**
     * 预估到达就绪状态的时间（毫秒）
     */
    private fun estimateTimeToReady(): Long {
        val current = _startupState.value
        return when (current.phase) {
            StartupPhase.NOT_STARTED -> 8000L // 预估8秒
            StartupPhase.NETWORK_LAYER_STARTING -> 6000L
            StartupPhase.NETWORK_LAYER_READY -> 5000L
            StartupPhase.AI_CORE_STARTING -> 3000L
            StartupPhase.AI_CORE_READY -> 1000L
            StartupPhase.ALL_READY -> 0L
            else -> 2000L
        }
    }

    /**
     * 获取组件状态
     */
    fun getComponentStatus(name: String): ComponentStatus? = componentStatuses[name]

    /**
     * 清理资源
     */
    fun cleanup() {
        startupScope.cancel()
        Timber.tag(TAG).i("🧹 AppStartupManager资源清理完成")
    }
}
