package com.example.gymbro.core.network.detector

import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🚀 渐进式协议检测器 - 分阶段高效协议识别
 * 
 * 设计目标：
 * - 分阶段检测：50/100/200 token阶段性识别
 * - 高效算法：使用KMP算法优化特征匹配
 * - 准确率优先：多重验证机制
 * - 内存友好：使用CharArray避免频繁字符串分配
 */
@Singleton
class ProgressiveProtocolDetector @Inject constructor(
    private val featureMatcher: FeatureMatcher
) {
    companion object {
        private const val TAG = "ProgressiveProtocolDetector"
        
        // 检测阶段配置
        private const val QUICK_DETECTION_THRESHOLD = 50      // 快速检测阈值
        private const val STANDARD_DETECTION_THRESHOLD = 100  // 标准检测阈值
        private const val FINAL_DETECTION_THRESHOLD = 200     // 最终检测阈值
        
        // 检测缓冲区大小
        private const val DETECTION_BUFFER_SIZE = 256
    }
    
    // 使用CharArray而非StringBuilder，减少内存分配
    private val detectionBuffer = CharArray(DETECTION_BUFFER_SIZE)
    private var bufferLength = 0
    private var detectionComplete = false
    private var detectedType: ContentType? = null
    
    /**
     * 渐进式协议检测
     * 
     * @param newToken 新接收的token
     * @return 检测结果，包含置信度和内容类型
     */
    fun detectWithConfidence(newToken: String): DetectionResult {
        // 如果已经完成检测，直接返回结果
        if (detectionComplete && detectedType != null) {
            return DetectionResult.Confirmed(detectedType!!, 1.0f)
        }
        
        // 添加新token到检测缓冲区
        val added = addTokenToBuffer(newToken)
        if (!added) {
            // 缓冲区已满，强制完成检测
            return forceCompleteDetection()
        }
        
        // 根据当前缓冲区长度选择检测策略
        return when {
            bufferLength >= QUICK_DETECTION_THRESHOLD && bufferLength < STANDARD_DETECTION_THRESHOLD -> {
                quickDetection()
            }
            bufferLength >= STANDARD_DETECTION_THRESHOLD && bufferLength < FINAL_DETECTION_THRESHOLD -> {
                standardDetection()
            }
            bufferLength >= FINAL_DETECTION_THRESHOLD -> {
                finalDetection()
            }
            else -> {
                DetectionResult.INSUFFICIENT_DATA
            }
        }
    }
    
    /**
     * 重置检测器状态
     */
    fun reset() {
        bufferLength = 0
        detectionComplete = false
        detectedType = null
        
        Timber.tag(TAG).d("🔄 检测器状态重置")
    }
    
    /**
     * 获取当前检测状态
     */
    fun getDetectionStatus(): DetectionStatus {
        return DetectionStatus(
            bufferLength = bufferLength,
            detectionComplete = detectionComplete,
            detectedType = detectedType,
            currentStage = getCurrentStage()
        )
    }
    
    /**
     * 添加token到缓冲区
     */
    private fun addTokenToBuffer(token: String): Boolean {
        val tokenChars = token.toCharArray()
        val availableSpace = detectionBuffer.size - bufferLength
        val copyLength = minOf(tokenChars.size, availableSpace)
        
        if (copyLength <= 0) {
            return false // 缓冲区已满
        }
        
        System.arraycopy(tokenChars, 0, detectionBuffer, bufferLength, copyLength)
        bufferLength += copyLength
        
        return true
    }
    
    /**
     * 快速检测（50 token）
     * 寻找明显的协议特征
     */
    private fun quickDetection(): DetectionResult {
        val content = String(detectionBuffer, 0, minOf(QUICK_DETECTION_THRESHOLD, bufferLength))
        
        return when {
            // JSON SSE格式快速识别
            featureMatcher.findPattern(detectionBuffer, bufferLength, FeatureMatcher.JSON_SSE_PATTERN) >= 0 -> {
                Timber.tag(TAG).d("🔍 快速检测: JSON SSE (置信度: 0.8)")
                DetectionResult.Probable(ContentType.JSON_SSE, 0.8f)
            }
            
            // ThinkingBox XML格式快速识别
            featureMatcher.findPattern(detectionBuffer, bufferLength, FeatureMatcher.XML_THINKING_PATTERN) >= 0 -> {
                Timber.tag(TAG).d("🔍 快速检测: ThinkingBox XML (置信度: 0.9)")
                DetectionResult.Probable(ContentType.XML_THINKING, 0.9f)
            }
            
            // JSON流格式快速识别
            content.trimStart().startsWith("{") -> {
                Timber.tag(TAG).d("🔍 快速检测: JSON Stream (置信度: 0.7)")
                DetectionResult.Probable(ContentType.JSON_STREAM, 0.7f)
            }
            
            else -> {
                DetectionResult.INSUFFICIENT_DATA
            }
        }
    }
    
    /**
     * 标准检测（100 token）
     * 提高检测准确率
     */
    private fun standardDetection(): DetectionResult {
        val content = String(detectionBuffer, 0, minOf(STANDARD_DETECTION_THRESHOLD, bufferLength))
        
        return when {
            // JSON SSE格式标准检测
            isJsonSseFormat(content) -> {
                Timber.tag(TAG).i("🔍 标准检测: JSON SSE 确认")
                DetectionResult.Confirmed(ContentType.JSON_SSE)
            }
            
            // ThinkingBox XML格式标准检测
            isThinkingBoxXml(content) -> {
                Timber.tag(TAG).i("🔍 标准检测: ThinkingBox XML 确认")
                DetectionResult.Confirmed(ContentType.XML_THINKING)
            }
            
            // 纯JSON流标准检测
            isPureJsonStream(content) -> {
                Timber.tag(TAG).i("🔍 标准检测: JSON Stream 确认")
                DetectionResult.Confirmed(ContentType.JSON_STREAM)
            }
            
            // WebSocket帧检测
            isWebSocketFrame(content) -> {
                Timber.tag(TAG).i("🔍 标准检测: WebSocket Frame 确认")
                DetectionResult.Confirmed(ContentType.WEBSOCKET_FRAME)
            }
            
            else -> {
                Timber.tag(TAG).d("🔍 标准检测: 可能是纯文本 (置信度: 0.6)")
                DetectionResult.Probable(ContentType.PLAIN_TEXT, 0.6f)
            }
        }
    }
    
    /**
     * 最终检测（200 token）
     * 确保检测准确性
     */
    private fun finalDetection(): DetectionResult {
        val content = String(detectionBuffer, 0, bufferLength)
        
        // 使用更复杂的算法进行最终判断
        val confidence = calculateConfidence(content)
        val contentType = determineContentType(content)
        
        detectionComplete = true
        detectedType = contentType
        
        Timber.tag(TAG).i("🔍 最终检测完成: type=$contentType, confidence=$confidence")
        
        return DetectionResult.Confirmed(contentType, confidence)
    }
    
    /**
     * 强制完成检测（缓冲区满时）
     */
    private fun forceCompleteDetection(): DetectionResult {
        val content = String(detectionBuffer, 0, bufferLength)
        val contentType = determineContentType(content)
        
        detectionComplete = true
        detectedType = contentType
        
        Timber.tag(TAG).w("🔍 强制完成检测: type=$contentType (缓冲区已满)")
        
        return DetectionResult.Confirmed(contentType, 0.8f)
    }
    
    /**
     * 获取当前检测阶段
     */
    private fun getCurrentStage(): DetectionStage {
        return when {
            bufferLength < QUICK_DETECTION_THRESHOLD -> DetectionStage.COLLECTING
            bufferLength < STANDARD_DETECTION_THRESHOLD -> DetectionStage.QUICK
            bufferLength < FINAL_DETECTION_THRESHOLD -> DetectionStage.STANDARD
            else -> DetectionStage.FINAL
        }
    }
    
    // 协议检测辅助方法
    private fun isJsonSseFormat(content: String): Boolean {
        return content.contains("data: {") && 
               (content.contains("\"content\"") || content.contains("\"delta\""))
    }
    
    private fun isPureJsonStream(content: String): Boolean {
        return content.trimStart().startsWith("{") && 
               (content.contains("\"text\"") || content.contains("\"message\""))
    }
    
    private fun isThinkingBoxXml(content: String): Boolean {
        return content.contains("<thinking>") || 
               content.contains("<segment") ||
               content.contains("</thinking>")
    }
    
    private fun isWebSocketFrame(content: String): Boolean {
        return content.startsWith("WS:") || content.contains("frame_type")
    }
    
    private fun calculateConfidence(content: String): Float {
        // 简化的置信度计算
        var confidence = 0.5f
        
        // 根据特征数量调整置信度
        if (content.contains("data: {")) confidence += 0.3f
        if (content.contains("<thinking>")) confidence += 0.4f
        if (content.contains("\"content\"")) confidence += 0.2f
        
        return confidence.coerceIn(0f, 1f)
    }
    
    private fun determineContentType(content: String): ContentType {
        return when {
            isJsonSseFormat(content) -> ContentType.JSON_SSE
            isThinkingBoxXml(content) -> ContentType.XML_THINKING
            isPureJsonStream(content) -> ContentType.JSON_STREAM
            isWebSocketFrame(content) -> ContentType.WEBSOCKET_FRAME
            else -> ContentType.PLAIN_TEXT
        }
    }
}

/**
 * 📝 内容类型枚举
 */
enum class ContentType {
    JSON_SSE,           // Server-Sent Events with JSON
    JSON_STREAM,        // Pure JSON streaming
    XML_THINKING,       // ThinkingBox XML format
    WEBSOCKET_FRAME,    // WebSocket binary frames
    PLAIN_TEXT          // Plain text streaming
}

/**
 * 🔍 检测结果密封类
 */
sealed class DetectionResult {
    object INSUFFICIENT_DATA : DetectionResult()
    data class Probable(val type: ContentType, val confidence: Float) : DetectionResult()
    data class Confirmed(val type: ContentType, val confidence: Float = 1.0f) : DetectionResult()
}

/**
 * 📊 检测状态信息
 */
data class DetectionStatus(
    val bufferLength: Int,
    val detectionComplete: Boolean,
    val detectedType: ContentType?,
    val currentStage: DetectionStage
)

/**
 * 🎯 检测阶段枚举
 */
enum class DetectionStage {
    COLLECTING,     // 收集数据阶段
    QUICK,          // 快速检测阶段
    STANDARD,       // 标准检测阶段
    FINAL           // 最终检测阶段
}
