package com.example.gymbro.features.coach.aicoach.internal.components

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.features.thinkingbox.ThinkingBoxStaticRenderer

/**
 * 🔥 【技术债务清理】此文件已标记为弃用 - AI响应渲染应完全由ThinkingBox模块负责
 *
 * ⚠️ 架构违规说明：
 * - **当前问题**：Coach模块不应该负责AI响应的渲染，这违反了职责分离原则
 * - **正确架构**：ThinkingBox模块应该负责所有AI响应的渲染（实时和历史）
 * - **Coach职责**：仅负责消息发送、对话管理、历史记录保存和数据持久化
 *
 * 🚧 迁移计划：
 * - [ ] 将历史消息渲染逻辑迁移到ThinkingBox模块
 * - [ ] 更新ChatInterface.kt等使用方，直接使用ThinkingBox组件
 * - [ ] 移除此文件和相关的AI响应渲染代码
 * - [ ] 确保Coach模块专注于业务逻辑，不涉及UI渲染
 *
 * @deprecated 此文件违反架构原则，应该被移除。AI响应渲染应完全由ThinkingBox模块负责。
 */

/**
 * Historical AI Response Renderer - Unified ThinkingBox Integration
 *
 * Uses the unified ThinkingBox component for consistent AI response rendering.
 * For historical messages, we create a minimal UI state to display the final content.
 *
 * @deprecated 违反架构原则：AI响应渲染应完全由ThinkingBox模块负责，Coach模块不应涉及UI渲染
 */
@Deprecated(
    message = "AI响应渲染应完全由ThinkingBox模块负责，Coach模块应专注于业务逻辑",
    replaceWith = ReplaceWith("ThinkingBoxStaticRenderer", "com.example.gymbro.features.thinkingbox.ThinkingBoxStaticRenderer")
)
@Composable
internal fun HistoricalAiResponseRenderer(
    message: AiCoachContract.MessageUi,
    onShowSummaryCard: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    // For historical messages, we only need to display the final content
    // The thinking process is not shown for historical messages to optimize performance
    StaticAiResponseRenderer(
        message = message,
        modifier = modifier,
    )
}

/**
 * Static AI Response Renderer - 富文本历史显示
 *
 * 🔥 【架构迁移】使用 ThinkingBox 模块的统一静态渲染器
 * - 完全委托给 ThinkingBox 模块处理 Final 内容渲染
 * - 支持 Markdown 格式：标题、表格、代码高亮、任务列表等
 * - 支持 Mermaid 图表渲染
 * - 禁用打字机效果，直接显示最终内容
 * - 保持与实时 ThinkingBox 一致的渲染质量
 *
 * @deprecated 违反架构原则：AI响应渲染应完全由ThinkingBox模块负责，Coach模块不应涉及UI渲染
 */
@Deprecated(
    message = "AI响应渲染应完全由ThinkingBox模块负责，Coach模块应专注于业务逻辑",
    replaceWith = ReplaceWith("ThinkingBoxStaticRenderer", "com.example.gymbro.features.thinkingbox.ThinkingBoxStaticRenderer")
)
@Composable
internal fun StaticAiResponseRenderer(
    message: AiCoachContract.MessageUi,
    modifier: Modifier = Modifier,
) {
    // 🔥 【架构迁移】使用 ThinkingBox 的统一静态渲染器
    val finalContent = message.finalMarkdown?.takeIf { it.isNotBlank() } ?: message.content

    if (finalContent.isNotBlank()) {
        ThinkingBoxStaticRenderer(
            finalMarkdown = finalContent,
            modifier = modifier, // ThinkingBoxStaticRenderer 内部已处理 fillMaxWidth
        )
    }
}
