package com.example.gymbro.features.thinkingbox.internal.presentation.ui

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.motion.MotionDurations
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.ColorTokens
import timber.log.Timber

/**
 * ScrollToBottomBtn - 滚动到底部按钮
 *
 * 带有动画效果的滚动按钮组件
 *
 * @param visible 是否可见
 * @param onClick 点击回调
 * @param modifier 修饰符
 */
@Composable
fun ScrollToBottomBtn(
    visible: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val scale by animateFloatAsState(
        targetValue = if (visible) 1f else 0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessLow,
        ),
        label = "scale",
    )

    val alpha by animateFloatAsState(
        targetValue = if (visible) 1f else 0f,
        animationSpec = tween(durationMillis = MotionDurations.Short),
        label = "alpha",
    )

    if (scale > 0f) {
        FloatingActionButton(
            onClick = onClick,
            modifier = modifier
                .size(Tokens.Icon.TouchTarget)
                .graphicsLayer {
                    scaleX = scale
                    scaleY = scale
                    this.alpha = alpha
                },
            containerColor = MaterialTheme.colorScheme.primary, // 🔥 【主题适配】使用主题感知颜色
            contentColor = MaterialTheme.colorScheme.onPrimary, // 🔥 【主题适配】使用主题感知颜色
        ) {
            Icon(
                imageVector = Icons.Default.KeyboardArrowDown,
                contentDescription = "滚动到底部",
            )
        }
        
        // 记录成功使用 Tokens 系统
        Timber.tag("DESIGN-SYSTEM").d("ScrollToBottomBtn 成功使用 MaterialTheme.colorScheme.primary 和 Tokens.Icon.TouchTarget")
    }
}

@GymBroPreview
@Composable
private fun ScrollToBottomBtnPreview() {
    GymBroTheme {
        ScrollToBottomBtn(
            visible = true,
            onClick = { /* 预览中无操作 */ },
        )
    }
}