# Core Network Module - 接口契约文档

## 📋 接口概述

Core Network模块提供简化统一的网络服务接口，专注于高性能的流处理、智能缓冲管理和直接输出通道。所有接口遵循Clean Architecture原则，确保高内聚、低耦合的设计。

## 🌊 流处理核心接口

### StreamingProcessor

**职责**: 提供统一的流数据处理服务

```kotlin
interface StreamingProcessor {
    /**
     * 处理Token流
     * @param messageId 消息ID，用于路由和隔离
     * @param tokenFlow Token数据流
     * @return 处理结果流
     */
    fun processTokenStream(
        messageId: String, 
        tokenFlow: Flow<String>
    ): Flow<ProcessingResult>

    /**
     * 处理事件流
     * @param messageId 消息ID
     * @param eventFlow 事件数据流
     * @return 处理结果流
     */
    fun processEventStream(
        messageId: String, 
        eventFlow: Flow<Any>
    ): Flow<ProcessingResult>

    /**
     * 停止特定消息的流处理
     * @param messageId 消息ID
     */
    suspend fun stopProcessing(messageId: String)

    /**
     * 获取处理状态
     * @param messageId 消息ID
     * @return 处理状态
     */
    fun getProcessingStatus(messageId: String): ProcessingStatus?
}
```

**实现类**: `StreamingProcessorImpl`
**结果类型**: `ProcessingResult`, `ProcessingStatus`
**错误处理**: 内置错误恢复和重试机制

### UnifiedTokenReceiver

**职责**: 统一的Token接收和预处理

```kotlin
interface UnifiedTokenReceiver {
    /**
     * 接收Token数据
     * @param source 数据源标识
     * @param token Token数据
     * @return 接收结果
     */
    suspend fun receiveToken(source: String, token: String): ReceiveResult

    /**
     * 批量接收Token
     * @param source 数据源标识
     * @param tokens Token列表
     * @return 批量接收结果
     */
    suspend fun receiveTokens(source: String, tokens: List<String>): BatchReceiveResult

    /**
     * 订阅Token流
     * @param source 数据源标识
     * @return Token流
     */
    fun subscribeTokens(source: String): Flow<String>

    /**
     * 获取接收统计
     * @return 接收统计信息
     */
    fun getReceiveStats(): ReceiveStats
}
```

### 使用示例

```kotlin
@Inject
lateinit var streamingProcessor: StreamingProcessor

@Inject
lateinit var tokenReceiver: UnifiedTokenReceiver

// 流式处理
val tokenFlow = tokenReceiver.subscribeTokens("ai-service")
streamingProcessor.processTokenStream("msg-123", tokenFlow)
    .collect { result ->
        when (result.status) {
            ProcessingStatus.SUCCESS -> println("处理成功: ${result.data}")
            ProcessingStatus.ERROR -> println("处理错误: ${result.error}")
        }
    }
```

## 📤 直接输出通道接口

### DirectOutputChannel

**职责**: 提供零中间层的直接输出服务

```kotlin
interface DirectOutputChannel {
    /**
     * 发送Token到指定通道
     * @param channelId 通道ID
     * @param token Token数据
     * @return 发送结果
     */
    suspend fun sendToken(channelId: String, token: String): OutputResult

    /**
     * 发送事件到指定通道
     * @param channelId 通道ID
     * @param event 事件数据
     * @return 发送结果
     */
    suspend fun sendEvent(channelId: String, event: Any): OutputResult

    /**
     * 批量发送数据
     * @param channelId 通道ID
     * @param data 数据列表
     * @return 批量发送结果
     */
    suspend fun sendBatch(channelId: String, data: List<Any>): BatchOutputResult

    /**
     * 关闭通道
     * @param channelId 通道ID
     */
    suspend fun closeChannel(channelId: String)

    /**
     * 获取通道状态
     * @param channelId 通道ID
     * @return 通道状态
     */
    fun getChannelStatus(channelId: String): ChannelStatus?

    /**
     * 处理结果流
     * @return 处理结果的Flow
     */
    val processingResults: Flow<OutputResult>
}
```

### ThinkingBoxAdapter

**职责**: ThinkingBox模块的专用适配器

```kotlin
interface ThinkingBoxAdapter {
    /**
     * 转发Token到ThinkingBox
     * @param messageId 消息ID
     * @param token Token数据
     * @return 转发结果
     */
    suspend fun forwardToken(messageId: String, token: String): ForwardResult

    /**
     * 转发事件到ThinkingBox
     * @param messageId 消息ID
     * @param event 事件数据
     * @return 转发结果
     */
    suspend fun forwardEvent(messageId: String, event: Any): ForwardResult

    /**
     * 获取适配器状态
     * @return 适配器状态
     */
    fun getAdapterStatus(): AdapterStatus

    /**
     * 重置适配器
     */
    suspend fun reset()
}
```

### 使用示例

```kotlin
@Inject
lateinit var directOutputChannel: DirectOutputChannel

@Inject
lateinit var thinkingBoxAdapter: ThinkingBoxAdapter

// 直接输出
val messageId = "conversation-123"
directOutputChannel.sendToken(messageId, "Hello from AI")
    .let { result ->
        when (result.status) {
            OutputStatus.SUCCESS -> println("发送成功")
            OutputStatus.FAILED -> println("发送失败: ${result.error}")
        }
    }

// 适配器转发
thinkingBoxAdapter.forwardToken(messageId, "Thinking...")
```

## 🧠 智能缓冲管理接口

### AdaptiveBufferManager

**职责**: 自适应缓冲区管理

```kotlin
interface AdaptiveBufferManager {
    /**
     * 缓冲数据
     * @param bufferId 缓冲区ID
     * @param data 要缓冲的数据
     * @return 缓冲结果
     */
    suspend fun bufferData(bufferId: String, data: Any): BufferResult

    /**
     * 获取缓冲数据
     * @param bufferId 缓冲区ID
     * @param count 获取数量，-1表示全部
     * @return 缓冲的数据
     */
    suspend fun getBufferedData(bufferId: String, count: Int = -1): List<Any>

    /**
     * 清空缓冲区
     * @param bufferId 缓冲区ID
     */
    suspend fun clearBuffer(bufferId: String)

    /**
     * 获取缓冲区状态
     * @param bufferId 缓冲区ID
     * @return 缓冲区状态
     */
    fun getBufferStatus(bufferId: String): BufferStatus?

    /**
     * 调整缓冲策略
     * @param strategy 新的缓冲策略
     */
    suspend fun adjustStrategy(strategy: BufferStrategy)

    /**
     * 获取性能指标
     * @return 缓冲性能指标
     */
    fun getPerformanceMetrics(): BufferMetrics
}
```

### SlidingWindowBuffer

**职责**: 滑动窗口缓冲实现

```kotlin
interface SlidingWindowBuffer<T> {
    /**
     * 添加数据到窗口
     * @param item 数据项
     */
    fun add(item: T)

    /**
     * 获取当前窗口数据
     * @return 窗口内的数据列表
     */
    fun getWindow(): List<T>

    /**
     * 获取窗口大小
     * @return 窗口大小
     */
    fun getWindowSize(): Int

    /**
     * 清空窗口
     */
    fun clear()

    /**
     * 窗口是否已满
     * @return 是否已满
     */
    fun isFull(): Boolean

    /**
     * 获取窗口使用率
     * @return 使用率百分比
     */
    fun getUsagePercentage(): Float
}
```

## 🔍 协议检测接口

### ProgressiveProtocolDetector

**职责**: 渐进式协议检测

```kotlin
interface ProgressiveProtocolDetector {
    /**
     * 检测服务器支持的协议
     * @param endpoint 服务器端点
     * @param timeout 检测超时时间
     * @return 检测结果
     */
    suspend fun detectSupportedProtocols(
        endpoint: String, 
        timeout: Long = 5000L
    ): ProtocolDetectionResult

    /**
     * 检测特定协议的支持情况
     * @param endpoint 服务器端点
     * @param protocol 要检测的协议
     * @return 支持情况
     */
    suspend fun detectProtocolSupport(
        endpoint: String, 
        protocol: NetworkProtocol
    ): ProtocolSupportResult

    /**
     * 获取推荐的协议配置
     * @param endpoint 服务器端点
     * @param requirements 需求配置
     * @return 推荐配置
     */
    suspend fun getRecommendedConfig(
        endpoint: String, 
        requirements: ProtocolRequirements
    ): ProtocolConfig

    /**
     * 缓存检测结果
     * @param endpoint 服务器端点
     * @param result 检测结果
     */
    suspend fun cacheDetectionResult(endpoint: String, result: ProtocolDetectionResult)
}
```

### FeatureMatcher

**职责**: 网络特征匹配

```kotlin
interface FeatureMatcher {
    /**
     * 匹配网络特征
     * @param endpoint 服务器端点
     * @param features 要匹配的特征列表
     * @return 匹配结果
     */
    suspend fun matchFeatures(
        endpoint: String, 
        features: List<NetworkFeature>
    ): FeatureMatchResult

    /**
     * 获取特征置信度
     * @param endpoint 服务器端点
     * @param feature 特征
     * @return 置信度分数 (0.0-1.0)
     */
    suspend fun getFeatureConfidence(
        endpoint: String, 
        feature: NetworkFeature
    ): Float

    /**
     * 更新特征库
     * @param features 新的特征定义
     */
    suspend fun updateFeatureLibrary(features: List<FeatureDefinition>)
}
```

## 🌐 REST客户端接口

### RestClient

**职责**: 提供统一的HTTP REST API调用服务

```kotlin
interface RestClient {
    /**
     * GET请求
     * @param url 请求URL
     * @param headers 请求头部
     * @return ApiResult封装的响应内容
     */
    suspend fun get(url: String, headers: Map<String, String> = emptyMap()): ApiResult<String>

    /**
     * POST请求
     * @param url 请求URL
     * @param body 请求体
     * @param headers 请求头部
     * @return ApiResult封装的响应内容
     */
    suspend fun post(url: String, body: String, headers: Map<String, String> = emptyMap()): ApiResult<String>

    /**
     * PUT请求
     * @param url 请求URL
     * @param body 请求体
     * @param headers 请求头部
     * @return ApiResult封装的响应内容
     */
    suspend fun put(url: String, body: String, headers: Map<String, String> = emptyMap()): ApiResult<String>

    /**
     * DELETE请求
     * @param url 请求URL
     * @param headers 请求头部
     * @return ApiResult封装的响应内容
     */
    suspend fun delete(url: String, headers: Map<String, String> = emptyMap()): ApiResult<String>

    /**
     * 流式请求（支持SSE）
     * @param url 请求URL
     * @param body 请求体
     * @param headers 请求头部
     * @return 流式响应
     */
    fun streamPost(url: String, body: String, headers: Map<String, String> = emptyMap()): Flow<String>
}
```

**实现类**: `RestClientImpl`
**结果封装**: `ApiResult<T>`
**拦截器链**: Auth → NetworkStatus → Logging → Retry

### SafeApiCall

**职责**: 安全的API调用工具

```kotlin
interface SafeApiCall {
    /**
     * 安全执行API调用
     * @param call API调用函数
     * @return 安全包装的结果
     */
    suspend fun <T> execute(call: suspend () -> T): ApiResult<T>

    /**
     * 带重试的安全API调用
     * @param maxRetries 最大重试次数
     * @param delayMs 重试延迟
     * @param call API调用函数
     * @return 安全包装的结果
     */
    suspend fun <T> executeWithRetry(
        maxRetries: Int = 3,
        delayMs: Long = 1000L,
        call: suspend () -> T
    ): ApiResult<T>
}
```

### 使用示例

```kotlin
@Inject
lateinit var restClient: RestClient

@Inject
lateinit var safeApiCall: SafeApiCall

// 安全的REST调用
val result = safeApiCall.execute {
    restClient.get("https://api.example.com/users/123")
}

when (result) {
    is ApiResult.Success -> println("数据: ${result.data}")
    is ApiResult.Error -> println("错误: ${result.error.message}")
}

// 流式调用
restClient.streamPost(
    url = "https://api.example.com/chat",
    body = """{"message": "Hello"}""",
    headers = mapOf("Content-Type" to "application/json")
).collect { chunk ->
    println("接收: $chunk")
}
```

## 📊 网络状态监控接口

### NetworkStateMonitor

**职责**: 提供网络状态监控服务

```kotlin
interface NetworkStateMonitor {
    /**
     * 当前网络状态
     * @return 网络状态流
     */
    val networkState: StateFlow<NetworkState>

    /**
     * 网络可用性
     * @return 是否可用的流
     */
    val isNetworkAvailable: Flow<Boolean>

    /**
     * 网络类型
     * @return 网络类型流
     */
    val networkType: Flow<NetworkType>

    /**
     * 连接质量
     * @return 连接质量流
     */
    val connectionQuality: Flow<ConnectionQuality>

    /**
     * 开始监控
     */
    suspend fun startMonitoring()

    /**
     * 停止监控
     */
    suspend fun stopMonitoring()

    /**
     * 刷新网络状态
     */
    suspend fun refreshNetworkState()
}
```

### NetworkWatchdog

**职责**: 网络事件监控和通知

```kotlin
interface NetworkWatchdog {
    /**
     * 网络事件流
     * @return 网络事件的Flow（带防抖处理）
     */
    val networkEvents: Flow<NetworkEvent>

    /**
     * 当前网络状态
     * @return 当前网络状态
     */
    val currentNetworkState: StateFlow<NetworkState>

    /**
     * 刷新网络状态
     */
    suspend fun refreshNetworkState()

    /**
     * 获取网络统计信息
     * @return 网络统计
     */
    fun getNetworkStats(): NetworkStats
}
```

**事件类型**: `NetworkEvent.Connected`, `NetworkEvent.Disconnected`, `NetworkEvent.QualityChanged`

### 使用示例

```kotlin
@Inject
lateinit var networkWatchdog: NetworkWatchdog

// 监听网络事件
networkWatchdog.networkEvents
    .collect { event ->
        when (event) {
            is NetworkEvent.Connected -> {
                println("网络已连接: ${event.type}")
                // 恢复网络操作
            }
            is NetworkEvent.Disconnected -> {
                println("网络断开: ${event.reason}")
                // 暂停网络操作
            }
            is NetworkEvent.QualityChanged -> {
                println("网络质量变化: ${event.quality}")
                // 调整传输策略
            }
        }
    }
```

## 🔧 配置管理接口

### NetworkConfigManager

**职责**: 网络配置管理

```kotlin
interface NetworkConfigManager {
    /**
     * 获取当前配置
     * @return 当前网络配置
     */
    fun getCurrentConfig(): NetworkConfig

    /**
     * 更新配置
     * @param config 新的配置
     */
    suspend fun updateConfig(config: NetworkConfig)

    /**
     * 监听配置变化
     * @return 配置变化流
     */
    fun observeConfig(): Flow<NetworkConfig>

    /**
     * 验证配置
     * @param config 要验证的配置
     * @return 验证结果
     */
    suspend fun validateConfig(config: NetworkConfig): ConfigValidationResult

    /**
     * 重置为默认配置
     */
    suspend fun resetToDefault()
}
```

### NetworkConfig

**职责**: 网络配置数据类

```kotlin
data class NetworkConfig(
    val restBaseUrl: String,
    val apiKey: String,
    val connectTimeoutMs: Long = 30000,
    val readTimeoutMs: Long = 30000,
    val writeTimeoutMs: Long = 30000,
    val maxRetries: Int = 3,
    val retryDelayMs: Long = 1000,
    val enableLogging: Boolean = false,
    val bufferSize: Int = 8192,
    val enableMetrics: Boolean = true
) {
    /**
     * 验证配置有效性
     * @return 是否有效
     */
    fun isValid(): Boolean {
        return restBaseUrl.isNotBlank() && 
               apiKey.isNotBlank() &&
               connectTimeoutMs > 0 &&
               readTimeoutMs > 0 &&
               writeTimeoutMs > 0 &&
               maxRetries >= 0 &&
               retryDelayMs > 0 &&
               bufferSize > 0
    }
}
```

## 📊 API结果封装

### ApiResult<T>

**职责**: 统一的API调用结果封装，支持函数式操作

```kotlin
sealed interface ApiResult<out T> {
    /**
     * 成功结果
     */
    data class Success<T>(val data: T) : ApiResult<T>

    /**
     * 错误结果
     */
    data class Error(val error: ApiError) : ApiResult<Nothing>

    // 函数式操作
    fun <R> map(transform: (T) -> R): ApiResult<R>
    fun <R> flatMap(transform: (T) -> ApiResult<R>): ApiResult<R>
    fun <R> fold(onSuccess: (T) -> R, onError: (ApiError) -> R): R
    
    // 便捷属性
    val isSuccess: Boolean
    val isError: Boolean
    fun getOrNull(): T?
    fun getOrThrow(): T
}
```

### ApiError

**职责**: 统一的API错误类型定义

```kotlin
sealed interface ApiError {
    val message: String
    val cause: Throwable?

    /**
     * 网络错误
     */
    data class Network(
        override val message: String,
        override val cause: Throwable? = null
    ) : ApiError

    /**
     * HTTP错误
     */
    data class Http(
        val code: Int,
        override val message: String,
        override val cause: Throwable? = null
    ) : ApiError

    /**
     * 离线错误
     */
    data class Offline(
        override val message: String = "Network is offline",
        override val cause: Throwable? = null
    ) : ApiError

    /**
     * 认证错误
     */
    data class Auth(
        override val message: String,
        override val cause: Throwable? = null
    ) : ApiError

    /**
     * 解析错误
     */
    data class Parse(
        override val message: String,
        override val cause: Throwable? = null
    ) : ApiError

    /**
     * 缓冲错误
     */
    data class Buffer(
        override val message: String,
        override val cause: Throwable? = null
    ) : ApiError

    /**
     * 协议错误
     */
    data class Protocol(
        override val message: String,
        override val cause: Throwable? = null
    ) : ApiError

    /**
     * 未知错误
     */
    data class Unknown(
        override val message: String,
        override val cause: Throwable? = null
    ) : ApiError
}
```

## 🔄 状态和事件类型

### ConnectionState

**职责**: 连接状态枚举

```kotlin
enum class ConnectionState {
    IDLE,           // 空闲状态
    CONNECTING,     // 连接中
    CONNECTED,      // 已连接
    PROCESSING,     // 处理中
    DISCONNECTING,  // 断开连接中
    DISCONNECTED,   // 已断开
    ERROR;          // 错误状态

    fun isActive(): Boolean = this in setOf(CONNECTING, CONNECTED, PROCESSING)
    fun canReconnect(): Boolean = this in setOf(DISCONNECTED, ERROR, IDLE)
}
```

### NetworkEvent

**职责**: 网络状态变化事件

```kotlin
sealed interface NetworkEvent {
    val timestamp: Long

    data class Connected(
        val type: NetworkType,
        override val timestamp: Long = System.currentTimeMillis()
    ) : NetworkEvent

    data class Disconnected(
        val reason: DisconnectReason,
        override val timestamp: Long = System.currentTimeMillis()
    ) : NetworkEvent

    data class QualityChanged(
        val quality: ConnectionQuality,
        override val timestamp: Long = System.currentTimeMillis()
    ) : NetworkEvent

    data class ProtocolChanged(
        val protocol: NetworkProtocol,
        override val timestamp: Long = System.currentTimeMillis()
    ) : NetworkEvent
}
```

## 🧪 测试接口

### 测试工具接口

```kotlin
// 网络模拟工具
interface NetworkTestUtils {
    fun createMockNetworkConfig(): NetworkConfig
    fun simulateNetworkDelay(delayMs: Long)
    fun simulateNetworkError(error: ApiError)
}

// 缓冲测试工具
interface BufferTestUtils {
    fun createTestBuffer(size: Int): SlidingWindowBuffer<String>
    fun fillBufferWithTestData(buffer: SlidingWindowBuffer<String>, count: Int)
}

// 流处理测试工具
interface StreamTestUtils {
    fun createTestTokenFlow(tokens: List<String>): Flow<String>
    fun createTestEventFlow(events: List<Any>): Flow<Any>
}
```

## 📋 接口契约验证

### 单元测试要求

1. **接口实现完整性**: 所有接口方法必须有对应实现
2. **错误处理**: 异常情况必须正确处理和封装
3. **状态一致性**: 状态变化必须符合状态机定义
4. **资源管理**: 缓冲区和连接必须正确释放
5. **性能指标**: 处理延迟和吞吐量验证

### 集成测试要求

1. **端到端流程**: 完整的数据流处理测试
2. **缓冲性能**: 不同负载下的缓冲效果验证
3. **协议检测**: 多种网络环境下的检测准确性
4. **错误恢复**: 异常情况下的自动恢复能力
5. **并发安全**: 多线程环境下的线程安全验证

---

**Core Network模块的简化接口设计专注于高性能、低延迟和智能适应，提供清晰、一致、可测试的API契约。**