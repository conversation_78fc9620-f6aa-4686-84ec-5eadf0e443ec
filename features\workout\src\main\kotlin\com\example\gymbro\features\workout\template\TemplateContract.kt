package com.example.gymbro.features.workout.template

import androidx.compose.runtime.Immutable
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.workout.model.TemplateDraft
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.shared.models.workout.TemplateExerciseDto
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto

/**
 * =========================================================================================
 * 🔥 GymBro Template Contract - 遵循黄金标准 🔥
 * =========================================================================================
 *
 * 本Contract遵循ProfileBio黄金标准，专注于核心功能。
 *
 * 🎯 核心功能：
 * 1. 模板列表管理 (加载、删除、搜索)
 * 2. 基础UI状态管理 (Tab切换、错误处理)
 * 3. 导航Effect处理 (通过EffectHandler执行)
 * 4. 职责明确：主Screen只负责列表管理，EditScreen负责详细编辑
 *
 * ✅ 已优化：
 * - 遵循@Immutable State设计
 * - Intent使用动词命名，Result后缀用于内部Intent
 * - Effect使用动词命名，描述一次性事件
 */
object TemplateContract {

    /**
     * State - UI状态快照
     *
     * 规范：
     * - 必须使用 @Immutable 注解
     * - 所有属性都是 `val`
     * - 包含数据状态、UI瞬时状态（如加载）和错误状态
     * - 使用派生属性（get()）来计算衍生值，避免在UI层计算
     */
    @Immutable
    data class State(
        // === 核心数据 ===
        val templates: List<WorkoutTemplate> = emptyList(),
        val drafts: List<TemplateDraft> = emptyList(),

        // === UI状态 ===
        val currentTab: TemplateTab = TemplateTab.TEMPLATES,
        val isLoading: Boolean = false,
        val isLoadingDrafts: Boolean = false,
        val isDeleting: Boolean = false,

        // === 搜索和筛选 ===
        val searchQuery: String = "",
        val filteredTemplates: List<WorkoutTemplate> = emptyList(),
        val filteredDrafts: List<TemplateDraft> = emptyList(),
        val showSearchField: Boolean = false,

        // === 错误处理 ===
        val error: UiText? = null,
    ) : UiState {
        // 派生属性
        val hasTemplates: Boolean get() = templates.isNotEmpty()
        val hasDrafts: Boolean get() = drafts.isNotEmpty()
        val isSearching: Boolean get() = searchQuery.isNotBlank()
    }

    /**
     * Intent - 用户意图
     *
     * 规范：
     * - 动词命名，清晰表达用户或系统的意图
     * - `...Result` 后缀用于从异步操作返回的内部Intent
     */
    sealed interface Intent : AppIntent {
        // === 数据加载 ===
        object LoadTemplates : Intent
        object RefreshTemplates : Intent
        object LoadDrafts : Intent
        object RefreshDrafts : Intent

        // === 模板操作 ===
        object NavigateToCreateTemplate : Intent
        data class NavigateToEditTemplate(val templateId: String) : Intent
        data class DeleteTemplate(val templateId: String) : Intent

        // === 搜索和筛选 ===
        data class UpdateSearchQuery(val query: String) : Intent
        object ToggleSearch : Intent
        object ClearSearch : Intent

        // === UI状态管理 ===
        data class SwitchTab(val tab: TemplateTab) : Intent
        object ClearError : Intent

        // === 结果处理 (内部Intent) ===
        data class TemplatesLoaded(val templates: List<WorkoutTemplate>) : Intent
        data class DraftsLoaded(val drafts: List<TemplateDraft>) : Intent
        data class TemplateDeleted(val templateId: String) : Intent
        data class LoadError(val error: UiText) : Intent
    }

    /**
     * Effect - 副作用
     *
     * 规范：
     * - 动词命名，描述一个需要执行的、一次性的事件
     * - 不包含任何执行逻辑，仅为数据载体
     * - 由ViewModel发出，UI层监听并执行
     */
    sealed interface Effect : UiEffect {
        // === 数据加载副作用 ===
        object LoadTemplatesData : Effect
        object RefreshTemplatesData : Effect
        object LoadDraftsData : Effect
        object RefreshDraftsData : Effect

        // === 导航副作用 ===
        object NavigateToCreateTemplate : Effect
        data class NavigateToEditTemplate(val templateId: String) : Effect

        // === 数据操作副作用 ===
        data class DeleteTemplate(val templateId: String) : Effect

        // === UI反馈副作用 ===
        data class ShowToast(val message: UiText) : Effect
        data class ShowError(val error: UiText) : Effect
    }

    // === 辅助枚举 ===

    enum class TemplateTab {
        TEMPLATES,
        DRAFTS
    }
}
