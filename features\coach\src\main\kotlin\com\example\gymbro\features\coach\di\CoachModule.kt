package com.example.gymbro.features.coach.di

import com.example.gymbro.core.di.qualifiers.DefaultDispatcher
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.ml.embedding.BgeEmbeddingEngine
import com.example.gymbro.data.coach.dao.ChatSearchDao
import com.example.gymbro.domain.autosave.ChatHistoryAutoSaveService
import com.example.gymbro.domain.coach.repository.ChatRepository
import com.example.gymbro.domain.coach.usecase.GetMessageByIdUseCase
import com.example.gymbro.features.coach.history.internal.manager.ArchitectureHealthChecker
import com.example.gymbro.domain.coach.history.HistoryFeatureManager
// 移除RAG相关导入 - 不属于History模块职责
import com.example.gymbro.features.coach.shared.services.HybridSearchUseCase
import com.example.gymbro.features.coach.shared.services.SmartSearchService

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Singleton

/**
 * Coach模块依赖注入配置 - v5.0-GOLD + Phase 1 BGE搜索集成
 *
 * 作为纯消费模块，Coach模块的DI配置专注于：
 * 1. 聚合其他模块的API服务
 * 2. 确保MVI架构组件的正确注入
 * 3. 遵循依赖倒置原则
 * 4. 提供BGE向量搜索相关服务
 *
 * 设计原则：
 * - 只注入来自其他模块API的服务
 * - 不暴露任何内部实现给外部
 * - 使用现有的domain层UseCase，不重复创建
 * - 新增搜索服务遵循Clean Architecture边界
 *
 * Phase 1 新增：
 * - HybridSearchUseCase：混合搜索核心逻辑
 * - SmartSearchService：智能搜索服务
 *
 * @since v5.0-GOLD - 纯消费模块设计
 * @since Phase 1 - BGE向量搜索集成
 */
@Module
@InstallIn(SingletonComponent::class)
internal object CoachModule {
    // ==================== Phase 1: BGE向量搜索服务 ====================

    /**
     * 提供混合搜索UseCase
     *
     * 依赖来自其他模块：
     * - ChatSearchDao: data模块提供
     * - EmbeddingEngine: core-ml模块提供
     */
    @Provides
    @Singleton
    fun provideHybridSearchUseCase(
        chatSearchDao: ChatSearchDao,
        vectorSearchService: com.example.gymbro.features.coach.shared.services.VectorSearchService,
        @IoDispatcher ioDispatcher: CoroutineDispatcher,
        @DefaultDispatcher defaultDispatcher: CoroutineDispatcher,
    ): HybridSearchUseCase =
        HybridSearchUseCase(
            chatSearchDao = chatSearchDao,
            vectorSearchService = vectorSearchService,
            ioDispatcher = ioDispatcher,
            defaultDispatcher = defaultDispatcher,
        )

    /**
     * 提供向量搜索服务
     *
     * 基于BGE模型的向量搜索服务
     */
    @Provides
    @Singleton
    fun provideVectorSearchService(
        chatSearchDao: ChatSearchDao,
        embeddingEngine: BgeEmbeddingEngine,
        @DefaultDispatcher defaultDispatcher: CoroutineDispatcher,
    ): com.example.gymbro.features.coach.shared.services.VectorSearchService =
        com.example.gymbro.features.coach.shared.services.VectorSearchService(
            chatSearchDao = chatSearchDao,
            embeddingEngine = embeddingEngine,
            defaultDispatcher = defaultDispatcher,
        )

    /**
     * 提供智能搜索服务
     *
     * 统一的搜索入口，集成多种搜索策略
     */
    @Provides
    @Singleton
    fun provideSmartSearchService(
        hybridSearchUseCase: HybridSearchUseCase,
        chatSearchDao: ChatSearchDao,
        @IoDispatcher ioDispatcher: CoroutineDispatcher,
        @DefaultDispatcher defaultDispatcher: CoroutineDispatcher,
    ): SmartSearchService =
        SmartSearchService(
            hybridSearchUseCase = hybridSearchUseCase,
            chatSearchDao = chatSearchDao,
            ioDispatcher = ioDispatcher,
            defaultDispatcher = defaultDispatcher,
        )

    // ==================== History功能增强组件 ====================

    /**
     * 提供History功能管理器
     *
     * 实现四级功能降级机制，确保系统可靠性
     */
    @Provides
    @Singleton
    fun provideHistoryFeatureManager(): HistoryFeatureManager = HistoryFeatureManager()

    /**
     * 提供架构健康检查器
     *
     * 监控History管道各组件健康状况
     */
    @Provides
    @Singleton
    fun provideArchitectureHealthChecker(
        chatHistoryAutoSaveService: ChatHistoryAutoSaveService,
        bgeEmbeddingEngine: BgeEmbeddingEngine,
        // 移除RAG相关依赖 - 不属于History模块职责
    ): ArchitectureHealthChecker =
        ArchitectureHealthChecker(
            chatHistoryAutoSaveService = chatHistoryAutoSaveService,
            bgeEmbeddingEngine = bgeEmbeddingEngine,
            // 移除RAG相关依赖
        )

    /**
     * 提供根据消息ID获取单个消息的用例
     *
     * 基于 history-todo-plan.md 的单个消息获取逻辑要求
     */
    @Provides
    @Singleton
    fun provideGetMessageByIdUseCase(
        chatRepository: ChatRepository,
        @IoDispatcher ioDispatcher: CoroutineDispatcher,
    ): GetMessageByIdUseCase =
        GetMessageByIdUseCase(
            chatRepository = chatRepository,
            ioDispatcher = ioDispatcher,
            logger =
            com.example.gymbro.core.logging
                .TimberLogger(),
        )

    // ==================== 🔥 Coach-ThinkingBox重构：集成服务 ====================

    /**
     * 提供Coach-ThinkingBox集成初始化器
     *
     * 用于在运行时将ThinkingBoxDisplay注入到AiCoachEffectHandler中
     */
    @Provides
    @Singleton
    fun provideCoachThinkingBoxInitializer(
        thinkingBoxDisplay: com.example.gymbro.features.thinkingbox.api.ThinkingBoxDisplay
    ): com.example.gymbro.features.coach.aicoach.internal.initializer.CoachThinkingBoxInitializer =
        com.example.gymbro.features.coach.aicoach.internal.initializer.CoachThinkingBoxInitializer(
            thinkingBoxDisplay = thinkingBoxDisplay
        )
}
