package com.example.gymbro.core.network.buffer

import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

/**
 * 🚀 SlidingWindowBuffer 单元测试
 *
 * 测试目标：
 * - 验证滑动窗口机制
 * - 测试批量处理逻辑
 * - 验证过期数据清理
 * - 测试背压控制
 */
class SlidingWindowBufferTest {

    private lateinit var buffer: SlidingWindowBuffer<String>

    @BeforeEach
    fun setup() {
        buffer = SlidingWindowBuffer(
            windowSize = 10,
            slideStep = 3,
            maxRetentionMs = 1000L
        )
    }

    @Test
    fun `初始状态应该为空`() {
        // When & Then
        assertTrue(buffer.isEmpty())
        assertEquals(0, buffer.size())
        assertFalse(buffer.isNearFull())
    }

    @Test
    fun `添加项目直到窗口满时应该触发滑动`() {
        // Given
        val items = (1..10).map { "item$it" }

        // When - 添加项目直到窗口满
        val results = mutableListOf<List<String>>()
        items.forEach { item ->
            val result = buffer.add(item)
            if (result.isNotEmpty()) {
                results.add(result)
            }
        }

        // Then
        assertTrue(results.isNotEmpty(), "窗口满时应该触发滑动")
        val firstSlide = results.first()
        assertEquals(3, firstSlide.size, "滑动步长应该是3")
        assertEquals(listOf("item1", "item2", "item3"), firstSlide)
    }

    @Test
    fun `滑动窗口应该按FIFO顺序处理`() {
        // Given
        val items = (1..15).map { "item$it" }

        // When
        val allResults = mutableListOf<String>()
        items.forEach { item ->
            val result = buffer.add(item)
            allResults.addAll(result)
        }

        // Then
        val expectedOrder = listOf("item1", "item2", "item3", "item4", "item5", "item6")
        assertEquals(expectedOrder, allResults.take(6))
    }

    @Test
    fun `isNearFull应该正确检测接近满载状态`() {
        // Given
        val threshold = 0.8f // 80%
        val nearFullCount = (10 * threshold).toInt() // 8个项目

        // When - 添加到接近满载
        repeat(nearFullCount) { i ->
            buffer.add("item${i + 1}")
        }

        // Then
        assertTrue(buffer.isNearFull(threshold), "应该检测到接近满载状态")

        // When - 再添加一个
        buffer.add("item${nearFullCount + 1}")

        // Then
        assertTrue(buffer.isNearFull(threshold), "仍然应该是接近满载状态")
    }

    @Test
    fun `flush应该返回所有剩余项目并清空缓冲区`() {
        // Given
        val items = listOf("item1", "item2", "item3", "item4", "item5")
        items.forEach { buffer.add(it) }

        // When
        val flushed = buffer.flush()

        // Then
        assertEquals(items, flushed)
        assertTrue(buffer.isEmpty())
        assertEquals(0, buffer.size())
    }

    @Test
    fun `clear应该清空缓冲区`() {
        // Given
        repeat(5) { buffer.add("item${it + 1}") }
        assertFalse(buffer.isEmpty())

        // When
        buffer.clear()

        // Then
        assertTrue(buffer.isEmpty())
        assertEquals(0, buffer.size())
    }

    @Test
    fun `getStatus应该返回正确的状态信息`() {
        // Given
        val items = listOf("item1", "item2", "item3")
        items.forEach { buffer.add(it) }

        // When
        val status = buffer.getStatus()

        // Then
        assertEquals(3, status.currentSize)
        assertEquals(10, status.windowSize)
        assertEquals(3, status.slideStep)
        assertEquals(0, status.processedCount) // 还没有滑动
        assertEquals(0, status.droppedCount)
        assertTrue(status.oldestItemAgeMs >= 0)
        assertEquals(0.3f, status.utilizationPercent, 0.01f)
    }

    @Test
    fun `过期项目应该被自动清理`() {
        // Given
        val shortRetentionBuffer = SlidingWindowBuffer<String>(
            windowSize = 10,
            slideStep = 3,
            maxRetentionMs = 100L // 100ms保留时间
        )

        // When - 添加项目然后等待过期
        shortRetentionBuffer.add("item1")
        shortRetentionBuffer.add("item2")

        Thread.sleep(150) // 等待超过保留时间

        shortRetentionBuffer.add("item3") // 触发清理

        // Then
        val status = shortRetentionBuffer.getStatus()
        assertTrue(status.droppedCount > 0, "过期项目应该被清理")
    }

    @Test
    fun `长时间间隔应该增加滑动步长`() {
        // Given - 使用更大的保留时间避免时序问题
        val longRetentionBuffer = SlidingWindowBuffer<String>(
            windowSize = 10,
            slideStep = 2,
            maxRetentionMs = 10000L // 10秒保留时间，避免意外触发
        )

        // When - 添加项目填满窗口
        repeat(10) { i ->
            longRetentionBuffer.add("item${i + 1}")
        }

        // 再添加一个项目触发正常滑动
        val result = longRetentionBuffer.add("item11")

        // Then - 正常情况下应该滑动slideStep个项目（可能因时间间隔而增加）
        assertTrue(result.size >= 2, "滑动应该移除至少2个项目，实际移除${result.size}个")

        // 验证状态
        val status = longRetentionBuffer.getStatus()
        assertTrue(status.processedCount >= 2, "应该处理了至少2个项目")
        assertTrue(longRetentionBuffer.size() <= 9, "滑动后应该有不超过9个项目，实际${longRetentionBuffer.size()}个")
    }

    @Test
    fun `空缓冲区flush应该返回空列表`() {
        // When
        val result = buffer.flush()

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `连续滑动应该保持正确的处理计数`() {
        // Given
        val items = (1..20).map { "item$it" }

        // When
        var totalProcessed = 0
        items.forEach { item ->
            val result = buffer.add(item)
            totalProcessed += result.size
        }

        // Then
        val status = buffer.getStatus()
        assertEquals(totalProcessed.toLong(), status.processedCount)
    }

    @Test
    fun `利用率计算应该正确`() {
        // Given
        val windowSize = 10
        val testBuffer = SlidingWindowBuffer<String>(
            windowSize = windowSize,
            slideStep = 3,
            maxRetentionMs = 1000L
        )

        // When - 添加一半的项目
        repeat(5) { i ->
            testBuffer.add("item${i + 1}")
        }

        // Then
        val status = testBuffer.getStatus()
        assertEquals(0.5f, status.utilizationPercent, 0.01f)
    }

    @Test
    fun `SlidingWindowBufferFactory应该创建正确配置的缓冲区`() {
        // Given
        val factory = SlidingWindowBufferFactory()

        // When
        val tokenBuffer = factory.createTokenBuffer()
        val eventBuffer = factory.createEventBuffer()
        val customBuffer = factory.createCustomBuffer<String>(
            windowSize = 20,
            slideStep = 5,
            maxRetentionMs = 2000L
        )

        // Then
        assertNotNull(tokenBuffer)
        assertNotNull(eventBuffer)
        assertNotNull(customBuffer)

        // 验证自定义缓冲区配置
        val customStatus = customBuffer.getStatus()
        assertEquals(20, customStatus.windowSize)
        assertEquals(5, customStatus.slideStep)
    }

    @Test
    fun `工厂方法应该验证参数`() {
        // Given
        val factory = SlidingWindowBufferFactory()

        // When & Then
        assertThrows(IllegalArgumentException::class.java) {
            factory.createCustomBuffer<String>(0, 1, 1000L) // windowSize = 0
        }

        assertThrows(IllegalArgumentException::class.java) {
            factory.createCustomBuffer<String>(10, 0, 1000L) // slideStep = 0
        }

        assertThrows(IllegalArgumentException::class.java) {
            factory.createCustomBuffer<String>(10, 15, 1000L) // slideStep > windowSize
        }

        assertThrows(IllegalArgumentException::class.java) {
            factory.createCustomBuffer<String>(10, 5, 0L) // maxRetentionMs = 0
        }
    }
}
