12:15:51.461 Choreographer            I  Skipped 73 frames!  The application may be doing too much work on its main thread.
12:15:51.475 HWUI                     I  Davey! duration=2051ms; Flags=0, FrameTimelineVsyncId=1221676, IntendedVsync=3402241538726, Vsync=3402341538722, InputEventId=513690503, HandleInputStart=3402527365300, AnimationStart=3402527391800, PerformTraversalsStart=3402965311400, DrawStart=3402965406300, FrameDeadline=3402258205392, FrameStartTime=3402347454600, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=3404280641000, SyncStart=3404280802500, IssueDrawCommandsStart=3404281039400, SwapBuffers=3404282198800, FrameCompleted=3404293189200, DequeueBufferDuration=24500, QueueBufferDuration=183500, GpuCompleted=3404293189200, Swap<PERSON><PERSON>ersCompleted=3404283005700, DisplayPresentTime=0, CommandSubmissionCompleted=3404282198800,
12:15:51.567 .example.gymbro          W  Cleared Reference was only reachable from finalizer (only reported once)
12:15:51.709                          I  Background concurrent mark compact GC freed 21MB AllocSpace bytes, 4(88KB) LOS objects, 19% free, 97MB/121MB, paused 2.588ms,10.935ms total 424.301ms
12:15:53.674 Choreographer            I  Skipped 55 frames!  The application may be doing too much work on its main thread.
12:15:53.952 HWUI                     I  Davey! duration=1121ms; Flags=0, FrameTimelineVsyncId=1221918, IntendedVsync=3405491538596, Vsync=3405524871928, InputEventId=873071980, HandleInputStart=3405539382000, AnimationStart=3405539405300, PerformTraversalsStart=3405585835700, DrawStart=3405585939900, FrameDeadline=3405508205262, FrameStartTime=3405539367100, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=3406495842100, SyncStart=3406498210700, IssueDrawCommandsStart=3406502790200, SwapBuffers=3406519302800, FrameCompleted=3406615791400, DequeueBufferDuration=25500, QueueBufferDuration=250400, GpuCompleted=3406615791400, SwapBuffersCompleted=3406520528600, DisplayPresentTime=0, CommandSubmissionCompleted=3406519302800,
12:15:54.844                          I  Davey! duration=1199ms; Flags=0, FrameTimelineVsyncId=1221926, IntendedVsync=3405574871926, Vsync=3406491538556, InputEventId=0, HandleInputStart=3406503075300, AnimationStart=3406503100400, PerformTraversalsStart=3406509605700, DrawStart=3406509675100, FrameDeadline=3406641538550, FrameStartTime=3406502468600, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=3406515452100, SyncStart=3406520959100, IssueDrawCommandsStart=3406521150700, SwapBuffers=3406522197400, FrameCompleted=3406779924600, DequeueBufferDuration=236125900, QueueBufferDuration=286100, GpuCompleted=3406778602300, SwapBuffersCompleted=3406779924600, DisplayPresentTime=0, CommandSubmissionCompleted=3406522197400,
12:15:54.861 Choreographer            I  Skipped 63 frames!  The application may be doing too much work on its main thread.
12:15:54.894 HWUI                     I  Davey! duration=914ms; Flags=2, FrameTimelineVsyncId=1221948, IntendedVsync=3406774871878, Vsync=3406774871878, InputEventId=0, HandleInputStart=3406774871878, AnimationStart=3406774871878, PerformTraversalsStart=3406774871878, DrawStart=3406774871878, FrameDeadline=3406558205220, FrameStartTime=3406774871878, FrameInterval=16666666, WorkloadTarget=-233333324, SyncQueued=3406780612400, SyncStart=3406780623500, IssueDrawCommandsStart=3406780739100, SwapBuffers=3407437744200, FrameCompleted=3407688885900, DequeueBufferDuration=163500, QueueBufferDuration=1078700, GpuCompleted=3407688885900, SwapBuffersCompleted=3407670967400, DisplayPresentTime=0, CommandSubmissionCompleted=3407437744200,
12:15:54.909                          I  Davey! duration=887ms; Flags=0, FrameTimelineVsyncId=1221941, IntendedVsync=3406524871888, Vsync=3406758205212, InputEventId=0, HandleInputStart=3406765196400, AnimationStart=3406765242900, PerformTraversalsStart=3407354170000, DrawStart=3407354305200, FrameDeadline=3407708205174, FrameStartTime=3406764771000, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=3407357005300, SyncStart=3407673621100, IssueDrawCommandsStart=3407673915600, SwapBuffers=3407697114300, FrameCompleted=3407728696700, DequeueBufferDuration=287200, QueueBufferDuration=311600, GpuCompleted=3407728696700, SwapBuffersCompleted=3407720595600, DisplayPresentTime=0, CommandSubmissionCompleted=3407697114300,
12:15:55.152                          I  Davey! duration=1341ms; Flags=0, FrameTimelineVsyncId=1221963, IntendedVsync=3406624871884, Vsync=3407674871842, InputEventId=0, HandleInputStart=3407694898400, AnimationStart=3407694939600, PerformTraversalsStart=3407735366800, DrawStart=3407735458300, FrameDeadline=3407758205172, FrameStartTime=3407689484600, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=3407940970900, SyncStart=3407941274500, IssueDrawCommandsStart=3407941496600, SwapBuffers=3407962876300, FrameCompleted=3407966379400, DequeueBufferDuration=26100, QueueBufferDuration=375400, GpuCompleted=3407966379400, SwapBuffersCompleted=3407963975700, DisplayPresentTime=0, CommandSubmissionCompleted=3407962876300,
12:15:57.458 CRITICAL-SAVE            I  🔥 [CRITICAL-PUBLISH] handlePublishTemplate 被调用，模板='训练模版'
12:15:57.458                          I  🔥 [CRITICAL-PUBLISH] 动作数=1
12:15:57.458                          I  🔥 [CRITICAL-PUBLISH] 动作1: 窄距杠铃卧推, customSets=3
12:15:57.459                          I  🔥 [CRITICAL-PUBLISH] 组1: weight=0.0, reps=10, rest=60s
12:15:57.459                          I  🔥 [CRITICAL-PUBLISH] 组2: weight=0.0, reps=10, rest=60s
12:15:57.459                          I  🔥 [CRITICAL-PUBLISH] 组3: weight=0.0, reps=10, rest=60s
12:15:57.540 WK-CORE                  I  🔥 [WK-SAVE-START] 训练模版 - 发布模板
12:15:57.541                          I  🔥 [WK-SAVE-PROCESS] 训练模版 - 调用发布处理器
12:15:57.541                          I  🔥 [WK-SAVE-VALIDATE] 训练模版 - 验证保存参数和状态
12:15:57.542                          I  🔥 [WK-SAVE-CANCEL] 训练模版 - 取消待处理的自动保存
12:15:57.734 TimberLogger             W  模板不存在: 8c293cc7-c253-463d-bd13-30e8844b4813
12:15:57.756 Choreographer            I  Skipped 34 frames!  The application may be doing too much work on its main thread.
12:15:58.091 TemplateEditSaveHandler  I  🔍 [TEMPLATE-SAVE] 数据库中未找到有效模板数据，判定为新建
12:15:58.091 WK-SAVE-DEBUG            I  🔥 [SAVE-OPERATION] 保存操作参数:
12:15:58.091                          I  🔥 [SAVE-OPERATION] isDraft=false
12:15:58.091                          I  🔥 [SAVE-OPERATION] isPublishing=true
12:15:58.091                          I  🔥 [SAVE-OPERATION] operationType=CREATE
12:15:58.091                          I  🔥 [SAVE-OPERATION] template.id=8c293cc7-c253-463d-bd13-30e8844b4813
12:15:58.091                          I  🔥 [SAVE-OPERATION] template.isDraft=true
12:15:58.093                          I  🔥 [SAVE-OPERATION] template.isPublished=false
12:15:58.095 WK-CORE                  I  🔥 [WK-SAVE-DETERMINE] 训练模版 - 操作类型: CREATE
12:15:58.095                          I  🔥 [WK-SAVE-CREATE-MODE] 训练模版 - 新建模板操作
12:15:58.095                          I  🔥 [WK-SAVE-NAME-FINAL] 训练模版 - 确定最终模板名称
12:15:58.095                          I  🔥 [WK-SAVE-BUILD] 训练模版 - 构建Domain模型
12:15:58.095                          I  🔥 [MAPPER-START] 训练模版 (1动作)
12:15:58.095 WK-DB                    I  🔥 [P0-ID-PRESERVE] 编辑现有模板，保持原始ID: 8c293cc7-c253-463d-bd13-30e8844b4813
12:15:58.095                          I  🔥 [P0-ID-FINAL] 模板ID确定: 原始='8c293cc7-c253-463d-bd13-30e8844b4813' -> 最终='8c293cc7-c253-463d-bd13-30e8844b4813' (保持原ID)
12:15:58.096                          I  🔥 [P0-EXERCISE-1] 处理动作: 窄距杠铃卧推, customSets=3
12:15:58.096                          I  🔥 [P0-SET-DATA] 动作1-组1: weight=0.0, reps=10, rest=60s
12:15:58.096                          I  🔥 [P0-SET-DATA] 动作1-组2: weight=0.0, reps=10, rest=60s
12:15:58.097                          I  🔥 [P0-SET-DATA] 动作1-组3: weight=0.0, reps=10, rest=60s
12:15:58.097 WK-CRITICAL              I  🔥 [P0-IMAGE-DATA] 动作1: imageUrl=https://example.com/default_exercise.jpg, videoUrl=https://example.com/default_exercise.mp4
12:15:58.122 HWUI                     I  Davey! duration=928ms; Flags=0, FrameTimelineVsyncId=1222220, IntendedVsync=3410008205082, Vsync=3410574871726, InputEventId=1042510316, HandleInputStart=3410813173500, AnimationStart=3410813272400, PerformTraversalsStart=3410906985200, DrawStart=3410907082200, FrameDeadline=3410024871748, FrameStartTime=3410584592200, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=3410917647700, SyncStart=3410918065300, IssueDrawCommandsStart=3410918212200, SwapBuffers=3410928218500, FrameCompleted=3410937172000, DequeueBufferDuration=30800, QueueBufferDuration=258800, GpuCompleted=3410937172000, SwapBuffersCompleted=3410931902500, DisplayPresentTime=0, CommandSubmissionCompleted=3410928218500,
12:15:58.151 WK-SAVE-DEBUG            I  🔥 [TEMPLATE-STATUS] 构建完成的模板状态:
12:15:58.151                          I  🔥 [TEMPLATE-STATUS] isDraft=false (参数)
12:15:58.151                          I  🔥 [TEMPLATE-STATUS] isPublishing=true (参数)
12:15:58.151                          I  🔥 [TEMPLATE-STATUS] template.isDraft=false (最终)
12:15:58.151                          I  🔥 [TEMPLATE-STATUS] template.isPublished=true (最终)
12:15:58.151                          I  🔥 [TEMPLATE-STATUS] template.name=训练模版
12:15:58.152                          I  🔥 [TEMPLATE-STATUS] template.id=8c293cc7-c253-463d-bd13-30e8844b4813
12:15:58.152 WK-CORE                  I  🔥 [WK-SAVE-EXECUTE] 训练模版 - 执行保存操作
12:15:58.152                          I  🔥 [WK-SAVE-TRANSACTION] 训练模版 - 开始事务保存
12:15:58.159                          I  🔥 [WK-SAVE-TX-START] 训练模版 - 开始事务保存 - ID: tx_1754021758157_8966
12:15:58.159                          I  🔥 [WK-SAVE-TX-VALIDATE] 训练模版 - 开始预验证阶段
12:15:58.159                          I  🔥 [WK-SAVE-TX-VALIDATE-OK] 训练模版 - 预验证通过
12:15:58.159                          I  🔥 [WK-SAVE-TX-PREPARE] 训练模版 - 数据准备阶段
12:15:58.159                          I  🔥 [WK-SAVE-TX-JSON-CHECK] 训练模版 - JSON兼容性验证
12:15:58.196                          I  🔥 [WK-SAVE-TX-FC-CHECK] 训练模版 - Function Call兼容性验证
12:15:58.222                          I  🔥 [WK-SAVE-TX-FC-OK] 训练模版 - Function Call兼容性验证通过
12:15:58.223                          I  🔥 [WK-SAVE-TX-ATOMIC] 训练模版 - 执行原子性保存
12:15:58.275                          I  🔥 [WK-SAVE-TX-SUCCESS] 训练模版 - 事务保存成功 - 模板ID: 8c293cc7-c253-463d-bd13-30e8844b4813
12:15:58.283                          I  🔥 [WK-SAVE-COMPLETE] 训练模版 - 保存成功 - ID: 8c293cc7-c253-463d-bd13-30e8844b4813
12:15:58.283 CRITICAL-AUTOSAVE        I  🔥 [P3-AUTOSAVE] 手动保存完成，设置冷却期: 5000ms
12:15:58.283 WK-CORE                  I  🔥 [WK-SAVE-NOTIFY] 训练模版 - 通知自动保存管理器
12:15:58.284                          I  🔥 [WK-SAVE-SUCCESS] 训练模版 - 模板发布成功 - ID: 8c293cc7-c253-463d-bd13-30e8844b4813
12:15:58.284                          I  🔥 保存成功回调: templateId=8c293cc7-c253-463d-bd13-30e8844b4813
12:15:58.291 WK-MAPPER                I  🔥 [PHASE1-NEW] mapDtoToState 开始 - 单向映射
12:15:58.291                          I  🔥 [PHASE1-NEW] 模板=训练模版, 动作数=1
12:15:58.291                          I  🔥 [PHASE1-NEW] 处理动作: 窄距杠铃卧推, customSets=3
12:15:58.292                          I  🔥 [PHASE1-NEW] 组1: weight=0.0, reps=10, rest=60s
12:15:58.293                          I  🔥 [PHASE1-NEW] 组2: weight=0.0, reps=10, rest=60s
12:15:58.293                          I  🔥 [PHASE1-NEW] 组3: weight=0.0, reps=10, rest=60s
12:16:00.060 DisplayEventDispatcher   W  Vsync time out! vsyncScheduleDelay=622ms
12:16:00.831 Choreographer            I  Skipped 78 frames!  The application may be doing too much work on its main thread.
12:16:01.548 HWUI                     I  Davey! duration=1409ms; Flags=2, FrameTimelineVsyncId=1222820, IntendedVsync=3412258204992, Vsync=3412258204992, InputEventId=0, HandleInputStart=3412258204992, AnimationStart=3412258204992, PerformTraversalsStart=3412258204992, DrawStart=3412258204992, FrameDeadline=3411624871684, FrameStartTime=3412258204992, FrameInterval=16666666, WorkloadTarget=-633333308, SyncQueued=3412266387100, SyncStart=3412266399800, IssueDrawCommandsStart=3412266535500, SwapBuffers=3412424055500, FrameCompleted=3413667254900, DequeueBufferDuration=26300, QueueBufferDuration=2737000, GpuCompleted=3413667254900, SwapBuffersCompleted=3412888537700, DisplayPresentTime=0, CommandSubmissionCompleted=3412424055500,
12:16:01.790 Choreographer            I  Skipped 56 frames!  The application may be doing too much work on its main thread.
12:16:01.811 TimberLogger             I  TemplateViewModel
12:16:01.816 HWUI                     I  Davey! duration=2143ms; Flags=0, FrameTimelineVsyncId=1222849, IntendedVsync=3412358204988, Vsync=3413658204936, InputEventId=795551822, HandleInputStart=3413782792100, AnimationStart=3413782820000, PerformTraversalsStart=3413853574500, DrawStart=3413853689000, FrameDeadline=3413691538268, FrameStartTime=3413658856500, FrameInterval=16666666, WorkloadTarget=16666666, SyncQueued=3414486471000, SyncStart=3414610734100, IssueDrawCommandsStart=3414611069600, SwapBuffers=3414612201300, FrameCompleted=3414626455900, DequeueBufferDuration=32700, QueueBufferDuration=372000, GpuCompleted=3414626455900, SwapBuffersCompleted=3414616852700, DisplayPresentTime=0, CommandSubmissionCompleted=3414612201300,
12:16:01.816 TimberLogger             I  TemplateViewModel
12:16:02.124                          I  TemplateViewModel
12:16:02.129                          I  TemplateViewModel
